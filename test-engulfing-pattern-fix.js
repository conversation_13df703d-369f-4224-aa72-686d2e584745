/**
 * Test script để verify fix cho Engulfing Pattern
 * <PERSON><PERSON><PERSON> bảo sử dụng 2 nến đã đóng thay vì nến hiện tại đang chạy
 */

// Mock config
global.config = {
  trading: {
    indicators: {
      ema: { fast: 50, slow: 200 },
      macd: { fast: 12, slow: 26, signal: 9 },
      rsi: { period: 14 }
    }
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

async function testEngulfingPatternFix() {
  console.log('🧪 Testing Engulfing Pattern Fix\\n');
  console.log('===============================\\n');

  try {
    // Load indicators
    const Indicators = require('./lib/trading/indicators');

    console.log('📊 Test Scenarios:');
    console.log('==================');
    console.log('1. Bullish Engulfing: prev2 (đỏ) + prev1 (xanh nuốt chửng)');
    console.log('2. Bearish Engulfing: prev2 (xanh) + prev1 (đỏ nuốt chửng)');
    console.log('3. No Engulfing: Normal candles');
    console.log('4. Current candle running: Should not affect pattern detection');

    // Test Case 1: Bullish Engulfing Pattern
    console.log('\\n🎯 Test 1: Bullish Engulfing Pattern');
    console.log('====================================');

    const bullishEngulfingCandles = [
      // Nến cũ hơn (không ảnh hưởng)
      { open: 100, high: 105, low: 95, close: 102, openTime: 1000 },
      { open: 102, high: 108, low: 98, close: 104, openTime: 2000 },

      // prev2: Nến đỏ (bearish) - đã đóng
      { open: 104, high: 106, low: 100, close: 101, openTime: 3000 }, // Đỏ: open > close

      // prev1: Nến xanh nuốt chửng (bullish) - đã đóng
      { open: 100, high: 108, low: 99, close: 107, openTime: 4000 }, // Xanh: open < close, nuốt chửng nến đỏ

      // current: Nến hiện tại đang chạy (không dùng cho pattern)
      { open: 107, high: 109, low: 106, close: 108, openTime: 5000 } // Đang chạy
    ];

    console.log('Candle data:');
    console.log('prev2 (đỏ):', {
      open: bullishEngulfingCandles[2].open,
      close: bullishEngulfingCandles[2].close,
      type: 'bearish (open > close)'
    });
    console.log('prev1 (xanh):', {
      open: bullishEngulfingCandles[3].open,
      close: bullishEngulfingCandles[3].close,
      type: 'bullish (open < close)',
      engulfs: 'prev2 completely'
    });
    console.log('current (running):', {
      open: bullishEngulfingCandles[4].open,
      close: bullishEngulfingCandles[4].close,
      note: 'NOT USED for pattern detection'
    });

    const bullishResult = Indicators.checkEngulfingPattern(bullishEngulfingCandles);
    console.log('\\nPattern result:', bullishResult);
    console.log('Expected: bullish');
    console.log('✅ Correct:', bullishResult === 'bullish' ? 'YES' : 'NO');

    // Test Case 2: Bearish Engulfing Pattern
    console.log('\\n🎯 Test 2: Bearish Engulfing Pattern');
    console.log('====================================');

    const bearishEngulfingCandles = [
      // Nến cũ hơn
      { open: 100, high: 105, low: 95, close: 102, openTime: 1000 },
      { open: 102, high: 108, low: 98, close: 104, openTime: 2000 },

      // prev2: Nến xanh (bullish) - đã đóng
      { open: 104, high: 110, low: 103, close: 109, openTime: 3000 }, // Xanh: open < close

      // prev1: Nến đỏ nuốt chửng (bearish) - đã đóng
      { open: 110, high: 112, low: 102, close: 103, openTime: 4000 }, // Đỏ: open > close, nuốt chửng nến xanh

      // current: Nến hiện tại đang chạy
      { open: 103, high: 105, low: 101, close: 102, openTime: 5000 }
    ];

    console.log('Candle data:');
    console.log('prev2 (xanh):', {
      open: bearishEngulfingCandles[2].open,
      close: bearishEngulfingCandles[2].close,
      type: 'bullish (open < close)'
    });
    console.log('prev1 (đỏ):', {
      open: bearishEngulfingCandles[3].open,
      close: bearishEngulfingCandles[3].close,
      type: 'bearish (open > close)',
      engulfs: 'prev2 completely'
    });

    const bearishResult = Indicators.checkEngulfingPattern(bearishEngulfingCandles);
    console.log('\\nPattern result:', bearishResult);
    console.log('Expected: bearish');
    console.log('✅ Correct:', bearishResult === 'bearish' ? 'YES' : 'NO');

    // Test Case 3: No Engulfing Pattern
    console.log('\\n🎯 Test 3: No Engulfing Pattern');
    console.log('================================');

    const noEngulfingCandles = [
      { open: 100, high: 105, low: 95, close: 102, openTime: 1000 },
      { open: 102, high: 108, low: 98, close: 104, openTime: 2000 },

      // prev2: Nến xanh nhỏ
      { open: 104, high: 106, low: 103, close: 105, openTime: 3000 },

      // prev1: Nến xanh nhỏ (không nuốt chửng)
      { open: 105, high: 107, low: 104, close: 106, openTime: 4000 },

      // current: Nến hiện tại
      { open: 106, high: 108, low: 105, close: 107, openTime: 5000 }
    ];

    const noEngulfingResult = Indicators.checkEngulfingPattern(noEngulfingCandles);
    console.log('Pattern result:', noEngulfingResult);
    console.log('Expected: none');
    console.log('✅ Correct:', noEngulfingResult === 'none' ? 'YES' : 'NO');

    // Test Case 4: Insufficient Candles
    console.log('\\n🎯 Test 4: Insufficient Candles (< 3)');
    console.log('======================================');

    const insufficientCandles = [
      { open: 100, high: 105, low: 95, close: 102, openTime: 1000 },
      { open: 102, high: 108, low: 98, close: 104, openTime: 2000 }
    ];

    const insufficientResult = Indicators.checkEngulfingPattern(insufficientCandles);
    console.log('Pattern result:', insufficientResult);
    console.log('Expected: none (insufficient data)');
    console.log('✅ Correct:', insufficientResult === 'none' ? 'YES' : 'NO');

    // Test Case 5: Edge Case - Current Candle Changes
    console.log('\\n🎯 Test 5: Current Candle Changes (Should Not Affect)');
    console.log('====================================================');

    const baseCandles = [
      { open: 100, high: 105, low: 95, close: 102, openTime: 1000 },
      { open: 102, high: 108, low: 98, close: 104, openTime: 2000 },
      { open: 104, high: 106, low: 100, close: 101, openTime: 3000 }, // prev2: đỏ
      { open: 100, high: 108, low: 99, close: 107, openTime: 4000 }   // prev1: xanh nuốt chửng
    ];

    // Test với current candle khác nhau
    const currentCandle1 = { open: 107, high: 109, low: 106, close: 108, openTime: 5000 };
    const currentCandle2 = { open: 107, high: 120, low: 90, close: 95, openTime: 5000 }; // Rất khác

    const result1 = Indicators.checkEngulfingPattern([...baseCandles, currentCandle1]);
    const result2 = Indicators.checkEngulfingPattern([...baseCandles, currentCandle2]);

    console.log('Result with normal current candle:', result1);
    console.log('Result with extreme current candle:', result2);
    console.log('Should be same:', result1 === result2 ? '✅ YES' : '❌ NO');
    console.log('Both should be bullish:', (result1 === 'bullish' && result2 === 'bullish') ? '✅ YES' : '❌ NO');

    console.log('\\n🎉 Test Summary');
    console.log('================');
    console.log('✅ Engulfing Pattern Fix implemented successfully:');
    console.log('   - Uses 2 completed candles (prev2 and prev1)');
    console.log('   - Ignores current running candle');
    console.log('   - Accurate pattern detection');
    console.log('   - Consistent results regardless of current candle');

    console.log('\\n📊 Before vs After Fix:');
    console.log('========================');
    console.log('❌ Before: checkEngulfingPattern(candles)');
    console.log('   - prev = candles[length-2] (completed)');
    console.log('   - current = candles[length-1] (running) ← WRONG!');
    console.log('   - Pattern detection unreliable');

    console.log('\\n✅ After: checkEngulfingPattern(candles)');
    console.log('   - prev2 = candles[length-3] (completed)');
    console.log('   - prev1 = candles[length-2] (completed)');
    console.log('   - current = candles[length-1] (ignored) ← CORRECT!');
    console.log('   - Pattern detection reliable');

    console.log('\\n📋 Real-world Impact:');
    console.log('=====================');
    console.log('- ✅ Accurate engulfing pattern detection');
    console.log('- ✅ No false signals from running candles');
    console.log('- ✅ Consistent signal generation');
    console.log('- ✅ Better trading signal quality');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testEngulfingPatternFix().catch(console.error);
}

module.exports = { testEngulfingPatternFix };