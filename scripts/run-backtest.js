// Sử dụng global imports như trong index.js
global._ = require('lodash');
global.config = require('../config/default.json');
global.Logger = require('../lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.logger = Logger(`${__dirname}/../logs`);
global.moment = require('moment');

const BacktestEngine = require('../lib/backtest/BacktestEngine');

async function runBacktest() {
    console.log('=== BACKTEST EXECUTION ===');

    try {
        // Backtest configuration
        const backtestConfig = {
            // Time Range
            startDate: '2024-08-01',
            endDate: '2024-10-31',

            // Assets
            symbols: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
            timeframes: ['5m'],

            // Capital Management
            initialCapital: 10000,
            positionSizePercent: 2, // 2% per trade
            maxConcurrentPositions: 3,

            // Strategy Settings
            useMarketConditionFilter: true,
            trailingStopEnabled: false, // Simplified for now

            // Execution Settings
            slippage: 0.1, // 0.1% slippage
            commission: 0.1, // 0.1% commission
            latency: 100, // 100ms execution delay

            // Risk Management
            maxDrawdown: 20, // 20% max drawdown
            dailyLossLimit: 5 // 5% daily loss limit
        };

        console.log('📊 Backtest Configuration:');
        console.log(`   Period: ${backtestConfig.startDate} to ${backtestConfig.endDate}`);
        console.log(`   Symbols: ${backtestConfig.symbols.join(', ')}`);
        console.log(`   Initial Capital: $${backtestConfig.initialCapital}`);
        console.log(`   Position Size: ${backtestConfig.positionSizePercent}%`);
        console.log(`   Max Positions: ${backtestConfig.maxConcurrentPositions}`);
        console.log(`   Market Condition Filter: ${backtestConfig.useMarketConditionFilter ? 'Enabled' : 'Disabled'}`);

        // Initialize backtest engine
        console.log('\n🚀 Initializing backtest engine...');
        const backtestEngine = new BacktestEngine(backtestConfig);

        // Monitor progress
        const progressInterval = setInterval(() => {
            const progress = backtestEngine.getProgress();
            if (progress.isRunning) {
                const elapsed = (progress.elapsedTime / 1000).toFixed(1);
                console.log(`   Progress: ${progress.progress.toFixed(1)}% (${elapsed}s elapsed)`);
            }
        }, 5000);

        // Run backtest
        console.log('\n⚡ Starting backtest execution...');
        const startTime = Date.now();

        const results = await backtestEngine.runBacktest();

        clearInterval(progressInterval);

        const executionTime = (Date.now() - startTime) / 1000;
        console.log(`\n✅ Backtest completed in ${executionTime.toFixed(2)} seconds`);

        // Display results
        console.log('\n📊 BACKTEST RESULTS:');
        console.log('=' .repeat(50));

        // Basic metrics
        console.log(`\n💰 Performance Summary:`);
        console.log(`   Initial Capital: $${results.analysis.initialCapital?.toFixed(2) || backtestConfig.initialCapital}`);
        console.log(`   Final Capital: $${results.analysis.currentCapital?.toFixed(2) || 'N/A'}`);
        console.log(`   Total Return: ${results.analysis.totalReturn?.toFixed(2) || 'N/A'}%`);
        console.log(`   Total P&L: $${results.analysis.totalPnL?.toFixed(2) || 'N/A'}`);

        // Trading metrics
        console.log(`\n📈 Trading Statistics:`);
        console.log(`   Total Signals: ${results.signals}`);
        console.log(`   Total Trades: ${results.trades}`);
        console.log(`   Win Rate: ${results.analysis.winRate?.toFixed(2) || 'N/A'}%`);
        console.log(`   Winning Trades: ${results.analysis.winningTrades || 'N/A'}`);
        console.log(`   Losing Trades: ${results.analysis.losingTrades || 'N/A'}`);

        // Risk metrics
        console.log(`\n⚠️  Risk Metrics:`);
        console.log(`   Max Drawdown: ${results.analysis.maxDrawdown?.toFixed(2) || 'N/A'}%`);
        console.log(`   Profit Factor: ${results.analysis.profitFactor?.toFixed(2) || 'N/A'}`);
        console.log(`   Sharpe Ratio: ${results.analysis.sharpeRatio?.toFixed(2) || 'N/A'}`);
        console.log(`   Average Win: $${results.analysis.avgWin?.toFixed(2) || 'N/A'}`);
        console.log(`   Average Loss: $${results.analysis.avgLoss?.toFixed(2) || 'N/A'}`);

        // Data summary
        console.log(`\n📊 Data Summary:`);
        Object.keys(results.historicalData).forEach(symbol => {
            Object.keys(results.historicalData[symbol]).forEach(timeframe => {
                const data = results.historicalData[symbol][timeframe];
                console.log(`   ${symbol} ${timeframe}: ${data.candleCount} candles`);
            });
        });

        // Save results to file
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const resultsFile = `backtest-results-${timestamp}.json`;
        const resultsPath = `./data/${resultsFile}`;

        // Ensure data directory exists
        const fs = require('fs');
        const path = require('path');
        const dataDir = path.dirname(resultsPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
        console.log(`\n💾 Results saved to: ${resultsPath}`);

        // Performance analysis
        console.log(`\n🎯 Performance Analysis:`);
        if (results.analysis.totalReturn > 0) {
            console.log(`   ✅ Profitable strategy (+${results.analysis.totalReturn.toFixed(2)}%)`);
        } else {
            console.log(`   ❌ Losing strategy (${results.analysis.totalReturn.toFixed(2)}%)`);
        }

        if (results.analysis.winRate > 60) {
            console.log(`   ✅ Good win rate (${results.analysis.winRate.toFixed(2)}%)`);
        } else if (results.analysis.winRate > 50) {
            console.log(`   ⚠️  Moderate win rate (${results.analysis.winRate.toFixed(2)}%)`);
        } else {
            console.log(`   ❌ Low win rate (${results.analysis.winRate.toFixed(2)}%)`);
        }

        if (results.analysis.maxDrawdown < 10) {
            console.log(`   ✅ Low drawdown (${results.analysis.maxDrawdown.toFixed(2)}%)`);
        } else if (results.analysis.maxDrawdown < 20) {
            console.log(`   ⚠️  Moderate drawdown (${results.analysis.maxDrawdown.toFixed(2)}%)`);
        } else {
            console.log(`   ❌ High drawdown (${results.analysis.maxDrawdown.toFixed(2)}%)`);
        }

        if (results.analysis.profitFactor > 1.5) {
            console.log(`   ✅ Good profit factor (${results.analysis.profitFactor.toFixed(2)})`);
        } else if (results.analysis.profitFactor > 1.0) {
            console.log(`   ⚠️  Moderate profit factor (${results.analysis.profitFactor.toFixed(2)})`);
        } else {
            console.log(`   ❌ Poor profit factor (${results.analysis.profitFactor.toFixed(2)})`);
        }

        console.log(`\n🎉 Backtest analysis completed!`);
        console.log(`📁 Detailed results available in: ${resultsPath}`);

    } catch (error) {
        console.error('❌ Backtest failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Run backtest
runBacktest().then(() => {
    console.log('\n=== BACKTEST COMPLETED ===');
}).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});