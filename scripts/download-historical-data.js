// Sử dụng global imports như trong index.js
global._ = require('lodash');
global.config = require('../config/default.json');
global.Logger = require('../lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.logger = Logger(`${__dirname}/../logs`);

const DataManager = require('../lib/backtest/DataManager');

async function downloadHistoricalData() {
    console.log('=== HISTORICAL DATA DOWNLOAD ===');

    try {
        const symbols = [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOGEUSDT', 'SOLUSDT',
            'BNBUSDT', 'XRPUSDT', 'LTCUSDT', 'AVAXUSDT', 'DOTUSDT'
        ];

        const timeframes = ['5m', '15m', '1h'];
        const startDate = '2024-01-01'; // 12 months of data
        const endDate = '2025-11-01';

        console.log(`Downloading data for ${symbols.length} symbols`);
        console.log(`Timeframes: ${timeframes.join(', ')}`);
        console.log(`Period: ${startDate} to ${endDate}`);

        let totalDownloaded = 0;
        let totalSkipped = 0;

        for (const symbol of symbols) {
            console.log(`\n📊 Processing ${symbol}...`);

            for (const timeframe of timeframes) {
                try {
                    // Check if data already exists
                    if (DataManager.hasData(symbol, timeframe, 'raw')) {
                        const dataInfo = DataManager.getDataInfo(symbol, timeframe, 'raw');
                        console.log(`   ⏭️  ${timeframe} data exists (${dataInfo.sizeKB}KB, modified: ${dataInfo.modified.toLocaleDateString()})`);
                        totalSkipped++;
                        continue;
                    }

                    console.log(`   ⬇️  Downloading ${timeframe} data...`);

                    // Download data
                    const candles = await DataManager.downloadDataRange(
                        symbol,
                        timeframe,
                        startDate,
                        endDate
                    );

                    if (candles.length === 0) {
                        console.log(`   ❌ No data received for ${symbol} ${timeframe}`);
                        continue;
                    }

                    // Validate data quality
                    const validation = DataManager.validateData(candles);
                    console.log(`   📊 Data quality: ${validation.dataQuality} (${validation.validPercentage}% valid)`);

                    if (validation.errors.length > 0) {
                        console.log(`   ⚠️  Quality issues: ${validation.errors.slice(0, 3).join(', ')}`);
                    }

                    // Save data
                    await DataManager.saveData(symbol, timeframe, candles, 'raw');
                    totalDownloaded++;

                    console.log(`   ✅ Downloaded ${candles.length} candles for ${symbol} ${timeframe}`);

                    // Rate limiting delay
                    await sleep(200);

                } catch (error) {
                    console.log(`   ❌ Error downloading ${symbol} ${timeframe}: ${error.message}`);

                    // If rate limited, wait longer
                    if (error.message.includes('429') || error.message.includes('418')) {
                        console.log('   ⏸️  Rate limited, waiting 60 seconds...');
                        await sleep(60000);
                    }
                }
            }
        }

        console.log(`\n📈 Download Summary:`);
        console.log(`   Downloaded: ${totalDownloaded} datasets`);
        console.log(`   Skipped: ${totalSkipped} datasets`);
        console.log(`   Total: ${totalDownloaded + totalSkipped} datasets`);

        // List all available data
        console.log(`\n📁 Available Data Files:`);
        const availableData = DataManager.listAvailableData();

        availableData.forEach((file, index) => {
            console.log(`   ${index + 1}. ${file.symbol}_${file.interval} (${file.sizeKB}KB) - ${file.modified.toLocaleDateString()}`);
        });

        console.log(`\n✅ Historical data download completed!`);

    } catch (error) {
        console.error('Download failed:', error);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Run download
downloadHistoricalData().then(() => {
    console.log('\n=== DOWNLOAD COMPLETED ===');
}).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});