#!/bin/bash

# ScalpWizard Bot Startup Script
# Tự động setup và khởi động bot

echo "🚀 ScalpWizard Bot - Startup Script"
echo "=================================="

# Kiểm tra Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js chưa được cài đặt!"
    echo "Vui lòng cài đặt Node.js >= 16.0.0"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version quá cũ: $(node -v)"
    echo "Vui lòng cập nhật lên >= 16.0.0"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Kiểm tra npm packages
if [ ! -d "node_modules" ]; then
    echo "📦 Cài đặt dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Lỗi cài đặt dependencies!"
        exit 1
    fi
fi

# Kiểm tra file .env
if [ ! -f ".env" ]; then
    echo "⚙️ Tạo file .env từ template..."
    cp .env.example .env
    echo "📝 Vui lòng chỉnh sửa file .env với thông tin của bạn:"
    echo "   - TELEGRAM_BOT_TOKEN"
    echo "   - TELEGRAM_CHAT_ID"
    echo "   - Database settings"
    echo ""
    read -p "Nhấn Enter sau khi đã cấu hình .env..."
fi

# Kiểm tra MongoDB
echo "🔍 Kiểm tra kết nối MongoDB..."
MONGO_HOST=$(grep MONGO_HOST .env | cut -d'=' -f2)
MONGO_PORT=$(grep MONGO_PORT .env | cut -d'=' -f2)

if [ -z "$MONGO_HOST" ] || [ -z "$MONGO_PORT" ]; then
    echo "⚠️ Sử dụng MongoDB từ config/default.json"
else
    echo "📊 MongoDB: $MONGO_HOST:$MONGO_PORT"
fi

# Tạo thư mục logs nếu chưa có
if [ ! -d "logs" ]; then
    mkdir logs
    echo "📁 Tạo thư mục logs"
fi

# Chọn chế độ chạy
echo ""
echo "🎯 Chọn chế độ chạy:"
echo "1. Development (với nodemon)"
echo "2. Production (node)"
echo "3. PM2 (background process)"
echo "4. Test bot trước"
echo ""
read -p "Nhập lựa chọn (1-4): " choice

case $choice in
    1)
        echo "🔧 Khởi động Development mode..."
        npm run dev
        ;;
    2)
        echo "🚀 Khởi động Production mode..."
        npm start
        ;;
    3)
        echo "⚙️ Khởi động với PM2..."
        if ! command -v pm2 &> /dev/null; then
            echo "📦 Cài đặt PM2..."
            npm install -g pm2
        fi
        npm run pm2:start
        echo "✅ Bot đang chạy background với PM2"
        echo "📊 Xem logs: npm run pm2:logs"
        echo "🛑 Dừng bot: npm run pm2:stop"
        ;;
    4)
        echo "🧪 Chạy test bot..."
        npm run test-bot
        echo ""
        read -p "Test xong, nhấn Enter để tiếp tục khởi động bot..."
        echo "🚀 Khởi động bot..."
        npm start
        ;;
    *)
        echo "❌ Lựa chọn không hợp lệ!"
        exit 1
        ;;
esac
