#!/usr/bin/env node

/**
 * Test với điều kiện đã được nới lỏng
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testRelaxedConditions() {
  console.log('🔄 Testing Relaxed Signal Conditions...\n');

  try {
    // <PERSON>ết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    console.log('📊 New Configuration:');
    console.log('='.repeat(50));
    console.log(`RSI BUY Zone: ${config.trading.indicators.rsi.buyZone[0]}-${config.trading.indicators.rsi.buyZone[1]} (was 55-65)`);
    console.log(`RSI SELL Zone: ${config.trading.indicators.rsi.sellZone[0]}-${config.trading.indicators.rsi.sellZone[1]} (was 35-45)`);
    console.log('Engulfing Pattern: OPTIONAL (was REQUIRED)');
    console.log('Required Conditions: 3/4 (was 5/5)');

    // Test với symbols đã test trước đó
    const testCases = [
      { symbol: 'BNBUSDT', timeframe: '5m', note: 'Had MACD bullish crossover' },
      { symbol: 'BTCUSDT', timeframe: '1m', note: 'Good EMA alignment' },
      { symbol: 'ETHUSDT', timeframe: '5m', note: 'SELL conditions potential' }
    ];

    console.log('\n📊 Testing Previous Failed Cases:');
    console.log('='.repeat(60));

    let signalsFound = 0;

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing ${testCase.symbol} ${testCase.timeframe} (${testCase.note}):`);
      console.log('-'.repeat(50));

      try {
        const klines = await binanceClient.getKlines(testCase.symbol, testCase.timeframe, 1000);
        
        if (klines.length >= 220) {
          const indicatorData = indicators.calculateAllIndicators(klines);
          
          if (indicatorData) {
            console.log(`📊 Indicators:`);
            console.log(`   💰 Entry: ${indicatorData.currentPrice}`);
            console.log(`   📈 EMA50: ${indicatorData.ema50.toFixed(6)}`);
            console.log(`   📈 EMA200: ${indicatorData.ema200.toFixed(6)}`);
            console.log(`   📊 RSI: ${indicatorData.rsi?.toFixed(2) || 'N/A'}`);

            const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);
            console.log(`   🔄 MACD Crossover: ${macdCrossover}`);

            // Test BUY conditions
            const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);
            console.log(`\n📋 BUY Conditions (need 3/4):`);
            console.log(`   ✅ Price > EMA200: ${buyConditions.conditions.priceAboveEMA200}`);
            console.log(`   ✅ EMA50 > EMA200: ${buyConditions.conditions.emaAlignment}`);
            console.log(`   ✅ MACD Crossover: ${buyConditions.conditions.macdCrossover}`);
            console.log(`   ✅ RSI Zone (50-70): ${buyConditions.conditions.rsiZone}`);
            console.log(`   🎁 Engulfing (bonus): ${buyConditions.conditions.engulfing}`);
            console.log(`   🎯 Valid BUY: ${buyConditions.isValid}`);

            // Test SELL conditions
            const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);
            console.log(`\n📋 SELL Conditions (need 3/4):`);
            console.log(`   ✅ Price < EMA200: ${sellConditions.conditions.priceBelowEMA200}`);
            console.log(`   ✅ EMA50 < EMA200: ${sellConditions.conditions.emaAlignment}`);
            console.log(`   ✅ MACD Crossover: ${sellConditions.conditions.macdCrossover}`);
            console.log(`   ✅ RSI Zone (30-50): ${sellConditions.conditions.rsiZone}`);
            console.log(`   🎁 Engulfing (bonus): ${sellConditions.conditions.engulfing}`);
            console.log(`   🎯 Valid SELL: ${sellConditions.isValid}`);

            // Test signal generation
            const signal = await signalAnalyzer.analyzeSignal(testCase.symbol, testCase.timeframe, klines);
            
            if (signal) {
              signalsFound++;
              console.log(`\n🚀 SIGNAL GENERATED!`);
              console.log(`   📊 Type: ${signal.type}`);
              console.log(`   💰 Entry: ${signal.entry}`);
              console.log(`   🛑 Stop Loss: ${signal.stopLoss}`);
              console.log(`   🎯 Take Profit: ${signal.takeProfit}`);
              console.log(`   ⚖️ Risk/Reward: 1:${signal.riskReward}`);
            } else {
              console.log(`\n❌ Still no signal generated`);
            }
          }
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }
    }

    // Test với top volume coins
    console.log('\n\n🎯 Testing Top Volume Coins with New Conditions:');
    console.log('='.repeat(60));

    const topCoins = await binanceClient.getTop24hVolumeCoins(15);
    console.log(`📊 Testing ${topCoins.length} top volume coins...`);

    let totalSignals = 0;
    const foundSignals = [];

    for (const symbol of topCoins) {
      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        if (klines.length >= 220) {
          const signal = await signalAnalyzer.analyzeSignal(symbol, '5m', klines);
          if (signal) {
            totalSignals++;
            foundSignals.push({
              symbol,
              type: signal.type,
              entry: signal.entry,
              riskReward: signal.riskReward
            });
            console.log(`🚀 ${symbol}: ${signal.type} at ${signal.entry} (RR: 1:${signal.riskReward})`);
          }
        }
      } catch (error) {
        // Skip individual errors
      }
    }

    console.log(`\n📊 Results Summary:`);
    console.log(`Total signals found: ${totalSignals} out of ${topCoins.length} symbols`);
    console.log(`Success rate: ${((totalSignals/topCoins.length)*100).toFixed(1)}%`);

    if (foundSignals.length > 0) {
      console.log(`\n🎉 Found Signals:`);
      foundSignals.forEach((signal, i) => {
        console.log(`${i+1}. ${signal.symbol}: ${signal.type} at ${signal.entry} (RR: 1:${signal.riskReward})`);
      });
    } else {
      console.log(`\n⚠️ Still no signals found. Consider further relaxing conditions.`);
    }

    console.log('\n✅ Relaxed conditions test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testRelaxedConditions().catch(console.error);
}

module.exports = testRelaxedConditions;
