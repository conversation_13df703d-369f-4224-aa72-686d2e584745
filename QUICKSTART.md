# 🚀 ScalpWizard - Hướng Dẫn Khởi Động Nhanh

## ⚡ Khởi Động Trong 5 Phút

### Bước 1: <PERSON><PERSON>n Bị Telegram Bot
1. Mở Telegram, tìm `@BotFather`
2. G<PERSON>i `/newbot` và làm theo hướng dẫn
3. <PERSON><PERSON>u **Bot Token** (dạng: `123456789:ABCdef...`)
4. Thêm bot vào nhóm và lấy **Chat ID**

### Bước 2: Cài Đặt Bot
```bash
# Clone repository
git clone <repository-url>
cd ScalpWizard

# Chạy script tự động
./start.sh
```

### Bước 3: Cấu Hình
Chỉnh sửa file `.env`:
```env
TELEGRAM_BOT_TOKEN=**********:AAF1suBw2mPQJ6JQXOER128O6XgDR4kO1lc
TELEGRAM_CHAT_ID=-1003287098255
```

### Bước 4: Khởi Động
```bash
npm start
```

## 🎯 Kiểm Tra Bot Hoạt Động

### 1. Health Check
```bash
curl http://localhost:6886/health
```

### 2. Trading Status
```bash
curl http://localhost:6886/api/v1/trading/status
```

### 3. Xem Logs
```bash
tail -f logs/system-*.log
```

## 📱 Thông Báo Telegram

Bot sẽ gửi các loại thông báo:

### Tín Hiệu Trading
```
🚀 TÍNH HIỆU TRADING 🚀

📊 Cặp: BTCUSDT
⏰ Thời gian: 28/10/2024 10:30:15
📈 Loại lệnh: 📈 MUA
💰 Entry: 43250.500000
🛑 Stop Loss: 43035.000000
🎯 Take Profit: 43682.000000

📋 Chỉ báo:
📊 EMA50: 43200.000000
📊 EMA200: 43000.000000
📊 MACD: 0.500000
📊 Signal: 0.300000
📊 RSI: 62.00
📊 Pattern: 🟢 Bullish Engulfing

#ScalpWizard #BTCUSDT
```

### Kết Quả Lệnh
```
📊 KẾT QUẢ LỆNH 📊

📊 Cặp: BTCUSDT
⏰ Thời gian đóng: 28/10/2024 11:15:30
📈 Loại lệnh: 📈 MUA
💰 Entry: 43250.500000
🏁 Exit: 43682.000000
📊 Kết quả: 🎯 WIN
💵 P&L: *****%

📈 Thống kê tổng:
✅ Win: 95
❌ Loss: 55
📊 Win Rate: 63.3%

#ScalpWizard #BTCUSDT
```

## ⚙️ Cấu Hình Nhanh

### Thay Đổi Timeframes
File `config/default.json`:
```json
{
  "trading": {
    "timeframes": ["1m", "5m", "15m", "1h"]
  }
}
```

### Thay Đổi Số Coins Theo Dõi
```json
{
  "trading": {
    "binance": {
      "maxCoinsToTrack": 50
    }
  }
}
```

### Thay Đổi Risk Management
```json
{
  "trading": {
    "riskManagement": {
      "stopLossPercent": 1.0,
      "takeProfitPercent": [2, 3]
    }
  }
}
```

## 🔧 Commands Hữu Ích

### Development
```bash
npm run dev          # Chạy với nodemon
npm run test-bot     # Test tất cả chức năng
```

### Production
```bash
npm start            # Chạy production
npm run pm2:start    # Chạy background với PM2
npm run pm2:logs     # Xem logs PM2
npm run pm2:stop     # Dừng PM2
```

### Monitoring
```bash
# Xem logs real-time
tail -f logs/system-*.log

# Xem chỉ signals
grep "Signal" logs/system-*.log

# Xem lỗi
grep "ERROR" logs/system-*.log

# Kiểm tra memory usage
ps aux | grep node
```

## 🚨 Troubleshooting Nhanh

### Bot Không Gửi Thông Báo
1. Kiểm tra Bot Token và Chat ID
2. Đảm bảo bot đã được `/start`
3. Kiểm tra logs: `grep "Telegram" logs/system-*.log`

### Không Có Tín Hiệu
1. Kiểm tra WebSocket connections
2. Xem market data: `curl localhost:6886/api/v1/trading/status`
3. Điều kiện tín hiệu rất nghiêm ngặt, có thể cần đợi

### High CPU/Memory
1. Giảm `maxCoinsToTrack` trong config
2. Tăng RAM server
3. Restart bot: `pm2 restart scalpwizard`

### MongoDB Errors
```bash
# Kiểm tra MongoDB
sudo systemctl status mongod

# Restart MongoDB
sudo systemctl restart mongod
```

## 📊 Monitoring Dashboard

### API Endpoints
- Health: `http://localhost:6886/health`
- Status: `http://localhost:6886/api/v1/trading/status`
- Statistics: `http://localhost:6886/api/v1/trading/statistics`
- Recent Signals: `http://localhost:6886/api/v1/trading/signals/recent`

### Log Files
- System: `logs/system-YYYY-MM-DD.log`
- Statistics: `logs/statistics-backup-*.json`
- Test Reports: `logs/test-report-*.json`

## 🎯 Tips Sử Dụng

### 1. Theo Dõi Performance
- Kiểm tra win rate hàng ngày
- Monitor top performing coins
- Adjust risk management theo market conditions

### 2. Backup Quan Trọng
```bash
# Backup MongoDB
mongodump --db wizard-management --out backup/

# Backup config
cp -r config/ backup/config/

# Backup logs
cp -r logs/ backup/logs/
```

### 3. Scaling Up
- Tăng `maxCoinsToTrack` từ từ
- Monitor server resources
- Sử dụng multiple timeframes

### 4. Security
- Không share Bot Token
- Sử dụng VPS riêng
- Regular backup
- Monitor unauthorized access

---

## 🆘 Cần Hỗ Trợ?

1. **Đọc logs**: `tail -f logs/system-*.log`
2. **Test bot**: `npm run test-bot`
3. **Check status**: `curl localhost:6886/api/v1/trading/status`
4. **Restart**: `pm2 restart scalpwizard`

**Bot sẽ tự động gửi thông báo lỗi qua Telegram nếu có vấn đề nghiêm trọng!**

---

**Happy Trading! 🚀📈**
