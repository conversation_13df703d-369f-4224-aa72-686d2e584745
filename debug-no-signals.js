#!/usr/bin/env node

/**
 * Debug tại sao không có signals được tạo
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function debugNoSignals() {
  console.log('🔍 Debugging Why No Signals Are Generated...\n');

  try {
    // <PERSON>ết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    // Test với một số symbols phổ biến
    const testSymbols = ['BTCUSDT', 'ETHUSDT', 'DOGEUSDT', 'BNBUSDT'];
    const timeframes = ['1m', '5m'];

    console.log('📊 Testing Signal Generation for Multiple Symbols:');
    console.log('='.repeat(70));

    for (const symbol of testSymbols) {
      for (const timeframe of timeframes) {
        console.log(`\n🔍 Testing ${symbol} ${timeframe}:`);
        console.log('-'.repeat(50));

        try {
          // 1. Lấy dữ liệu
          const klines = await binanceClient.getKlines(symbol, timeframe, 1000);
          console.log(`📊 Candles: ${klines.length}`);

          if (klines.length >= 220) {
            // 2. Tính indicators
            const indicatorData = indicators.calculateAllIndicators(klines);
            
            if (indicatorData) {
              console.log(`✅ Indicators calculated:`);
              console.log(`   💰 Entry: ${indicatorData.currentPrice}`);
              console.log(`   📈 EMA50: ${indicatorData.ema50.toFixed(6)}`);
              console.log(`   📈 EMA200: ${indicatorData.ema200.toFixed(6)}`);
              console.log(`   📊 MACD: ${indicatorData.macd?.macd?.toFixed(6) || 'N/A'}`);
              console.log(`   📊 Signal: ${indicatorData.macd?.signal?.toFixed(6) || 'N/A'}`);
              console.log(`   📊 RSI: ${indicatorData.rsi?.toFixed(2) || 'N/A'}`);
              console.log(`   🕯️ Pattern: ${indicatorData.engulfing}`);

              // 3. Kiểm tra MACD crossover
              const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);
              console.log(`   🔄 MACD Crossover: ${macdCrossover}`);

              // 4. Kiểm tra điều kiện BUY
              const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);
              console.log(`\n📋 BUY Conditions:`);
              console.log(`   ✅ Price > EMA200: ${buyConditions.conditions.priceAboveEMA200} (${indicatorData.currentPrice} > ${indicatorData.ema200})`);
              console.log(`   ✅ EMA50 > EMA200: ${buyConditions.conditions.emaAlignment} (${indicatorData.ema50} > ${indicatorData.ema200})`);
              console.log(`   ✅ MACD Crossover: ${buyConditions.conditions.macdCrossover} (${macdCrossover})`);
              console.log(`   ✅ RSI Zone: ${buyConditions.conditions.rsiZone} (RSI: ${indicatorData.rsi})`);
              console.log(`   ✅ Engulfing: ${buyConditions.conditions.engulfing} (${indicatorData.engulfing})`);
              console.log(`   🎯 Valid BUY Signal: ${buyConditions.isValid}`);

              // 5. Kiểm tra điều kiện SELL
              const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);
              console.log(`\n📋 SELL Conditions:`);
              console.log(`   ✅ Price < EMA200: ${sellConditions.conditions.priceBelowEMA200} (${indicatorData.currentPrice} < ${indicatorData.ema200})`);
              console.log(`   ✅ EMA50 < EMA200: ${sellConditions.conditions.emaAlignment} (${indicatorData.ema50} < ${indicatorData.ema200})`);
              console.log(`   ✅ MACD Crossover: ${sellConditions.conditions.macdCrossover} (${macdCrossover})`);
              console.log(`   ✅ RSI Zone: ${sellConditions.conditions.rsiZone} (RSI: ${indicatorData.rsi})`);
              console.log(`   ✅ Engulfing: ${sellConditions.conditions.engulfing} (${indicatorData.engulfing})`);
              console.log(`   🎯 Valid SELL Signal: ${sellConditions.isValid}`);

              // 6. Test signal analyzer
              const signal = await signalAnalyzer.analyzeSignal(symbol, timeframe, klines);
              
              if (signal) {
                console.log(`\n🚀 SIGNAL GENERATED:`);
                console.log(`   📊 Type: ${signal.type}`);
                console.log(`   💰 Entry: ${signal.entry}`);
                console.log(`   🛑 Stop Loss: ${signal.stopLoss}`);
                console.log(`   🎯 Take Profit: ${signal.takeProfit}`);
                console.log(`   ⚖️ Risk/Reward: 1:${signal.riskReward}`);
              } else {
                console.log(`\n❌ NO SIGNAL: Conditions not met`);
                
                // Phân tích tại sao không có signal
                console.log(`\n🔍 Analysis:`);
                if (!buyConditions.isValid && !sellConditions.isValid) {
                  console.log(`   ⚠️ Neither BUY nor SELL conditions are met`);
                  
                  // Kiểm tra từng điều kiện
                  if (macdCrossover === 'none') {
                    console.log(`   📊 Missing: MACD crossover signal`);
                  }
                  if (!buyConditions.conditions.priceAboveEMA200 && !sellConditions.conditions.priceBelowEMA200) {
                    console.log(`   📈 Price is between EMA50 and EMA200 (sideways trend)`);
                  }
                  if (indicatorData.engulfing === 'none') {
                    console.log(`   🕯️ Missing: Engulfing pattern`);
                  }
                }
              }

            } else {
              console.log(`❌ Failed to calculate indicators`);
            }
          } else {
            console.log(`❌ Not enough candles: ${klines.length}`);
          }

        } catch (error) {
          console.log(`❌ Error testing ${symbol} ${timeframe}: ${error.message}`);
        }
      }
    }

    // Test với dữ liệu cụ thể để tìm signal
    console.log('\n\n🎯 Looking for Active Signals:');
    console.log('='.repeat(50));

    const topCoins = await binanceClient.getTop24hVolumeCoins(10);
    console.log(`📊 Testing top 10 volume coins: ${topCoins.join(', ')}`);

    let signalsFound = 0;
    for (const symbol of topCoins) {
      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        if (klines.length >= 220) {
          const signal = await signalAnalyzer.analyzeSignal(symbol, '5m', klines);
          if (signal) {
            signalsFound++;
            console.log(`🚀 FOUND SIGNAL: ${symbol} ${signal.type} at ${signal.entry}`);
          }
        }
      } catch (error) {
        // Skip errors for individual symbols
      }
    }

    console.log(`\n📊 Total signals found: ${signalsFound} out of ${topCoins.length} symbols`);

    if (signalsFound === 0) {
      console.log('\n⚠️ NO SIGNALS FOUND - Possible reasons:');
      console.log('1. Market is in sideways/consolidation phase');
      console.log('2. MACD crossover conditions are too strict');
      console.log('3. RSI zones are too narrow');
      console.log('4. Engulfing pattern requirement is too restrictive');
      console.log('5. EMA alignment conditions are not met');
    }

    console.log('\n✅ Signal debugging completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy debug
if (require.main === module) {
  debugNoSignals().catch(console.error);
}

module.exports = debugNoSignals;
