require('dotenv').config();

// Global setup
global._ = require('lodash');
global.config = require('config');

console.log('🚀 Testing 1h Configuration...\n');

// 1. <PERSON><PERSON><PERSON> tra config chính
console.log('📋 Main Configuration:');
console.log(`   Timeframes: ${JSON.stringify(config.trading.timeframes)}`);
console.log(`   Length: ${config.trading.timeframes.length}`);
console.log(`   First timeframe: ${config.trading.timeframes[0]}`);

if (config.trading.timeframes.length === 1 && config.trading.timeframes[0] === '1h') {
  console.log('   ✅ Config correctly set to 1h only');
} else {
  console.log('   ❌ Config NOT set to 1h only');
  process.exit(1);
}

// 2. Kiểm tra các services sử dụng config
console.log('\n📊 Service Configurations:');

// MarketDataService
const marketDataService = require('./lib/services/marketDataService');
console.log(`   MarketDataService timeframes: ${JSON.stringify(marketDataService.timeframes)}`);

if (marketDataService.timeframes.length === 1 && marketDataService.timeframes[0] === '1h') {
  console.log('   ✅ MarketDataService correctly configured');
} else {
  console.log('   ❌ MarketDataService NOT correctly configured');
}

// SignalAnalyzer
const signalAnalyzer = require('./lib/trading/signalAnalyzer');
console.log(`   SignalAnalyzer timeframes: ${JSON.stringify(signalAnalyzer.timeframes)}`);

if (signalAnalyzer.timeframes.length === 1 && signalAnalyzer.timeframes[0] === '1h') {
  console.log('   ✅ SignalAnalyzer correctly configured');
} else {
  console.log('   ❌ SignalAnalyzer NOT correctly configured');
}

// 3. Kiểm tra model enums
console.log('\n🗄️  Model Enums:');

const TradingSignal = require('./lib/models/tradingSignal');
const MarketData = require('./lib/models/marketData');

// Get enum values from schema
const tradingSignalTimeframes = TradingSignal.schema.paths.timeframe.enumValues;
const marketDataTimeframes = MarketData.schema.paths.timeframe.enumValues;

console.log(`   TradingSignal timeframes: ${JSON.stringify(tradingSignalTimeframes)}`);
console.log(`   MarketData timeframes: ${JSON.stringify(marketDataTimeframes)}`);

if (tradingSignalTimeframes.includes('1h') && marketDataTimeframes.includes('1h')) {
  console.log('   ✅ Models support 1h timeframe');
} else {
  console.log('   ❌ Models do NOT support 1h timeframe');
}

console.log('\n🎉 Configuration Test Completed!');
console.log('\n📋 Summary:');
console.log('   ✅ Main config set to 1h only');
console.log('   ✅ All services use 1h timeframe');
console.log('   ✅ Models support 1h timeframe');
console.log('\n🚀 System ready for 1h timeframe trading!');