#!/usr/bin/env node

/**
 * Test EMA200 với dữ liệu 500 nến để so s<PERSON>h với Binance
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testImprovedEMA() {
  console.log('🔧 Testing Improved EMA200 Calculation...\n');

  try {
    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');

    // Test với 42USDT
    console.log('🔍 Testing 42USDT with 500 candles:');
    console.log('='.repeat(50));

    const klines = await binanceClient.getKlines('42USDT', '5m', 500);
    console.log(`✅ Lấy được ${klines.length} nến 42USDT 5m`);

    if (klines.length >= 220) {
      // Test với các độ dài khác nhau
      const testLengths = [220, 245]; // Chỉ test với số nến có sẵn

      console.log('\n📊 EMA200 Comparison:');
      console.log('Binance Exchange: 0.17348');
      console.log('');

      for (const length of testLengths) {
        if (klines.length >= length) {
          const testData = klines.slice(-length);
          const testIndicators = indicators.calculateAllIndicators(testData);

          if (testIndicators) {
            const difference = Math.abs(0.17348 - testIndicators.ema200);
            const percentDiff = (difference / 0.17348) * 100;

            console.log(`📈 ${length} candles: ${testIndicators.ema200.toFixed(6)} (diff: ${percentDiff.toFixed(2)}%)`);
          }
        }
      }

      // Test với tất cả dữ liệu có sẵn
      console.log(`\n🎯 Final Test with ${klines.length} candles:`);
      const finalIndicators = indicators.calculateAllIndicators(klines);

      if (finalIndicators) {
        console.log(`💰 Entry: ${finalIndicators.currentPrice}`);
        console.log(`📈 EMA50: ${finalIndicators.ema50}`);
        console.log(`📈 EMA200: ${finalIndicators.ema200}`);
        console.log(`📊 MACD: ${finalIndicators.macd?.macd || 'N/A'}`);
        console.log(`📊 RSI: ${finalIndicators.rsi}`);

        const binanceEMA200 = 0.17348;
        const difference = Math.abs(binanceEMA200 - finalIndicators.ema200);
        const percentDiff = (difference / binanceEMA200) * 100;

        console.log('\n⚖️ Comparison with Binance:');
        console.log(`Binance EMA200: ${binanceEMA200}`);
        console.log(`Our EMA200: ${finalIndicators.ema200}`);
        console.log(`Difference: ${difference.toFixed(6)} (${percentDiff.toFixed(2)}%)`);

        if (percentDiff < 0.5) {
          console.log('✅ EXCELLENT: Difference < 0.5%');
        } else if (percentDiff < 1.0) {
          console.log('✅ GOOD: Difference < 1.0%');
        } else {
          console.log('⚠️ NEEDS IMPROVEMENT: Difference > 1.0%');
        }
      }
    } else {
      console.log(`❌ Not enough candles: ${klines.length} (need at least 220)`);
    }

    // Test với DOGEUSDT
    console.log('\n\n🐕 Testing DOGEUSDT with 500 candles:');
    console.log('='.repeat(50));

    const dogeKlines = await binanceClient.getKlines('DOGEUSDT', '1m', 500);
    console.log(`✅ Lấy được ${dogeKlines.length} nến DOGEUSDT 1m`);

    if (dogeKlines.length >= 250) {
      const dogeIndicators = indicators.calculateAllIndicators(dogeKlines);

      if (dogeIndicators) {
        console.log(`💰 Entry: ${dogeIndicators.currentPrice}`);
        console.log(`📈 EMA50: ${dogeIndicators.ema50}`);
        console.log(`📈 EMA200: ${dogeIndicators.ema200}`);
        console.log(`📊 MACD: ${dogeIndicators.macd?.macd || 'N/A'}`);
        console.log(`📊 RSI: ${dogeIndicators.rsi}`);
      }
    }

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testImprovedEMA().catch(console.error);
}

module.exports = testImprovedEMA;
