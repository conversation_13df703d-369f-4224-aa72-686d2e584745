/**
 * Test script để kiểm tra logic trailing stop đã được sửa ĐÚNG
 * Kiểm tra breakeven logic và exit price reporting
 */

// Mock config và logger
global.config = {
  trading: {
    riskManagement: {
      stopLossPercent: 0.5,
      minRiskReward: 1.2,
      dynamicTP: { enabled: true }
    }
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

// Mock TradingSignal model
const mockTradingSignal = {
  findByIdAndUpdate: async (id, update) => {
    console.log(`📝 Mock DB Update - Signal ${id}:`, update);
    return true;
  }
};

// Mock require
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === '../models/tradingSignal') {
    return mockTradingSignal;
  }
  return originalRequire.apply(this, arguments);
};

const TrailingManager = require('./lib/trading/trailingManager');

async function testCorrectTrailingLogic() {
  console.log('🧪 Testing CORRECT Trailing Stop Logic\n');

  // Test Case: SELL Signal với logic ĐÚNG
  console.log('📉 Test Case: SELL Signal Breakeven Logic (CORRECT)');
  console.log('===================================================');

  const sellSignal = {
    _id: 'test-sell-correct',
    symbol: 'GIGGLEUSDT',
    type: 'SELL',
    entry: 110.020000,
    stopLoss: 110.570000,  // Original SL (cao hơn entry)
    takeProfit: 109.020000  // Original TP (thấp hơn entry)
  };

  console.log('📊 SELL Signal Setup:');
  console.log(`  Entry: ${sellSignal.entry}`);
  console.log(`  Original SL: ${sellSignal.stopLoss}`);
  console.log(`  Original TP: ${sellSignal.takeProfit}`);

  const riskAmount = sellSignal.stopLoss - sellSignal.entry; // 0.55
  const profitAmount = sellSignal.entry - sellSignal.takeProfit; // 1.0

  console.log(`  Risk Amount: ${riskAmount.toFixed(6)}`);
  console.log(`  Profit Amount: ${profitAmount.toFixed(6)}`);

  // Add signal to trailing
  TrailingManager.addSignalToTrailing(sellSignal);

  // Test breakeven trigger
  console.log(`\n💰 Testing breakeven trigger at price: 109.020000 (1R profit)`);
  await TrailingManager.updateTrailing('test-sell-correct', 109.020000);

  const trailingInfo = TrailingManager.getTrailingInfo('test-sell-correct');

  console.log('\n📊 Breakeven Results:');
  console.log(`  Breakeven triggered: ${trailingInfo.breakeven}`);
  console.log(`  New SL: ${trailingInfo.currentSL.toFixed(6)}`);
  console.log(`  Expected SL: ${(sellSignal.entry - riskAmount * 0.1).toFixed(6)} (Entry - 10% risk)`);

  // Validate breakeven logic
  const expectedBreakevenSL = sellSignal.entry - (riskAmount * 0.1);
  const isCorrectBreakeven = Math.abs(trailingInfo.currentSL - expectedBreakevenSL) < 0.000001;

  if (isCorrectBreakeven) {
    console.log('  ✅ CORRECT: SELL Breakeven SL = Entry - 10% risk');
  } else {
    console.log('  ❌ ERROR: SELL Breakeven SL calculation wrong!');
  }

  // Test scenario: Giá tăng trở lại (như trong case thực tế)
  console.log(`\n📈 Testing price increase to: 110.210000 (above entry)`);

  // Kiểm tra xem SL có bị hit không
  const currentPrice = 110.210000;
  const slCheck = TrailingManager.checkTrailingSLTP('test-sell-correct', currentPrice);

  console.log('\n🎯 SL Check Results:');
  console.log(`  Current Price: ${currentPrice}`);
  console.log(`  Current SL: ${trailingInfo.currentSL.toFixed(6)}`);
  console.log(`  SL Hit: ${slCheck.hitSL}`);

  if (slCheck.hitSL) {
    console.log(`  ✅ CORRECT: Price ${currentPrice} >= SL ${trailingInfo.currentSL.toFixed(6)} → Hit SL`);
    console.log(`  📊 Exit should be reported at: ${trailingInfo.currentSL.toFixed(6)} (actual SL price)`);
    console.log(`  📊 NOT at: ${currentPrice} (current market price)`);
  } else {
    console.log(`  ❌ ERROR: SL should be hit!`);
  }

  // Test BUY signal for comparison
  console.log('\n📈 Test Case: BUY Signal Breakeven Logic (for comparison)');
  console.log('======================================================');

  const buySignal = {
    _id: 'test-buy-correct',
    symbol: 'BTCUSDT',
    type: 'BUY',
    entry: 43250.00,
    stopLoss: 43000.00,   // Original SL (thấp hơn entry)
    takeProfit: 43500.00  // Original TP (cao hơn entry)
  };

  console.log('📊 BUY Signal Setup:');
  console.log(`  Entry: ${buySignal.entry}`);
  console.log(`  Original SL: ${buySignal.stopLoss}`);
  console.log(`  Original TP: ${buySignal.takeProfit}`);

  const buyRiskAmount = buySignal.entry - buySignal.stopLoss; // 250

  TrailingManager.addSignalToTrailing(buySignal);

  // Test BUY breakeven
  console.log(`\n💰 Testing BUY breakeven trigger at price: 43500.00 (1R profit)`);
  await TrailingManager.updateTrailing('test-buy-correct', 43500.00);

  const buyTrailingInfo = TrailingManager.getTrailingInfo('test-buy-correct');

  console.log('\n📊 BUY Breakeven Results:');
  console.log(`  Breakeven triggered: ${buyTrailingInfo.breakeven}`);
  console.log(`  New SL: ${buyTrailingInfo.currentSL.toFixed(2)}`);
  console.log(`  Expected SL: ${(buySignal.entry + buyRiskAmount * 0.1).toFixed(2)} (Entry + 10% risk)`);

  const expectedBuyBreakevenSL = buySignal.entry + (buyRiskAmount * 0.1);
  const isCorrectBuyBreakeven = Math.abs(buyTrailingInfo.currentSL - expectedBuyBreakevenSL) < 0.01;

  if (isCorrectBuyBreakeven) {
    console.log('  ✅ CORRECT: BUY Breakeven SL = Entry + 10% risk');
  } else {
    console.log('  ❌ ERROR: BUY Breakeven SL calculation wrong!');
  }

  console.log('\n🎯 Summary of Correct Logic');
  console.log('============================');
  console.log('✅ SELL Breakeven: SL = Entry - 10% risk (THẤP HƠN entry)');
  console.log('✅ BUY Breakeven: SL = Entry + 10% risk (CAO HƠN entry)');
  console.log('✅ Exit price reporting: Use actual SL/TP price, not current market price');
  console.log('✅ This prevents false loss reporting when price moves after SL hit');

  console.log('\n📋 Real-world Example (GIGGLEUSDT):');
  console.log('Entry: 110.020000');
  console.log('Breakeven SL: 109.965000 (Entry - 10% risk)');
  console.log('Market price when checked: 110.210000');
  console.log('→ SL was hit at 109.965000 (should report this price)');
  console.log('→ NOT at 110.210000 (current market price)');

  // Cleanup
  TrailingManager.removeSignalFromTrailing('test-sell-correct');
  TrailingManager.removeSignalFromTrailing('test-buy-correct');
}

// Run the test
if (require.main === module) {
  testCorrectTrailingLogic().catch(console.error);
}

module.exports = { testCorrectTrailingLogic };