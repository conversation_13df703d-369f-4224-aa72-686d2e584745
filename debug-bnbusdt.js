#!/usr/bin/env node

/**
 * Debug BNBUSDT để tìm hiểu tại sao chỉ có 276 nến
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function debugBNBUSDT() {
  console.log('🔍 Debugging BNBUSDT Data Issue...\n');

  try {
    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const marketDataService = require('./lib/services/marketDataService');

    console.log('1️⃣ Testing Direct Binance API Call:');
    console.log('='.repeat(50));

    // Test trực tiếp với Binance API
    try {
      const directKlines = await binanceClient.getKlines('BNBUSDT', '5m', 1000);
      console.log(`📊 Direct API call: ${directKlines.length} candles`);
      
      if (directKlines.length > 0) {
        console.log(`📅 From: ${directKlines[0].openTime.toISOString()}`);
        console.log(`📅 To: ${directKlines[directKlines.length - 1].openTime.toISOString()}`);
        
        // Kiểm tra gaps trong dữ liệu
        let gaps = 0;
        for (let i = 1; i < directKlines.length; i++) {
          const timeDiff = directKlines[i].openTime - directKlines[i-1].openTime;
          const expectedDiff = 5 * 60 * 1000; // 5 minutes
          if (timeDiff !== expectedDiff) {
            gaps++;
          }
        }
        console.log(`⚠️ Data gaps found: ${gaps}`);
        
        // Hiển thị 5 nến đầu và cuối
        console.log('\n📊 First 5 candles:');
        directKlines.slice(0, 5).forEach((candle, i) => {
          console.log(`${i+1}. ${candle.openTime.toISOString()} | O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
        });
        
        console.log('\n📊 Last 5 candles:');
        directKlines.slice(-5).forEach((candle, i) => {
          console.log(`${i+1}. ${candle.openTime.toISOString()} | O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
        });
      }
    } catch (error) {
      console.log(`❌ Direct API error: ${error.message}`);
    }

    console.log('\n\n2️⃣ Testing Database Query:');
    console.log('='.repeat(50));

    // Test database query
    try {
      const dbCandles = await marketDataService.getLatestCandles('BNBUSDT', '5m', 1000);
      console.log(`📊 Database query: ${dbCandles.length} candles`);
      
      if (dbCandles.length > 0) {
        console.log(`📅 From: ${dbCandles[0].openTime.toISOString()}`);
        console.log(`📅 To: ${dbCandles[dbCandles.length - 1].openTime.toISOString()}`);
        
        // Kiểm tra thứ tự
        const isAscending = dbCandles[0].openTime <= dbCandles[dbCandles.length - 1].openTime;
        console.log(`📊 Order: ${isAscending ? 'Ascending ✅' : 'Descending ❌'}`);
      }
    } catch (error) {
      console.log(`❌ Database error: ${error.message}`);
    }

    console.log('\n\n3️⃣ Checking Database Records:');
    console.log('='.repeat(50));

    // Kiểm tra records trong database
    try {
      const totalRecords = await MarketData.countDocuments({
        symbol: 'BNBUSDT',
        timeframe: '5m'
      });
      console.log(`📊 Total BNBUSDT 5m records in DB: ${totalRecords}`);

      // Lấy record mới nhất và cũ nhất
      const latestRecord = await MarketData.findOne({
        symbol: 'BNBUSDT',
        timeframe: '5m'
      }).sort({ openTime: -1 });

      const oldestRecord = await MarketData.findOne({
        symbol: 'BNBUSDT',
        timeframe: '5m'
      }).sort({ openTime: 1 });

      if (latestRecord && oldestRecord) {
        console.log(`📅 Oldest record: ${oldestRecord.openTime.toISOString()}`);
        console.log(`📅 Latest record: ${latestRecord.openTime.toISOString()}`);
        
        // Tính khoảng thời gian
        const timeDiff = latestRecord.openTime - oldestRecord.openTime;
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        const expectedCandles = Math.floor(hoursDiff / (5/60)); // 5 minutes per candle
        
        console.log(`⏰ Time span: ${hoursDiff.toFixed(1)} hours`);
        console.log(`📊 Expected candles: ${expectedCandles}`);
        console.log(`📊 Actual records: ${totalRecords}`);
        console.log(`📊 Missing: ${expectedCandles - totalRecords} candles`);
      }
    } catch (error) {
      console.log(`❌ Database check error: ${error.message}`);
    }

    console.log('\n\n4️⃣ Comparing with Other Symbols:');
    console.log('='.repeat(50));

    // So sánh với các symbols khác
    const compareSymbols = ['BTCUSDT', 'ETHUSDT', 'DOGEUSDT'];
    
    for (const symbol of compareSymbols) {
      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        const dbCandles = await marketDataService.getLatestCandles(symbol, '5m', 1000);
        
        console.log(`📊 ${symbol}: API=${klines.length}, DB=${dbCandles.length}`);
      } catch (error) {
        console.log(`❌ ${symbol}: Error - ${error.message}`);
      }
    }

    console.log('\n\n5️⃣ Testing Historical Data Load:');
    console.log('='.repeat(50));

    // Test load historical data
    try {
      console.log('🔄 Loading historical data for BNBUSDT...');
      await marketDataService.loadHistoricalData('BNBUSDT', '5m');
      
      // Kiểm tra lại sau khi load
      const afterLoadCandles = await marketDataService.getLatestCandles('BNBUSDT', '5m', 1000);
      console.log(`📊 After loading: ${afterLoadCandles.length} candles`);
      
    } catch (error) {
      console.log(`❌ Historical load error: ${error.message}`);
    }

    console.log('\n✅ BNBUSDT debug completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy debug
if (require.main === module) {
  debugBNBUSDT().catch(console.error);
}

module.exports = debugBNBUSDT;
