#!/usr/bin/env node

/**
 * Test logic tín hiệu linh hoạt hơn
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testFlexibleSignalLogic() {
  console.log('🔄 Testing Flexible Signal Logic...\n');

  try {
    // <PERSON><PERSON><PERSON> n<PERSON>i <PERSON>go<PERSON>
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    console.log('📊 Flexible Signal Logic:');
    console.log('='.repeat(60));
    console.log('✅ Body mạnh: 60% (was 70%)');
    console.log('✅ Pattern: Body mạnh HOẶC Engulfing HOẶC MACD crossover');
    console.log('✅ Logic: Core + Signal + (Pattern HOẶC MACD)');
    console.log('');

    // Test với top volume coins
    const topCoins = await binanceClient.getTop24hVolumeCoins(15);
    console.log(`📊 Testing ${topCoins.length} top volume coins...`);

    let totalSignals = 0;
    const foundSignals = [];

    for (const symbol of topCoins) {
      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        
        if (klines.length >= 220) {
          const indicatorData = indicators.calculateAllIndicators(klines);
          
          if (indicatorData) {
            const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);

            // Test BUY conditions
            const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);
            
            // Test SELL conditions
            const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);

            // Test signal generation
            const signal = await signalAnalyzer.analyzeSignal(symbol, '5m', klines);
            
            if (signal) {
              totalSignals++;
              foundSignals.push({
                symbol,
                type: signal.type,
                entry: signal.entry,
                riskReward: signal.riskReward,
                conditions: signal.type === 'BUY' ? buyConditions : sellConditions,
                strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0,
                macdCrossover,
                rsi: indicatorData.rsi
              });
              
              console.log(`🚀 ${symbol}: ${signal.type} at ${signal.entry} (RR: 1:${signal.riskReward})`);
              console.log(`   Body: ${indicatorData.strongBody?.bodyPercent?.toFixed(1) || 0}%, MACD: ${macdCrossover}, RSI: ${indicatorData.rsi?.toFixed(1)}`);
            }
          }
        }
      } catch (error) {
        // Skip individual errors
      }
    }

    console.log(`\n📊 RESULTS SUMMARY:`);
    console.log('='.repeat(60));
    console.log(`Total signals found: ${totalSignals} out of ${topCoins.length} symbols`);
    console.log(`Success rate: ${((totalSignals/topCoins.length)*100).toFixed(1)}%`);

    if (foundSignals.length > 0) {
      console.log(`\n🎉 Found Signals:`);
      foundSignals.forEach((signal, i) => {
        console.log(`${i+1}. ${signal.symbol}: ${signal.type} at ${signal.entry} (RR: 1:${signal.riskReward})`);
        console.log(`   📊 Body: ${signal.strongBodyPercent.toFixed(1)}%, MACD: ${signal.macdCrossover}, RSI: ${signal.rsi?.toFixed(1)}`);
        console.log(`   📋 Core: ${signal.conditions.details.coreConditions}, Signal: ${signal.conditions.details.signalCondition}, Pattern: ${signal.conditions.details.patternCondition}`);
      });

      console.log(`\n📈 Analysis:`);
      const buySignals = foundSignals.filter(s => s.type === 'BUY');
      const sellSignals = foundSignals.filter(s => s.type === 'SELL');
      
      console.log(`BUY signals: ${buySignals.length}`);
      console.log(`SELL signals: ${sellSignals.length}`);
      
      // Phân tích pattern usage
      const strongBodySignals = foundSignals.filter(s => s.strongBodyPercent >= 60);
      const macdSignals = foundSignals.filter(s => s.macdCrossover !== 'none');
      
      console.log(`Strong Body (≥60%) signals: ${strongBodySignals.length}`);
      console.log(`MACD crossover signals: ${macdSignals.length}`);
      
      // Hiển thị average metrics
      const avgRR = foundSignals.reduce((sum, s) => sum + s.riskReward, 0) / foundSignals.length;
      const avgBodyPercent = foundSignals.reduce((sum, s) => sum + s.strongBodyPercent, 0) / foundSignals.length;
      
      console.log(`Average Risk/Reward: 1:${avgRR.toFixed(2)}`);
      console.log(`Average Body %: ${avgBodyPercent.toFixed(1)}%`);
      
    } else {
      console.log(`\n⚠️ Still no signals found. Market might be in consolidation.`);
    }

    console.log('\n✅ Flexible signal logic test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testFlexibleSignalLogic().catch(console.error);
}

module.exports = testFlexibleSignalLogic;
