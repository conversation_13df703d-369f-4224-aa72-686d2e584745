# Tài liệu hướng dẫn hệ thống Trading - Requirements

## Giới thiệu

Tài liệu này mô tả chi tiết cách thức hoạt động của hệ thống trading tự động, bao gồm cách v<PERSON><PERSON> l<PERSON>, đặt Stop Loss (SL), Take Profit (TP), và quản lý rủi ro.

## Requirements

### Requirement 1: Hiểu rõ cách hệ thống phân tích và tạo tín hiệu

**User Story:** <PERSON><PERSON> một trader, tôi muốn hiểu rõ cách hệ thống phân tích thị trường và tạo ra tín hiệu mua/bán, để có thể theo dõi và đánh giá hiệu quả.

#### Acceptance Criteria

1. WHEN hệ thống nhận dữ liệu nến THEN hệ thống SHALL tính toán các chỉ bá<PERSON> kỹ thuật (EMA50, EMA200, MACD, RSI)
2. WHEN các điều kiện core được thỏa mãn THEN hệ thống SHALL kiểm tra điều kiện signal và pattern
3. WHEN tất cả điều kiện BUY được thỏa mãn THEN hệ thống SHALL tạo tín hiệu BUY với entry, SL, TP
4. WHEN tất cả điều kiện SELL được thỏa mãn THEN hệ thống SHALL tạo tín hiệu SELL với entry, SL, TP
5. WHEN có tín hiệu mới THEN hệ thống SHALL kiểm tra trùng lặp và conflict với lệnh đang active

### Requirement 2: Hiểu rõ cách tính toán Stop Loss và Take Profit

**User Story:** Là một trader, tôi muốn hiểu cách hệ thống tính toán SL/TP, để có thể đánh giá mức độ rủi ro và lợi nhuận tiềm năng.

#### Acceptance Criteria

1. WHEN tạo lệnh BUY THEN hệ thống SHALL tính SL dựa trên support, ATR hoặc percentage
2. WHEN tạo lệnh SELL THEN hệ thống SHALL tính SL dựa trên resistance, ATR hoặc percentage
3. WHEN tính TP THEN hệ thống SHALL ưu tiên resistance/support levels, sau đó ATR, cuối cùng là dynamic RR
4. WHEN tính toán xong THEN hệ thống SHALL đảm bảo Risk/Reward ratio tối thiểu 1.2:1
5. WHEN có ATR THEN hệ thống SHALL sử dụng ATR multiplier để tính SL/TP chính xác hơn

### Requirement 3: Hiểu rõ hệ thống quản lý lệnh và trailing

**User Story:** Là một trader, tôi muốn hiểu cách hệ thống theo dõi và quản lý các lệnh đang active, bao gồm trailing stop.

#### Acceptance Criteria

1. WHEN có lệnh active THEN hệ thống SHALL theo dõi giá mỗi 5 giây
2. WHEN giá chạm SL THEN hệ thống SHALL đóng lệnh và cập nhật trạng thái 'hit_sl'
3. WHEN giá chạm TP THEN hệ thống SHALL đóng lệnh và cập nhật trạng thái 'hit_tp'
4. WHEN trailing enabled THEN hệ thống SHALL kích hoạt trailing khi đạt 1.5R
5. WHEN đạt 1R THEN hệ thống SHALL move SL về breakeven
6. WHEN đạt TP1 THEN hệ thống SHALL thực hiện partial profit và extend TP

### Requirement 4: Hiểu rõ các điều kiện vào lệnh

**User Story:** Là một trader, tôi muốn hiểu rõ các điều kiện cụ thể để hệ thống tạo lệnh BUY/SELL.

#### Acceptance Criteria

1. WHEN kiểm tra BUY THEN hệ thống SHALL yêu cầu: giá > EMA200, EMA50 > EMA200, MACD cắt lên hoặc RSI 50-70, nến body mạnh ≥60% hoặc Bullish Engulfing
2. WHEN kiểm tra SELL THEN hệ thống SHALL yêu cầu: giá < EMA200, EMA50 < EMA200, MACD cắt xuống hoặc RSI 30-50, nến body mạnh ≥60% hoặc Bearish Engulfing
3. WHEN thiếu điều kiện core THEN hệ thống SHALL không tạo tín hiệu
4. WHEN thiếu pattern condition THEN hệ thống SHALL không tạo tín hiệu
5. WHEN có đủ điều kiện THEN hệ thống SHALL tạo tín hiệu và gửi thông báo Telegram

### Requirement 5: Hiểu rõ hệ thống thống kê và báo cáo

**User Story:** Là một trader, tôi muốn xem thống kê hiệu quả trading để đánh giá và cải thiện chiến lược.

#### Acceptance Criteria

1. WHEN lệnh đóng THEN hệ thống SHALL tính toán P&L dựa trên giá thực tế
2. WHEN có kết quả THEN hệ thống SHALL cập nhật thống kê tổng (win rate, total P&L)
3. WHEN gửi thông báo kết quả THEN hệ thống SHALL bao gồm thống kê tổng trong tin nhắn
4. WHEN yêu cầu THEN hệ thống SHALL cung cấp thống kê theo khoảng thời gian
5. WHEN cleanup THEN hệ thống SHALL xóa các signal cũ sau 30 ngày