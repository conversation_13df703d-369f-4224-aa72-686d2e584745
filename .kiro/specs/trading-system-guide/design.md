# Tài liệu thiết kế hệ thống Trading

## Tổng quan

Hệ thống trading tự động này được thiết kế để phân tích thị trường crypto, tạo tín hiệu mua/b<PERSON>, và quản lý rủi ro một cách tự động. Hệ thống sử dụng các chỉ báo kỹ thuật kết hợp với pattern recognition để đưa ra quyết định trading.

## Kiến trúc hệ thống

### Luồng hoạt động chính

```mermaid
graph TD
    A[Binance API] --> B[Market Data Service]
    B --> C[Signal Analyzer]
    C --> D{Điều kiện đủ?}
    D -->|Có| E[Tạo Signal]
    D -->|Không| F[Bỏ qua]
    E --> G[Order Manager]
    G --> H[Trailing Manager]
    G --> I[Telegram <PERSON>]
    H --> J[Price Monitor]
    J --> K{SL/TP hit?}
    K -->|Có| L[Đóng lệnh]
    K -->|Không| M[Tiếp tục monitor]
    L --> N[Cập nhật thống kê]
```

## Các thành phần và giao diện

### 1. Signal Analyzer (lib/trading/signalAnalyzer.js)

**Chức năng:** Phân tích dữ liệu thị trường và tạo tín hiệu trading

**Điều kiện vào lệnh BUY:**
- **Core Conditions (BẮT BUỘC):**
  - Giá đóng nến > EMA200
  - EMA50 > EMA200 (xu hướng tăng)

- **Signal Conditions (1 trong 2):**
  - MACD vừa cắt lên signal line
  - RSI trong vùng 50-70

- **Pattern Conditions (BẮT BUỘC):**
  - Nến có body mạnh ≥60% tổng chiều cao nến, HOẶC
  - Bullish Engulfing pattern

**Điều kiện vào lệnh SELL:**
- **Core Conditions (BẮT BUỘC):**
  - Giá đóng nến < EMA200
  - EMA50 < EMA200 (xu hướng giảm)

- **Signal Conditions (1 trong 2):**
  - MACD vừa cắt xuống signal line
  - RSI trong vùng 30-50

- **Pattern Conditions (BẮT BUỘC):**
  - Nến có body mạnh ≥60% tổng chiều cao nến, HOẶC
  - Bearish Engulfing pattern

### 2. Technical Indicators (lib/trading/indicators.js)

**Chức năng:** Tính toán các chỉ báo kỹ thuật và SL/TP

**Các chỉ báo được sử dụng:**
- **EMA (Exponential Moving Average):** EMA50 và EMA200 với SMA seed method
- **MACD:** Fast=12, Slow=26, Signal=9
- **RSI:** Period=14, Buy zone=50-70, Sell zone=30-50
- **ATR:** Average True Range để tính toán SL/TP động

**Thuật toán tính Stop Loss:**

Cho lệnh BUY:
1. **Ưu tiên 1:** Support level gần nhất (nếu < 2% từ entry)
2. **Ưu tiên 2:** ATR-based: Entry - (ATR × 1.2)
3. **Fallback:** Percentage-based: Entry × (1 - 0.5%)

Cho lệnh SELL:
1. **Ưu tiên 1:** Resistance level gần nhất (nếu < 2% từ entry)
2. **Ưu tiên 2:** ATR-based: Entry + (ATR × 1.2)
3. **Fallback:** Percentage-based: Entry × (1 + 0.5%)

**Thuật toán tính Take Profit:**

Cho lệnh BUY:
1. **Ưu tiên 1:** Resistance level > minTP (RR ≥ 1.2)
2. **Ưu tiên 2:** ATR-based: Entry + (ATR × 2.0)
3. **Ưu tiên 3:** Dynamic RR based trên risk amount

Cho lệnh SELL:
1. **Ưu tiên 1:** Support level < minTP (RR ≥ 1.2)
2. **Ưu tiên 2:** ATR-based: Entry - (ATR × 2.0)
3. **Ưu tiên 3:** Dynamic RR based trên risk amount

### 3. Order Manager (lib/trading/orderManager.js)

**Chức năng:** Quản lý và theo dõi các lệnh đang active

**Monitoring Loop:**
- Kiểm tra giá mỗi 5 giây
- So sánh với SL/TP của từng signal active
- Xử lý khi hit SL hoặc TP
- Cập nhật trailing stop nếu enabled

**Xử lý kết quả:**
- Tính toán P&L thực tế
- Cập nhật trạng thái signal trong database
- Gửi thông báo kết quả qua Telegram
- Cập nhật thống kê tổng

### 4. Trailing Manager (lib/trading/trailingManager.js)

**Chức năng:** Quản lý trailing stop để tối ưu hóa lợi nhuận

**Trailing Logic cho BUY:**
1. **Breakeven:** Khi đạt 1R (risk amount), move SL về Entry + 10% risk
2. **Activate Trailing:** Khi đạt 1.5R, bắt đầu trailing
3. **Trailing Distance:** 50% của original risk amount
4. **Partial TP:** Khi hit TP1, extend TP thêm 1R

**Trailing Logic cho SELL:**
1. **Breakeven:** Khi đạt 1R, move SL về Entry - 10% risk
2. **Activate Trailing:** Khi đạt 1.5R, bắt đầu trailing
3. **Trailing Distance:** 50% của original risk amount
4. **Partial TP:** Khi hit TP1, extend TP thêm 1R

## Mô hình dữ liệu

### TradingSignal Schema

```javascript
{
  symbol: String,           // Cặp trading (VD: BTCUSDT)
  timeframe: String,        // Khung thời gian (5m, 15m)
  type: String,            // BUY hoặc SELL
  entry: Number,           // Giá vào lệnh
  stopLoss: Number,        // Giá stop loss
  takeProfit: Number,      // Giá take profit
  riskReward: Number,      // Tỷ lệ risk/reward
  tpMethod: String,        // Phương pháp tính TP
  atr: Number,            // ATR tại thời điểm tạo signal
  status: String,         // active, hit_tp, hit_sl, cancelled
  exitPrice: Number,      // Giá đóng lệnh
  pnlPercent: Number,     // P&L tính theo %
  indicators: {           // Các chỉ báo tại thời điểm tạo signal
    ema50: Number,
    ema200: Number,
    macd: Object,
    rsi: Number,
    engulfing: String,
    strongBody: Object
  }
}
```

## Xử lý lỗi

### Lỗi kết nối API
- Retry mechanism với exponential backoff
- Fallback sang API backup nếu có
- Log chi tiết để debug

### Lỗi tính toán chỉ báo
- Validate dữ liệu đầu vào (tối thiểu 220 nến)
- Handle missing data gracefully
- Return null nếu không đủ dữ liệu

### Lỗi database
- Retry operations với timeout
- Graceful degradation nếu không save được
- Backup data trong memory tạm thời

## Chiến lược testing

### Unit Tests
- Test từng chỉ báo kỹ thuật riêng biệt
- Test logic tính SL/TP với các scenarios khác nhau
- Test pattern recognition accuracy

### Integration Tests
- Test toàn bộ flow từ data → signal → execution
- Test với dữ liệu thị trường thực
- Test performance với volume cao

### Backtesting
- Test chiến lược trên dữ liệu lịch sử
- Đo lường win rate, max drawdown, Sharpe ratio
- Optimize parameters dựa trên kết quả backtest

## Cấu hình hệ thống

### Risk Management Settings
```json
{
  "stopLossPercent": 0.5,           // SL mặc định 0.5%
  "takeProfitPercent": [1, 2],      // TP levels
  "minRiskReward": 1.2,             // RR tối thiểu
  "maxRiskPercent": 2.0,            // Risk tối đa mỗi lệnh
  "atrMultiplier": {
    "stopLoss": 1.2,                // ATR multiplier cho SL
    "takeProfit": 2.0               // ATR multiplier cho TP
  },
  "dynamicTP": {
    "enabled": true,                // Bật trailing
    "preferSR": true,               // Ưu tiên S/R levels
    "preferATR": true               // Ưu tiên ATR-based
  }
}
```

### Indicator Settings
```json
{
  "ema": { "fast": 50, "slow": 200 },
  "macd": { "fast": 12, "slow": 26, "signal": 9 },
  "rsi": {
    "period": 14,
    "buyZone": [50, 70],
    "sellZone": [30, 50]
  }
}
```

## Tối ưu hóa hiệu suất

### Caching Strategy
- Cache indicator calculations
- Cache support/resistance levels
- Invalidate cache khi có dữ liệu mới

### Database Optimization
- Index trên symbol, status, createdAt
- Cleanup old signals định kỳ
- Aggregate statistics efficiently

### Memory Management
- Limit số lượng signals active
- Cleanup trailing data cũ
- Monitor memory usage và alert nếu cao