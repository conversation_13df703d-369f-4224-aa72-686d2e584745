/**
 * Test script để kiểm tra logic conflict detection đã được sửa
 * Đả<PERSON> bảo không báo conflict với signals đã đóng hoặc zombie
 */

// Mock config và logger
global.config = {
  trading: {
    timeframes: ['5m', '15m']
  }
};
global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

// Mock moment
global.moment = require('moment');

// Mock active signals trong orderManager
const mockActiveSignals = new Map();

// Mock TradingSignal model
const mockTradingSignal = {
  getActiveSignalBySymbol: async (symbol, timeframe) => {
    // Simulate different scenarios
    if (symbol === 'TAOUSDT' && timeframe === '5m') {
      // Case 1: Signal đã hit TP nhưng chưa cập nhật DB (zombie)
      return {
        _id: 'zombie-signal-1',
        symbol: 'TAOUSDT',
        timeframe: '5m',
        type: 'SELL',
        entry: 430.57,
        stopLoss: 435.96,
        takeProfit: 424.102,
        status: 'active', // ❌ Vẫn active trong DB
        createdAt: new Date(Date.now() - 15 * 60 * 60 * 1000), // 15 giờ trước
        isConflictNotification: false
      };
    }

    if (symbol === 'EVAAUSDT' && timeframe === '5m') {
      // Case 2: Signal thực sự đang active và được monitor
      const signalId = 'active-signal-1';
      mockActiveSignals.set(signalId, true); // Đang được monitor

      return {
        _id: signalId,
        symbol: 'EVAAUSDT',
        timeframe: '5m',
        type: 'SELL',
        entry: 10.184,
        stopLoss: 10.48305714,
        takeProfit: 9.82513143,
        status: 'active',
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 phút trước
        isConflictNotification: false
      };
    }

    if (symbol === 'BTCUSDT' && timeframe === '5m') {
      // Case 3: Không có signal active
      return null;
    }

    return null;
  },

  getActiveSignals: async () => {
    return [
      {
        _id: 'zombie-signal-1',
        symbol: 'TAOUSDT',
        timeframe: '5m',
        type: 'SELL',
        status: 'active',
        createdAt: new Date(Date.now() - 15 * 60 * 60 * 1000)
      },
      {
        _id: 'active-signal-1',
        symbol: 'EVAAUSDT',
        timeframe: '5m',
        type: 'SELL',
        status: 'active',
        createdAt: new Date(Date.now() - 30 * 60 * 1000)
      }
    ];
  },

  findByIdAndUpdate: async (id, update) => {
    console.log(`📝 Mock: Updated signal ${id} with status: ${update.status}`);
    return true;
  }
};

// Mock orderManager
const mockOrderManager = {
  activeSignals: mockActiveSignals,
  updateSignalStatus: async (id, status, price, time) => {
    console.log(`📝 Mock: Signal ${id} updated to ${status}`);
    return { _id: id, status };
  }
};

// Mock require
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === '../models/tradingSignal') {
    return mockTradingSignal;
  }
  if (id === './orderManager') {
    return mockOrderManager;
  }
  if (id === '../logger') {
    return () => global.logger;
  }
  return originalRequire.apply(this, arguments);
};

async function testConflictFix() {
  console.log('🧪 Testing Conflict Detection Fix\n');
  console.log('=================================\n');

  const SignalAnalyzer = require('./lib/trading/signalAnalyzer');

  console.log('📊 Test Scenarios:');
  console.log('==================');
  console.log('1. TAOUSDT: Zombie signal (active in DB, not monitored, 15h old)');
  console.log('2. EVAAUSDT: Real active signal (monitored, 30min old)');
  console.log('3. BTCUSDT: No active signal');

  console.log('\n🎯 Test 1: Zombie Signal Detection (TAOUSDT)');
  console.log('============================================');

  const zombieConflict = await SignalAnalyzer.checkActiveSignalConflict('TAOUSDT', '5m', 'BUY');

  console.log('Zombie signal test:');
  console.log(`  Has Conflict: ${zombieConflict.hasConflict}`);
  console.log(`  Expected: false (should not conflict with zombie signal)`);
  console.log(`  Result: ${!zombieConflict.hasConflict ? '✅ CORRECT' : '❌ INCORRECT'}`);

  if (zombieConflict.hasConflict) {
    console.log(`  Conflict Type: ${zombieConflict.conflictType}`);
    console.log(`  Active Signal: ${zombieConflict.activeSignal.symbol} ${zombieConflict.activeSignal.type}`);
  }

  console.log('\n🎯 Test 2: Real Active Signal Detection (EVAAUSDT)');
  console.log('=================================================');

  const realConflict = await SignalAnalyzer.checkActiveSignalConflict('EVAAUSDT', '5m', 'SELL');

  console.log('Real active signal test:');
  console.log(`  Has Conflict: ${realConflict.hasConflict}`);
  console.log(`  Expected: true (should conflict with real active signal)`);
  console.log(`  Result: ${realConflict.hasConflict ? '✅ CORRECT' : '❌ INCORRECT'}`);

  if (realConflict.hasConflict) {
    console.log(`  Conflict Type: ${realConflict.conflictType}`);
    console.log(`  Active Signal: ${realConflict.activeSignal.symbol} ${realConflict.activeSignal.type}`);
  }

  console.log('\n🎯 Test 3: No Active Signal (BTCUSDT)');
  console.log('=====================================');

  const noConflict = await SignalAnalyzer.checkActiveSignalConflict('BTCUSDT', '5m', 'BUY');

  console.log('No active signal test:');
  console.log(`  Has Conflict: ${noConflict.hasConflict}`);
  console.log(`  Expected: false (no active signal)`);
  console.log(`  Result: ${!noConflict.hasConflict ? '✅ CORRECT' : '❌ INCORRECT'}`);

  console.log('\n🎯 Test 4: Zombie Cleanup Function');
  console.log('==================================');

  console.log('Testing zombie cleanup...');
  const cleanedCount = await SignalAnalyzer.cleanupZombieSignals();

  console.log(`Zombie cleanup results:`);
  console.log(`  Cleaned signals: ${cleanedCount}`);
  console.log(`  Expected: 1 (TAOUSDT zombie signal)`);
  console.log(`  Result: ${cleanedCount === 1 ? '✅ CORRECT' : '❌ INCORRECT'}`);

  console.log('\n🎯 Test 5: Edge Cases');
  console.log('=====================');

  // Test với signal rất cũ
  console.log('Testing very old signal detection...');
  // Tạm thời modify mock để test
  const originalGetActiveSignalBySymbol = mockTradingSignal.getActiveSignalBySymbol;
  mockTradingSignal.getActiveSignalBySymbol = async (symbol, timeframe) => {
    if (symbol === 'OLDUSDT') {
      return {
        _id: 'very-old-signal',
        symbol: 'OLDUSDT',
        timeframe: '5m',
        type: 'BUY',
        status: 'active',
        createdAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 giờ trước
        isConflictNotification: false
      };
    }
    return originalGetActiveSignalBySymbol(symbol, timeframe);
  };

  const oldSignalConflict = await SignalAnalyzer.checkActiveSignalConflict('OLDUSDT', '5m', 'SELL');
  console.log(`  Very old signal (25h): Has Conflict = ${oldSignalConflict.hasConflict}`);
  console.log(`  Should warn about old signal but may still conflict`);

  console.log('\n🎉 Test Summary');
  console.log('===============');
  console.log('✅ Improved conflict detection logic:');
  console.log('   - Checks if signal is actually being monitored');
  console.log('   - Warns about very old active signals');
  console.log('   - Allows bypassing zombie signals');
  console.log('✅ Zombie cleanup function:');
  console.log('   - Identifies signals active in DB but not monitored');
  console.log('   - Automatically cancels old zombie signals');
  console.log('   - Scheduled to run every 30 minutes');
  console.log('✅ This should reduce false conflict notifications!');

  console.log('\n📋 Real-world Impact:');
  console.log('=====================');
  console.log('- TAOUSDT case: Zombie signal will be cleaned up automatically');
  console.log('- EVAAUSDT case: Real conflicts will still be detected correctly');
  console.log('- Fewer false conflict notifications');
  console.log('- Better system reliability');
}

// Run the test
if (require.main === module) {
  testConflictFix().catch(console.error);
}

module.exports = { testConflictFix };