# 🚀 Hướng Dẫn Sử Dụng Hệ Thống Backtest

## 📋 Tổng Quan

Hệ thống backtest cho phép bạn kiểm tra hiệu suất của trading strategy trên dữ liệu lịch sử, gi<PERSON>p đ<PERSON>h giá và tối ưu hóa chiến lược trước khi áp dụng vào live trading.

## 🏗️ Kiến Trúc Hệ Thống

```
lib/backtest/
├── BacktestEngine.js      # Engine chính điều phối toàn bộ quá trình
├── DataManager.js         # Quản lý download và lưu trữ dữ liệu lịch sử
├── SignalSimulator.js     # Mô phỏng việc tạo signal
├── OrderSimulator.js      # Mô phỏng thực thi lệnh
├── PortfolioManager.js    # Quản lý danh mục và vốn
├── RiskAnalyzer.js        # Phân tích rủi ro và metrics
└── ReportGenerator.js     # T<PERSON><PERSON> báo cáo chi tiết

scripts/
├── download-historical-data.js  # Download dữ liệu lịch sử
└── run-backtest.js             # Chạy backtest cơ bản

# Scripts mới
├── test-backtest-system.js     # Test hệ thống
├── run-full-backtest.js        # Chạy backtest với nhiều options
└── optimize-backtest.js        # Tối ưu hóa parameters
```

## 🚀 Bắt Đầu Nhanh

### Bước 1: Download Dữ Liệu Lịch Sử

```bash
# Download dữ liệu cho 15 symbols, timeframe 5m và 15m
node scripts/download-historical-data.js
```

### Bước 2: Test Hệ Thống

```bash
# Kiểm tra xem hệ thống có hoạt động không
node test-backtest-system.js
```

### Bước 3: Chạy Backtest Đầu Tiên

```bash
# Chạy backtest nhanh (1 tháng, 2 symbols)
node run-full-backtest.js --config quick

# Chạy backtest chuẩn (3 tháng, 5 symbols)
node run-full-backtest.js --config standard

# Chạy backtest toàn diện (6 tháng, 10 symbols)
node run-full-backtest.js --config comprehensive
```

## 📊 Các Loại Backtest

### 1. Quick Test
- **Thời gian**: 1 tháng
- **Symbols**: BTCUSDT, ETHUSDT
- **Mục đích**: Test nhanh, kiểm tra hệ thống

### 2. Standard Test
- **Thời gian**: 3 tháng
- **Symbols**: 5 pairs chính
- **Market Filter**: Bật
- **Mục đích**: Đánh giá hiệu suất cơ bản

### 3. Comprehensive Test
- **Thời gian**: 6 tháng
- **Symbols**: 10 pairs
- **Trailing Stop**: Bật
- **Mục đích**: Đánh giá toàn diện

### 4. Conservative Test
- **Position Size**: 1% (thấp hơn)
- **Max Drawdown**: 10%
- **Mục đích**: Chiến lược ít rủi ro

### 5. Aggressive Test
- **Position Size**: 3% (cao hơn)
- **Max Positions**: 7
- **Market Filter**: Tắt
- **Mục đích**: Chiến lược tích cực

## 🎛️ Tùy Chọn Nâng Cao

### Chạy Backtest với Options

```bash
# Chạy với verbose output
node run-full-backtest.js --config standard --verbose

# Chạy không tạo report
node run-full-backtest.js --config quick --no-report

# Xem help
node run-full-backtest.js --help
```

### Tối Ưu Hóa Parameters

```bash
# Tối ưu nhanh (20 iterations)
node optimize-backtest.js --mode quick --metric totalReturn

# Tối ưu chuẩn (50 iterations)
node optimize-backtest.js --mode standard --metric sharpeRatio

# Tối ưu toàn diện (100 iterations)
node optimize-backtest.js --mode comprehensive --metric composite

# Giới hạn số iterations
node optimize-backtest.js --mode standard --max-iterations 30
```

## 📈 Hiểu Kết Quả Backtest

### Performance Metrics

```
💰 Performance Summary:
   Initial Capital: $10,000
   Final Capital: $12,500
   Total Return: 25.0%
   Total P&L: $2,500

📈 Trading Statistics:
   Total Signals: 150
   Total Trades: 120
   Win Rate: 68.33%
   Winning Trades: 82
   Losing Trades: 38
   Profit Factor: 2.15

⚠️ Risk Metrics:
   Max Drawdown: 8.5%
   Sharpe Ratio: 1.85
   Volatility: 12.3%
```

### Đánh Giá Hiệu Suất

- **✅ Excellent**: Return > 20%, Win Rate > 70%, Drawdown < 10%
- **✅ Good**: Return > 10%, Win Rate > 60%, Drawdown < 15%
- **⚠️ Moderate**: Return > 0%, Win Rate > 50%, Drawdown < 25%
- **❌ Poor**: Return < 0% hoặc Win Rate < 50%

## 🔧 Cấu Hình Parameters

### Các Parameters Chính

```javascript
{
  // Thời gian
  startDate: '2024-08-01',
  endDate: '2024-10-31',

  // Assets
  symbols: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
  timeframes: ['5m'],

  // Quản lý vốn
  initialCapital: 10000,
  positionSizePercent: 2,        // 2% mỗi lệnh
  maxConcurrentPositions: 3,     // Tối đa 3 lệnh cùng lúc

  // Chiến lược
  useMarketConditionFilter: true,
  trailingStopEnabled: false,

  // Chi phí giao dịch
  slippage: 0.1,                 // 0.1% slippage
  commission: 0.1,               // 0.1% phí

  // Quản lý rủi ro
  maxDrawdown: 20,               // 20% max drawdown
  dailyLossLimit: 5              // 5% loss limit mỗi ngày
}
```

### Parameters Tối Ưu Hóa

- **positionSizePercent**: [1, 1.5, 2, 2.5, 3]
- **maxConcurrentPositions**: [2, 3, 4, 5]
- **useMarketConditionFilter**: [true, false]
- **maxDrawdown**: [15, 20, 25, 30]
- **slippage/commission**: [0.05, 0.1, 0.15]

## 📁 Cấu Trúc Dữ Liệu

### Thư Mục Data

```
data/
├── raw/                           # Dữ liệu thô từ Binance
│   ├── BTCUSDT_5m_raw.json.gz
│   ├── ETHUSDT_5m_raw.json.gz
│   └── ...
├── processed/                     # Dữ liệu đã xử lý
├── cache/                         # Cache tạm thời
├── backtest-results-*.json        # Kết quả backtest
├── backtest-report-*.json         # Báo cáo chi tiết
├── optimization-results-*.json    # Kết quả tối ưu hóa
└── best-config-*.json            # Config tối ưu nhất
```

### Format Kết Quả

```javascript
{
  "metadata": {
    "generated_at": "2024-11-01T10:00:00Z",
    "backtest_period": {
      "start": "2024-08-01",
      "end": "2024-10-31",
      "duration_days": 92
    }
  },
  "executive_summary": {
    "performance_grade": {
      "grade": "A-",
      "score": "82.5"
    },
    "key_metrics": {
      "total_return": "25.50%",
      "win_rate": "68.33%",
      "profit_factor": "2.15",
      "max_drawdown": "8.50%"
    }
  },
  "analysis": { /* Chi tiết metrics */ },
  "recommendations": [ /* Khuyến nghị */ ]
}
```

## 🎯 Workflow Khuyến Nghị

### 1. Giai Đoạn Khởi Động
```bash
# Download dữ liệu
node scripts/download-historical-data.js

# Test hệ thống
node test-backtest-system.js

# Chạy quick test
node run-full-backtest.js --config quick
```

### 2. Giai Đoạn Phát Triển
```bash
# Test các config khác nhau
node run-full-backtest.js --config standard --verbose
node run-full-backtest.js --config conservative
node run-full-backtest.js --config aggressive

# So sánh kết quả
```

### 3. Giai Đoạn Tối Ưu Hóa
```bash
# Tối ưu parameters
node optimize-backtest.js --mode standard --metric composite

# Test config tối ưu
node run-full-backtest.js --config comprehensive
```

### 4. Giai Đoạn Validation
```bash
# Test trên periods khác nhau
# Thay đổi startDate/endDate trong config
# Forward testing trên dữ liệu gần đây
```

## 🚨 Lưu Ý Quan Trọng

### Giới Hạn của Backtest

1. **Overfitting**: Tối ưu quá mức trên dữ liệu lịch sử
2. **Market Regime**: Thị trường thay đổi theo thời gian
3. **Slippage**: Thực tế có thể khác simulation
4. **Liquidity**: Không tính đến tác động thanh khoản

### Best Practices

1. **Đa dạng hóa**: Test trên nhiều periods và symbols
2. **Out-of-sample**: Giữ lại data để validation
3. **Walk-forward**: Test liên tục trên data mới
4. **Conservative**: Sử dụng parameters bảo thủ hơn

### Troubleshooting

```bash
# Nếu không có dữ liệu
node scripts/download-historical-data.js

# Nếu lỗi memory
# Giảm số symbols hoặc thời gian test

# Nếu lỗi signal analyzer
# Kiểm tra lib/trading/signalAnalyzer.js

# Nếu lỗi market condition
# Tắt useMarketConditionFilter: false
```

## 📞 Hỗ Trợ

### Log Files
- Logs được lưu trong `logs/` directory
- Check logs để debug issues

### Common Issues
1. **No signals generated**: Kiểm tra signal conditions
2. **No trades executed**: Kiểm tra portfolio constraints
3. **Poor performance**: Thử tối ưu parameters
4. **Memory issues**: Giảm data size hoặc tăng RAM

### Performance Tips
- Sử dụng SSD cho data storage
- Tăng RAM nếu test nhiều symbols
- Chạy optimization vào ban đêm
- Backup kết quả quan trọng

---

## 🎉 Kết Luận

Hệ thống backtest này cung cấp framework hoàn chỉnh để:
- ✅ Validate trading strategies
- ✅ Optimize parameters
- ✅ Assess risk metrics
- ✅ Generate detailed reports
- ✅ Compare different approaches

**Happy Backtesting! 🚀📈**