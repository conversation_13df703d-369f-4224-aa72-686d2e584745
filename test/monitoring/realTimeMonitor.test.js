const RealTimeMonitor = require('../../lib/monitoring/realTimeMonitor');
const EventEmitter = require('events');

// Mock dependencies
jest.mock('../../lib/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../lib/trading/performanceMetricsCalculator', () => {
  return jest.fn().mockImplementation(() => ({
    calculateComprehensiveMetrics: jest.fn().mockReturnValue({
      totalTrades: 10,
      winRate: 60,
      profitFactor: 1.5,
      totalPnL: 5.2,
      sharpeRatio: 0.8,
      maxDrawdown: 8.5,
      expectancy: 0.52
    })
  }));
});

describe('RealTimeMonitor', () => {
  let monitor;
  let mockTrades;

  beforeEach(() => {
    monitor = new RealTimeMonitor({
      metricsUpdateInterval: 100, // Fast intervals for testing
      performanceCheckInterval: 150,
      systemHealthInterval: 200,
      thresholds: {
        winRateWarning: 40,
        winRateCritical: 30,
        drawdownWarning: 10,
        drawdownCritical: 15,
        consecutiveLossesWarning: 3,
        consecutiveLossesCritical: 5
      }
    });

    mockTrades = [
      { pnlPercent: 2.5, updatedAt: new Date(), symbol: 'BTCUSDT' },
      { pnlPercent: -1.2, updatedAt: new Date(), symbol: 'ETHUSDT' },
      { pnlPercent: 3.1, updatedAt: new Date(), symbol: 'BTCUSDT' },
      { pnlPercent: -0.8, updatedAt: new Date(), symbol: 'ADAUSDT' },
      { pnlPercent: 1.8, updatedAt: new Date(), symbol: 'BTCUSDT' }
    ];

    // Mock getRecentTrades method
    monitor.getRecentTrades = jest.fn().mockResolvedValue(mockTrades);
  });

  afterEach(async () => {
    if (monitor.isMonitoring) {
      monitor.stopMonitoring();
    }
    // Wait a bit for timers to clear
    await new Promise(resolve => setTimeout(resolve, 50));
  });

  describe('constructor', () => {
    it('should initialize with default configuration', () => {
      const defaultMonitor = new RealTimeMonitor();
      
      expect(defaultMonitor.config.metricsUpdateInterval).toBe(30000);
      expect(defaultMonitor.config.performanceCheckInterval).toBe(60000);
      expect(defaultMonitor.config.thresholds.winRateWarning).toBe(40);
      expect(defaultMonitor.isMonitoring).toBe(false);
    });

    it('should merge custom configuration', () => {
      const customMonitor = new RealTimeMonitor({
        metricsUpdateInterval: 5000,
        thresholds: { winRateWarning: 50 }
      });

      expect(customMonitor.config.metricsUpdateInterval).toBe(5000);
      expect(customMonitor.config.thresholds.winRateWarning).toBe(50);
      expect(customMonitor.config.performanceCheckInterval).toBe(60000); // Default
    });

    it('should extend EventEmitter', () => {
      expect(monitor).toBeInstanceOf(EventEmitter);
    });
  });

  describe('startMonitoring', () => {
    it('should start monitoring successfully', async () => {
      const startSpy = jest.fn();
      monitor.on('monitoring_started', startSpy);

      await monitor.startMonitoring();

      expect(monitor.isMonitoring).toBe(true);
      expect(monitor.metricsTimer).toBeDefined();
      expect(monitor.performanceTimer).toBeDefined();
      expect(monitor.systemHealthTimer).toBeDefined();
      expect(startSpy).toHaveBeenCalled();
    });

    it('should not start if already monitoring', async () => {
      await monitor.startMonitoring();
      const firstTimerId = monitor.metricsTimer;

      await monitor.startMonitoring(); // Second call

      expect(monitor.metricsTimer).toBe(firstTimerId); // Same timer
    });

    it('should handle errors during startup', async () => {
      monitor.updateMetrics = jest.fn().mockRejectedValue(new Error('Test error'));

      await expect(monitor.startMonitoring()).rejects.toThrow('Test error');
      expect(monitor.isMonitoring).toBe(false);
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring successfully', async () => {
      const stopSpy = jest.fn();
      monitor.on('monitoring_stopped', stopSpy);

      await monitor.startMonitoring();
      monitor.stopMonitoring();

      expect(monitor.isMonitoring).toBe(false);
      expect(monitor.metricsTimer).toBeNull();
      expect(monitor.performanceTimer).toBeNull();
      expect(monitor.systemHealthTimer).toBeNull();
      expect(stopSpy).toHaveBeenCalled();
    });

    it('should handle stop when not monitoring', () => {
      expect(() => monitor.stopMonitoring()).not.toThrow();
      expect(monitor.isMonitoring).toBe(false);
    });
  });

  describe('updateMetrics', () => {
    it('should update metrics with trade data', async () => {
      const metricsSpy = jest.fn();
      monitor.on('metrics_updated', metricsSpy);

      await monitor.updateMetrics();

      expect(monitor.currentMetrics).toBeDefined();
      expect(monitor.currentMetrics.totalTrades).toBe(10);
      expect(monitor.currentMetrics.winRate).toBe(60);
      expect(monitor.currentMetrics.consecutiveLosses).toBe(0);
      expect(metricsSpy).toHaveBeenCalledWith(monitor.currentMetrics);
    });

    it('should handle empty trades', async () => {
      monitor.getRecentTrades.mockResolvedValue([]);

      await monitor.updateMetrics();

      expect(monitor.currentMetrics.totalTrades).toBe(0);
      expect(monitor.currentMetrics.winRate).toBe(0);
      expect(monitor.currentMetrics.profitFactor).toBe(0);
    });

    it('should add metrics to performance history', async () => {
      await monitor.updateMetrics();

      expect(monitor.performanceHistory).toHaveLength(1);
      expect(monitor.performanceHistory[0]).toMatchObject({
        winRate: 60,
        profitFactor: 1.5,
        totalPnL: 5.2
      });
    });
  });

  describe('checkPerformance', () => {
    beforeEach(async () => {
      await monitor.updateMetrics(); // Initialize current metrics
    });

    it('should trigger warning alert for low win rate', async () => {
      const alertSpy = jest.fn();
      monitor.on('alert', alertSpy);

      // Set win rate below warning threshold
      monitor.currentMetrics.winRate = 35;

      await monitor.checkPerformance();

      expect(alertSpy).toHaveBeenCalled();
      const alert = alertSpy.mock.calls[0][0];
      expect(alert.severity).toBe('warning');
      expect(alert.metric).toBe('win_rate');
      expect(alert.value).toBe(35);
    });

    it('should trigger critical alert for very low win rate', async () => {
      const alertSpy = jest.fn();
      monitor.on('alert', alertSpy);

      monitor.currentMetrics.winRate = 25;

      await monitor.checkPerformance();

      expect(alertSpy).toHaveBeenCalled();
      const alert = alertSpy.mock.calls[0][0];
      expect(alert.severity).toBe('critical');
      expect(alert.metric).toBe('win_rate');
    });

    it('should trigger alert for high drawdown', async () => {
      const alertSpy = jest.fn();
      monitor.on('alert', alertSpy);

      monitor.currentMetrics.maxDrawdown = 12;

      await monitor.checkPerformance();

      expect(alertSpy).toHaveBeenCalled();
      const alert = alertSpy.mock.calls[0][0];
      expect(alert.metric).toBe('max_drawdown');
      expect(alert.value).toBe(12);
    });

    it('should trigger alert for consecutive losses', async () => {
      const alertSpy = jest.fn();
      monitor.on('alert', alertSpy);

      monitor.consecutiveLosses = 4;

      await monitor.checkPerformance();

      expect(alertSpy).toHaveBeenCalled();
      const alert = alertSpy.mock.calls[0][0];
      expect(alert.metric).toBe('consecutive_losses');
      expect(alert.value).toBe(4);
    });

    it('should not trigger alerts when metrics are good', async () => {
      const alertSpy = jest.fn();
      monitor.on('alert', alertSpy);

      // Good metrics (defaults from mock)
      await monitor.checkPerformance();

      expect(alertSpy).not.toHaveBeenCalled();
    });

    it('should handle missing current metrics', async () => {
      monitor.currentMetrics = null;

      await expect(monitor.checkPerformance()).resolves.not.toThrow();
    });
  });

  describe('updateTradeResult', () => {
    it('should reset consecutive losses on winning trade', () => {
      const tradeSpy = jest.fn();
      monitor.on('trade_result', tradeSpy);

      monitor.consecutiveLosses = 3;
      monitor.updateTradeResult({ pnlPercent: 2.5, symbol: 'BTCUSDT' });

      expect(monitor.consecutiveLosses).toBe(0);
      expect(monitor.lastTradeResult.pnlPercent).toBe(2.5);
      expect(tradeSpy).toHaveBeenCalledWith({
        result: { pnlPercent: 2.5, symbol: 'BTCUSDT' },
        consecutiveLosses: 0
      });
    });

    it('should increment consecutive losses on losing trade', () => {
      const tradeSpy = jest.fn();
      monitor.on('trade_result', tradeSpy);

      monitor.consecutiveLosses = 2;
      monitor.updateTradeResult({ pnlPercent: -1.5, symbol: 'ETHUSDT' });

      expect(monitor.consecutiveLosses).toBe(3);
      expect(tradeSpy).toHaveBeenCalledWith({
        result: { pnlPercent: -1.5, symbol: 'ETHUSDT' },
        consecutiveLosses: 3
      });
    });

    it('should not change consecutive losses on breakeven trade', () => {
      monitor.consecutiveLosses = 2;
      monitor.updateTradeResult({ pnlPercent: 0, symbol: 'BTCUSDT' });

      expect(monitor.consecutiveLosses).toBe(2);
    });
  });

  describe('processAlert', () => {
    it('should process alert and add to history', async () => {
      const alert = {
        type: 'performance',
        severity: 'warning',
        metric: 'win_rate',
        value: 35,
        message: 'Low win rate'
      };

      await monitor.processAlert(alert);

      expect(monitor.alertHistory).toHaveLength(1);
      expect(monitor.alertHistory[0]).toMatchObject({
        type: 'performance',
        severity: 'warning',
        metric: 'win_rate'
      });
      expect(monitor.alertHistory[0].id).toBeDefined();
      expect(monitor.alertHistory[0].timestamp).toBeInstanceOf(Date);
    });

    it('should skip duplicate alerts', async () => {
      const alert = {
        type: 'performance',
        severity: 'warning',
        metric: 'win_rate',
        value: 35,
        message: 'Low win rate'
      };

      await monitor.processAlert(alert);
      await monitor.processAlert(alert); // Duplicate

      expect(monitor.alertHistory).toHaveLength(1);
    });
  });

  describe('getTradesInTimeframe', () => {
    it('should filter trades by timeframe', () => {
      const now = new Date();
      const trades = [
        { updatedAt: new Date(now.getTime() - 30 * 60 * 1000) }, // 30 min ago
        { updatedAt: new Date(now.getTime() - 90 * 60 * 1000) }, // 90 min ago
        { updatedAt: new Date(now.getTime() - 10 * 60 * 1000) }  // 10 min ago
      ];

      const result = monitor.getTradesInTimeframe(trades, 1); // Last 1 hour

      expect(result).toBe(2); // Only 2 trades within last hour
    });
  });

  describe('getSystemHealth', () => {
    it('should return system health metrics', async () => {
      const health = await monitor.getSystemHealth();

      expect(health).toHaveProperty('timestamp');
      expect(health).toHaveProperty('memoryUsage');
      expect(health).toHaveProperty('cpuUsage');
      expect(health).toHaveProperty('uptime');
      expect(health).toHaveProperty('nodeVersion');
      expect(health).toHaveProperty('platform');
    });

    it('should handle errors gracefully', async () => {
      // Mock process to throw error
      const originalProcess = global.process;
      global.process = { memoryUsage: () => { throw new Error('Test error'); } };

      const health = await monitor.getSystemHealth();

      expect(health.error).toBeDefined();
      expect(health.memoryUsage).toBe(0);

      global.process = originalProcess;
    });
  });

  describe('utility methods', () => {
    it('should generate unique alert IDs', () => {
      const id1 = monitor.generateAlertId();
      const id2 = monitor.generateAlertId();

      expect(id1).toMatch(/^alert_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^alert_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('should get monitoring status', () => {
      monitor.currentMetrics = { winRate: 60 };
      monitor.consecutiveLosses = 2;

      const status = monitor.getMonitoringStatus();

      expect(status.isMonitoring).toBe(false);
      expect(status.currentMetrics.winRate).toBe(60);
      expect(status.consecutiveLosses).toBe(2);
    });

    it('should get performance history with time filter', () => {
      const now = new Date();
      monitor.performanceHistory = [
        { timestamp: new Date(now.getTime() - 30 * 60 * 1000), winRate: 60 },
        { timestamp: new Date(now.getTime() - 90 * 60 * 1000), winRate: 55 },
        { timestamp: new Date(now.getTime() - 10 * 60 * 1000), winRate: 65 }
      ];

      const history = monitor.getPerformanceHistory(1); // Last 1 hour

      expect(history).toHaveLength(2);
      expect(history.every(entry => entry.timestamp >= new Date(now.getTime() - 60 * 60 * 1000))).toBe(true);
    });

    it('should get alert history with time filter', () => {
      const now = new Date();
      monitor.alertHistory = [
        { timestamp: new Date(now.getTime() - 30 * 60 * 1000), severity: 'warning' },
        { timestamp: new Date(now.getTime() - 90 * 60 * 1000), severity: 'critical' },
        { timestamp: new Date(now.getTime() - 10 * 60 * 1000), severity: 'info' }
      ];

      const history = monitor.getAlertHistory(1); // Last 1 hour

      expect(history).toHaveLength(2);
      expect(history.every(alert => alert.timestamp >= new Date(now.getTime() - 60 * 60 * 1000))).toBe(true);
    });

    it('should clear alert history', () => {
      monitor.alertHistory = [
        { id: 'alert1', severity: 'warning' },
        { id: 'alert2', severity: 'critical' }
      ];

      monitor.clearAlertHistory();

      expect(monitor.alertHistory).toHaveLength(0);
    });

    it('should update configuration', () => {
      const newConfig = {
        thresholds: { winRateWarning: 45 },
        metricsUpdateInterval: 5000
      };

      monitor.updateConfig(newConfig);

      expect(monitor.config.thresholds.winRateWarning).toBe(45);
      expect(monitor.config.metricsUpdateInterval).toBe(5000);
    });
  });

  describe('data retention', () => {
    it('should limit performance history size', async () => {
      // Mock config for short retention
      monitor.config.dataRetention.performanceHistory = 0.001; // Very short retention

      // Add multiple entries
      for (let i = 0; i < 5; i++) {
        await monitor.updateMetrics();
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      expect(monitor.performanceHistory.length).toBeLessThan(5);
    });
  });
});
