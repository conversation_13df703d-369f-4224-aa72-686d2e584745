const AIOptimizationEngine = require('../../lib/optimization/aiOptimizationEngine');
const EventEmitter = require('events');

// Mock dependencies
jest.mock('../../lib/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../lib/ai/aiClient', () => {
  return jest.fn().mockImplementation(() => ({
    analyze: jest.fn().mockResolvedValue({
      confidenceScore: 80,
      insights: ['Test insight'],
      recommendations: ['Test recommendation'],
      parameterSuggestions: { 'riskManagement.stopLossPercent': 0.6 },
      strategySuggestions: {},
      riskAssessment: { level: 'medium' },
      expectedImprovement: 0.08,
      implementationPriority: 'high'
    })
  }));
});

jest.mock('../../lib/optimization/parameterOptimizer', () => {
  return jest.fn().mockImplementation(() => ({
    optimize: jest.fn().mockResolvedValue({
      bestParameters: { riskManagement: { stopLossPercent: 0.6 } },
      improvement: 0.12,
      backtestResults: { winRate: 65, profitFactor: 1.8 },
      confidence: 0.85
    })
  }));
});

jest.mock('../../lib/optimization/strategyOptimizer', () => {
  return jest.fn().mockImplementation(() => ({
    optimize: jest.fn().mockResolvedValue({
      bestStrategy: { name: 'optimized_strategy' },
      improvement: 0.08,
      backtestResults: { winRate: 62, profitFactor: 1.6 },
      confidence: 0.75
    })
  }));
});

jest.mock('../../lib/optimization/backtestEngine', () => {
  return jest.fn().mockImplementation(() => ({
    validateParameters: jest.fn().mockResolvedValue({
      improvement: 0.10,
      metrics: { winRate: 63, profitFactor: 1.7 },
      confidence: 0.80
    }),
    validateStrategy: jest.fn().mockResolvedValue({
      improvement: 0.06,
      metrics: { winRate: 60, profitFactor: 1.5 },
      confidence: 0.70
    }),
    validateCombined: jest.fn().mockResolvedValue({
      improvement: 0.08,
      metrics: { winRate: 65, profitFactor: 1.8 },
      confidence: 0.75
    })
  }));
});

jest.mock('../../lib/models/tradingSignal', () => ({
  find: jest.fn().mockResolvedValue([
    { _id: '1', symbol: 'BTCUSDT', pnlPercent: 2.5, createdAt: new Date() },
    { _id: '2', symbol: 'ETHUSDT', pnlPercent: -1.2, createdAt: new Date() },
    { _id: '3', symbol: 'BTCUSDT', pnlPercent: 3.1, createdAt: new Date() }
  ])
}));

describe('AIOptimizationEngine', () => {
  let engine;

  beforeEach(() => {
    engine = new AIOptimizationEngine({
      optimization: {
        optimizationInterval: 100, // Fast interval for testing
        minTradesForOptimization: 2
      },
      ai: {
        confidenceThreshold: 70,
        autoImplementThreshold: 85
      },
      safety: {
        cooldownPeriod: 50 // Short cooldown for testing
      }
    });

    // Mock implementation methods
    engine.implementParameterChanges = jest.fn().mockResolvedValue({ success: true });
    engine.implementStrategyChanges = jest.fn().mockResolvedValue({ success: true });
  });

  afterEach(async () => {
    if (engine.isOptimizing) {
      engine.stopOptimization();
    }
    await new Promise(resolve => setTimeout(resolve, 10));
  });

  describe('constructor', () => {
    it('should initialize with default configuration', () => {
      const defaultEngine = new AIOptimizationEngine();
      
      expect(defaultEngine.config.ai.enabled).toBe(true);
      expect(defaultEngine.config.optimization.minTradesForOptimization).toBe(100);
      expect(defaultEngine.isOptimizing).toBe(false);
    });

    it('should merge custom configuration', () => {
      const customEngine = new AIOptimizationEngine({
        ai: { confidenceThreshold: 80 },
        optimization: { minTradesForOptimization: 50 }
      });

      expect(customEngine.config.ai.confidenceThreshold).toBe(80);
      expect(customEngine.config.optimization.minTradesForOptimization).toBe(50);
    });

    it('should extend EventEmitter', () => {
      expect(engine).toBeInstanceOf(EventEmitter);
    });
  });

  describe('startOptimization', () => {
    it('should start optimization successfully', async () => {
      const startSpy = jest.fn();
      engine.on('optimization_started', startSpy);

      await engine.startOptimization();

      expect(engine.isOptimizing).toBe(true);
      expect(engine.performanceBaseline).toBeDefined();
      expect(startSpy).toHaveBeenCalled();
    });

    it('should not start if already optimizing', async () => {
      await engine.startOptimization();
      const firstBaseline = engine.performanceBaseline;

      await engine.startOptimization(); // Second call

      expect(engine.performanceBaseline).toBe(firstBaseline);
    });

    it('should handle errors during startup', async () => {
      engine.initializeBaseline = jest.fn().mockRejectedValue(new Error('Test error'));

      await expect(engine.startOptimization()).rejects.toThrow('Test error');
      expect(engine.isOptimizing).toBe(false);
    });
  });

  describe('stopOptimization', () => {
    it('should stop optimization successfully', async () => {
      const stopSpy = jest.fn();
      engine.on('optimization_stopped', stopSpy);

      await engine.startOptimization();
      engine.stopOptimization();

      expect(engine.isOptimizing).toBe(false);
      expect(stopSpy).toHaveBeenCalled();
    });
  });

  describe('runOptimization', () => {
    beforeEach(async () => {
      await engine.startOptimization();
    });

    it('should run comprehensive optimization successfully', async () => {
      const completedSpy = jest.fn();
      engine.on('optimization_completed', completedSpy);

      const result = await engine.runOptimization({ type: 'manual' });

      expect(result.success).toBe(true);
      expect(result.type).toBe('manual');
      expect(result.aiAnalysis).toBeDefined();
      expect(result.parameterResults).toBeDefined();
      expect(result.strategyResults).toBeDefined();
      expect(result.validationResults).toBeDefined();
      expect(result.implementationResults).toBeDefined();
      expect(completedSpy).toHaveBeenCalled();
    });

    it('should skip optimization due to insufficient data', async () => {
      // Mock insufficient trades
      const TradingSignal = require('../../lib/models/tradingSignal');
      TradingSignal.find.mockResolvedValueOnce([]);

      const result = await engine.runOptimization();

      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('insufficient_data');
    });

    it('should skip optimization during cooldown', async () => {
      // Set last optimization time to recent
      engine.lastOptimizationTime = new Date();

      const result = await engine.runOptimization();

      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('cooldown');
    });

    it('should skip optimization after max consecutive optimizations', async () => {
      engine.consecutiveOptimizations = 5; // Exceed limit

      const result = await engine.runOptimization();

      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('max_consecutive');
    });

    it('should handle optimization errors', async () => {
      const errorSpy = jest.fn();
      engine.on('optimization_error', errorSpy);

      engine.gatherPerformanceData = jest.fn().mockRejectedValue(new Error('Test error'));

      await expect(engine.runOptimization()).rejects.toThrow('Test error');
      expect(errorSpy).toHaveBeenCalled();
    });
  });

  describe('performAIAnalysis', () => {
    it('should perform AI analysis successfully', async () => {
      const performanceData = {
        metrics: { winRate: 55, profitFactor: 1.3 },
        totalTrades: 100
      };

      const result = await engine.performAIAnalysis(performanceData);

      expect(result.source).toBe('ai');
      expect(result.confidence).toBe(80);
      expect(result.insights).toHaveLength(1);
      expect(result.recommendations).toHaveLength(1);
    });

    it('should use fallback analysis when AI confidence is low', async () => {
      engine.aiClient.analyze.mockResolvedValueOnce({ confidenceScore: 30 });

      const performanceData = {
        metrics: { winRate: 55, profitFactor: 1.3 }
      };

      const result = await engine.performAIAnalysis(performanceData);

      expect(result.source).toBe('fallback');
      expect(result.confidence).toBe(60);
    });

    it('should use fallback analysis when AI is disabled', async () => {
      engine.config.ai.enabled = false;

      const performanceData = {
        metrics: { winRate: 55, profitFactor: 1.3 }
      };

      const result = await engine.performAIAnalysis(performanceData);

      expect(result.source).toBe('fallback');
    });
  });

  describe('optimizeParameters', () => {
    it('should optimize parameters successfully', async () => {
      const performanceData = { metrics: { winRate: 55 } };
      const aiAnalysis = { parameterSuggestions: { stopLoss: 0.6 } };

      const result = await engine.optimizeParameters(performanceData, aiAnalysis);

      expect(result.success).toBe(true);
      expect(result.optimizedParameters).toBeDefined();
      expect(result.improvement).toBe(0.12);
      expect(result.confidence).toBe(0.85);
    });

    it('should handle parameter optimization errors', async () => {
      engine.parameterOptimizer.optimize.mockRejectedValueOnce(new Error('Optimization failed'));

      const performanceData = { metrics: { winRate: 55 } };
      const aiAnalysis = { parameterSuggestions: {} };

      const result = await engine.optimizeParameters(performanceData, aiAnalysis);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Optimization failed');
    });
  });

  describe('optimizeStrategy', () => {
    it('should optimize strategy successfully', async () => {
      const performanceData = { metrics: { winRate: 55 } };
      const aiAnalysis = { strategySuggestions: { indicators: {} } };

      const result = await engine.optimizeStrategy(performanceData, aiAnalysis);

      expect(result.success).toBe(true);
      expect(result.optimizedStrategy).toBeDefined();
      expect(result.improvement).toBe(0.08);
      expect(result.confidence).toBe(0.75);
    });

    it('should handle strategy optimization errors', async () => {
      engine.strategyOptimizer.optimize.mockRejectedValueOnce(new Error('Strategy optimization failed'));

      const performanceData = { metrics: { winRate: 55 } };
      const aiAnalysis = { strategySuggestions: {} };

      const result = await engine.optimizeStrategy(performanceData, aiAnalysis);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Strategy optimization failed');
    });
  });

  describe('validateOptimizations', () => {
    it('should validate optimizations successfully', async () => {
      const parameterResults = {
        success: true,
        optimizedParameters: { stopLoss: 0.6 }
      };
      const strategyResults = {
        success: true,
        optimizedStrategy: { name: 'test' }
      };
      const performanceData = { metrics: { winRate: 55 } };

      const result = await engine.validateOptimizations(parameterResults, strategyResults, performanceData);

      expect(result.parameters.valid).toBe(true);
      expect(result.strategy.valid).toBe(true);
      expect(result.combined.valid).toBe(true);
    });

    it('should handle validation errors', async () => {
      engine.backtestEngine.validateParameters.mockRejectedValueOnce(new Error('Validation failed'));

      const parameterResults = { success: true, optimizedParameters: {} };
      const strategyResults = { success: false };
      const performanceData = { metrics: { winRate: 55 } };

      const result = await engine.validateOptimizations(parameterResults, strategyResults, performanceData);

      expect(result.parameters.valid).toBe(false);
      expect(result.parameters.error).toBe('Validation failed');
    });
  });

  describe('implementOptimizations', () => {
    it('should implement optimizations when AI confidence is high', async () => {
      const validationResults = {
        parameters: { valid: true, improvement: 0.10 },
        strategy: { valid: true, improvement: 0.08 }
      };
      const aiAnalysis = { confidence: 90 }; // Above auto-implement threshold

      const result = await engine.implementOptimizations(validationResults, aiAnalysis);

      expect(result.implemented).toBe(2);
      expect(result.skipped).toBe(0);
      expect(result.failed).toBe(0);
      expect(result.changes).toHaveLength(2);
    });

    it('should skip implementation when AI confidence is low', async () => {
      const validationResults = {
        parameters: { valid: true, improvement: 0.10 },
        strategy: { valid: true, improvement: 0.08 }
      };
      const aiAnalysis = { confidence: 70 }; // Below auto-implement threshold

      const result = await engine.implementOptimizations(validationResults, aiAnalysis);

      expect(result.implemented).toBe(0);
      expect(result.skipped).toBe(1);
    });

    it('should handle implementation failures', async () => {
      engine.implementParameterChanges.mockRejectedValueOnce(new Error('Implementation failed'));

      const validationResults = {
        parameters: { valid: true, improvement: 0.10 }
      };
      const aiAnalysis = { confidence: 90 };

      const result = await engine.implementOptimizations(validationResults, aiAnalysis);

      expect(result.implemented).toBe(0);
      expect(result.failed).toBe(1);
    });
  });

  describe('performance monitoring', () => {
    beforeEach(async () => {
      await engine.startOptimization();
    });

    it('should calculate performance change correctly', () => {
      const baseline = { winRate: 50, profitFactor: 1.5, sharpeRatio: 0.5, maxDrawdown: 10 };
      const current = { winRate: 55, profitFactor: 1.8, sharpeRatio: 0.7, maxDrawdown: 8 };

      const change = engine.calculatePerformanceChange(baseline, current);

      expect(change).toBeGreaterThan(0); // Should show improvement
    });

    it('should detect performance degradation', async () => {
      // Set baseline
      engine.performanceBaseline = {
        metrics: { winRate: 60, profitFactor: 2.0, sharpeRatio: 1.0, maxDrawdown: 5 }
      };

      // Mock poor current performance
      engine.gatherPerformanceData = jest.fn().mockResolvedValue({
        metrics: { winRate: 40, profitFactor: 1.0, sharpeRatio: 0.2, maxDrawdown: 20 }
      });

      engine.performRollback = jest.fn().mockResolvedValue();

      await engine.monitorPerformance();

      expect(engine.performRollback).toHaveBeenCalled();
    });
  });

  describe('utility methods', () => {
    it('should generate unique optimization IDs', () => {
      const id1 = engine.generateOptimizationId();
      const id2 = engine.generateOptimizationId();

      expect(id1).toMatch(/^opt_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^opt_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('should check cooldown status correctly', () => {
      expect(engine.isInCooldown()).toBe(false);

      engine.lastOptimizationTime = new Date();
      expect(engine.isInCooldown()).toBe(true);

      engine.lastOptimizationTime = new Date(Date.now() - 100); // Past cooldown
      expect(engine.isInCooldown()).toBe(false);
    });

    it('should enter cooldown correctly', () => {
      engine.consecutiveOptimizations = 3;
      engine.enterCooldown();

      expect(engine.consecutiveOptimizations).toBe(0);
      expect(engine.lastOptimizationTime).toBeInstanceOf(Date);
    });

    it('should get optimization status', () => {
      const status = engine.getOptimizationStatus();

      expect(status.isOptimizing).toBe(false);
      expect(status.consecutiveOptimizations).toBe(0);
      expect(status.isInCooldown).toBe(false);
      expect(status.optimizationHistory).toBeInstanceOf(Array);
    });

    it('should update configuration', () => {
      const newConfig = {
        ai: { confidenceThreshold: 80 },
        optimization: { minTradesForOptimization: 150 }
      };

      engine.updateConfig(newConfig);

      expect(engine.config.ai.confidenceThreshold).toBe(80);
      expect(engine.config.optimization.minTradesForOptimization).toBe(150);
    });
  });

  describe('data analysis methods', () => {
    const mockTrades = [
      { symbol: 'BTCUSDT', pnlPercent: 2.5, createdAt: new Date() },
      { symbol: 'ETHUSDT', pnlPercent: -1.2, createdAt: new Date() },
      { symbol: 'BTCUSDT', pnlPercent: 3.1, createdAt: new Date() }
    ];

    it('should analyze symbol performance', () => {
      const result = engine.analyzeSymbolPerformance(mockTrades);

      expect(result).toHaveLength(2); // BTCUSDT and ETHUSDT
      expect(result[0]).toHaveProperty('symbol');
      expect(result[0]).toHaveProperty('winRate');
      expect(result[0]).toHaveProperty('avgPnL');
    });

    it('should analyze timeframe performance', () => {
      const tradesWithTimeframe = mockTrades.map(t => ({ ...t, timeframe: '1h' }));
      const result = engine.analyzeTimeframePerformance(tradesWithTimeframe);

      expect(result).toHaveLength(1); // Only 1h timeframe
      expect(result[0].timeframe).toBe('1h');
      expect(result[0]).toHaveProperty('winRate');
    });

    it('should analyze trading patterns', () => {
      const result = engine.analyzePatterns(mockTrades);

      expect(result).toHaveProperty('hourlyDistribution');
      expect(result).toHaveProperty('dayOfWeekDistribution');
      expect(result).toHaveProperty('consecutiveWins');
      expect(result).toHaveProperty('consecutiveLosses');
    });
  });

  describe('rollback functionality', () => {
    beforeEach(async () => {
      await engine.startOptimization();
    });

    it('should add to rollback stack', () => {
      const optimizationRecord = {
        id: 'test-opt-1',
        timestamp: new Date(),
        parameterResults: { currentParameters: { test: 'param' } },
        strategyResults: { currentStrategy: { test: 'strategy' } }
      };

      engine.addToRollbackStack(optimizationRecord);

      expect(engine.rollbackStack).toHaveLength(1);
      expect(engine.rollbackStack[0].id).toBe('test-opt-1');
    });

    it('should limit rollback stack size', () => {
      // Add 7 records (more than limit of 5)
      for (let i = 0; i < 7; i++) {
        engine.addToRollbackStack({
          id: `test-opt-${i}`,
          timestamp: new Date(),
          parameterResults: { currentParameters: {} },
          strategyResults: { currentStrategy: {} }
        });
      }

      expect(engine.rollbackStack).toHaveLength(5);
      expect(engine.rollbackStack[0].id).toBe('test-opt-2'); // First two should be removed
    });

    it('should perform rollback successfully', async () => {
      const rollbackSpy = jest.fn();
      engine.on('rollback_performed', rollbackSpy);

      // Add rollback configuration
      engine.rollbackStack.push({
        id: 'test-rollback',
        timestamp: new Date(),
        previousParameters: { test: 'param' },
        previousStrategy: { test: 'strategy' }
      });

      await engine.performRollback();

      expect(engine.implementParameterChanges).toHaveBeenCalled();
      expect(engine.implementStrategyChanges).toHaveBeenCalled();
      expect(rollbackSpy).toHaveBeenCalled();
      expect(engine.rollbackStack).toHaveLength(0);
    });

    it('should handle rollback when no configuration available', async () => {
      engine.rollbackStack = [];

      await expect(engine.performRollback()).resolves.not.toThrow();
    });
  });
});
