const { describe, it, expect, beforeEach, vi } = require('vitest');
const MarketRegimeDetector = require('../../lib/trading/marketRegimeDetector');

// Mock logger
vi.mock('../../lib/logger');

describe('MarketRegimeDetector', () => {
  let detector;
  let mockCandles;

  beforeEach(() => {
    detector = new MarketRegimeDetector();
  });

  const createTrendingCandles = (length = 60) => {
    const candles = [];
    let price = 100;

    for (let i = 0; i < length; i++) {
      const trend = 0.5; // Consistent upward trend
      price += trend + (Math.random() - 0.5) * 0.2; // Small random variation

      candles.push({
        openTime: new Date(Date.now() - (length - i) * 60000),
        open: price - 0.1,
        high: price + 0.3,
        low: price - 0.3,
        close: price,
        volume: 1000 + Math.random() * 200
      });
    }

    return candles;
  };

  const createSidewaysCandles = (length = 60) => {
    const candles = [];
    const basePrice = 100;

    for (let i = 0; i < length; i++) {
      const price = basePrice + (Math.random() - 0.5) * 2; // Random within ±1

      candles.push({
        openTime: new Date(Date.now() - (length - i) * 60000),
        open: price - 0.1,
        high: price + 0.2,
        low: price - 0.2,
        close: price,
        volume: 1000 + Math.random() * 200
      });
    }

    return candles;
  };

  const createVolatileCandles = (length = 60) => {
    const candles = [];
    let price = 100;

    for (let i = 0; i < length; i++) {
      const volatileMove = (Math.random() - 0.5) * 4; // Large random moves
      price += volatileMove;

      candles.push({
        openTime: new Date(Date.now() - (length - i) * 60000),
        open: price - Math.abs(volatileMove) * 0.5,
        high: price + Math.abs(volatileMove),
        low: price - Math.abs(volatileMove),
        close: price,
        volume: 1000 + Math.random() * 500
      });
    }

    return candles;
  };

  describe('detectMarketRegime', () => {
    it('should return unknown for insufficient data', async () => {
      const smallCandles = createTrendingCandles(20);

      const result = await detector.detectMarketRegime(smallCandles);

      expect(result.regime).toBe('unknown');
      expect(result.confidence).toBe(0);
      expect(result.reason).toContain('Insufficient data');
    });

    it('should detect trending market', async () => {
      const trendingCandles = createTrendingCandles(60);

      const result = await detector.detectMarketRegime(trendingCandles);

      expect(result.regime).toBe('trending');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.metrics.trendStrength).toBeGreaterThan(0.6);
    });

    it('should detect sideways market', async () => {
      const sidewaysCandles = createSidewaysCandles(60);

      const result = await detector.detectMarketRegime(sidewaysCandles);

      expect(result.regime).toBe('sideways');
      expect(result.confidence).toBeGreaterThan(0.3);
      expect(result.metrics.trendStrength).toBeLessThan(0.4);
    });

    it('should detect volatile market', async () => {
      const volatileCandles = createVolatileCandles(60);

      const result = await detector.detectMarketRegime(volatileCandles);

      expect(result.regime).toBe('volatile');
      expect(result.confidence).toBeGreaterThan(0.3);
      expect(result.metrics.volatility).toBeGreaterThan(0.05);
    });

    it('should handle errors gracefully', async () => {
      const result = await detector.detectMarketRegime(null);

      expect(result.regime).toBe('error');
      expect(result.confidence).toBe(0);
      expect(result.reason).toContain('Error in regime detection');
    });
  });

  describe('calculateRegimeMetrics', () => {
    it('should calculate all required metrics', () => {
      const candles = createTrendingCandles(60);

      const metrics = detector.calculateRegimeMetrics(candles);

      expect(metrics).toHaveProperty('trendStrength');
      expect(metrics).toHaveProperty('volatility');
      expect(metrics).toHaveProperty('directionalMovement');
      expect(metrics).toHaveProperty('priceRange');
      expect(metrics).toHaveProperty('momentum');
      expect(metrics).toHaveProperty('volumeTrend');
      expect(metrics.candleCount).toBe(50); // Min of 50 and candles.length
    });

    it('should use correct lookback period', () => {
      const longCandles = createTrendingCandles(100);

      const metrics = detector.calculateRegimeMetrics(longCandles);

      expect(metrics.candleCount).toBe(50); // Should cap at 50
    });
  });

  describe('calculateTrendStrength', () => {
    it('should return high trend strength for trending market', () => {
      const trendingCandles = createTrendingCandles(60);

      const strength = detector.calculateTrendStrength(trendingCandles);

      expect(strength).toBeGreaterThan(0.5);
    });

    it('should return low trend strength for sideways market', () => {
      const sidewaysCandles = createSidewaysCandles(60);

      const strength = detector.calculateTrendStrength(sidewaysCandles);

      expect(strength).toBeLessThan(0.5);
    });

    it('should handle insufficient data', () => {
      const smallCandles = createTrendingCandles(10);

      const strength = detector.calculateTrendStrength(smallCandles);

      expect(strength).toBe(0);
    });
  });

  describe('calculateVolatility', () => {
    it('should return high volatility for volatile market', () => {
      const volatileCandles = createVolatileCandles(60);

      const volatility = detector.calculateVolatility(volatileCandles);

      expect(volatility).toBeGreaterThan(0.03);
    });

    it('should return low volatility for stable market', () => {
      const stableCandles = createSidewaysCandles(60);

      const volatility = detector.calculateVolatility(stableCandles);

      expect(volatility).toBeLessThan(0.05);
    });

    it('should handle single candle', () => {
      const singleCandle = createTrendingCandles(1);

      const volatility = detector.calculateVolatility(singleCandle);

      expect(volatility).toBe(0);
    });
  });

  describe('calculateDirectionalMovement', () => {
    it('should return high directional movement for trending market', () => {
      const trendingCandles = createTrendingCandles(60);

      const directional = detector.calculateDirectionalMovement(trendingCandles);

      expect(directional).toBeGreaterThan(0.3);
    });

    it('should return low directional movement for sideways market', () => {
      const sidewaysCandles = createSidewaysCandles(60);

      const directional = detector.calculateDirectionalMovement(sidewaysCandles);

      expect(directional).toBeLessThan(0.5);
    });
  });

  describe('calculatePriceRangeEfficiency', () => {
    it('should return high efficiency for trending market', () => {
      const trendingCandles = createTrendingCandles(60);

      const efficiency = detector.calculatePriceRangeEfficiency(trendingCandles);

      expect(efficiency).toBeGreaterThan(0.3);
    });

    it('should return low efficiency for sideways market', () => {
      const sidewaysCandles = createSidewaysCandles(60);

      const efficiency = detector.calculatePriceRangeEfficiency(sidewaysCandles);

      expect(efficiency).toBeLessThan(0.3);
    });
  });

  describe('classifyRegime', () => {
    it('should classify as volatile when volatility is high', () => {
      const metrics = {
        trendStrength: 0.4,
        volatility: 0.06, // High volatility
        directionalMovement: 0.5,
        priceRange: 0.3
      };

      const result = detector.classifyRegime(metrics);

      expect(result.regime).toBe('volatile');
      expect(result.reason).toContain('High volatility');
    });

    it('should classify as trending when conditions are met', () => {
      const metrics = {
        trendStrength: 0.7, // High trend strength
        volatility: 0.02,   // Low volatility
        directionalMovement: 0.8, // High directional movement
        priceRange: 0.6
      };

      const result = detector.classifyRegime(metrics);

      expect(result.regime).toBe('trending');
      expect(result.reason).toContain('Strong trend');
    });

    it('should classify as sideways when conditions are met', () => {
      const metrics = {
        trendStrength: 0.2,    // Low trend strength
        volatility: 0.02,      // Low volatility
        directionalMovement: 0.3, // Low directional movement
        priceRange: 0.2
      };

      const result = detector.classifyRegime(metrics);

      expect(result.regime).toBe('sideways');
      expect(result.reason).toContain('Sideways market');
    });

    it('should classify as mixed for ambiguous conditions', () => {
      const metrics = {
        trendStrength: 0.5,
        volatility: 0.03,
        directionalMovement: 0.5,
        priceRange: 0.4
      };

      const result = detector.classifyRegime(metrics);

      expect(result.regime).toBe('mixed');
      expect(result.reason).toContain('mixed characteristics');
    });
  });

  describe('calculateConfidence', () => {
    it('should return high confidence for clear trending regime', () => {
      const metrics = {
        trendStrength: 0.8,
        volatility: 0.01,
        directionalMovement: 0.9,
        priceRange: 0.7
      };

      const confidence = detector.calculateConfidence(metrics, 'trending');

      expect(confidence).toBeGreaterThan(0.7);
    });

    it('should return high confidence for clear sideways regime', () => {
      const metrics = {
        trendStrength: 0.1,
        volatility: 0.01,
        directionalMovement: 0.2,
        priceRange: 0.1
      };

      const confidence = detector.calculateConfidence(metrics, 'sideways');

      expect(confidence).toBeGreaterThan(0.6);
    });

    it('should return low confidence for mixed regime', () => {
      const metrics = {
        trendStrength: 0.5,
        volatility: 0.03,
        directionalMovement: 0.5,
        priceRange: 0.4
      };

      const confidence = detector.calculateConfidence(metrics, 'mixed');

      expect(confidence).toBe(0.3);
    });
  });

  describe('getRegimeRecommendations', () => {
    it('should return trend following recommendations for trending regime', () => {
      const recommendations = detector.getRegimeRecommendations('trending', 0.8);

      expect(recommendations.strategy).toBe('trend_following');
      expect(recommendations.riskMultiplier).toBeGreaterThan(1.0);
      expect(recommendations.filters).toContain('momentum_confirmation');
    });

    it('should return mean reversion recommendations for sideways regime', () => {
      const recommendations = detector.getRegimeRecommendations('sideways', 0.7);

      expect(recommendations.strategy).toBe('mean_reversion');
      expect(recommendations.riskMultiplier).toBeLessThan(1.0);
      expect(recommendations.filters).toContain('range_boundaries');
    });

    it('should return avoid trading recommendations for volatile regime', () => {
      const recommendations = detector.getRegimeRecommendations('volatile', 0.6);

      expect(recommendations.strategy).toBe('avoid_trading');
      expect(recommendations.riskMultiplier).toBeLessThan(0.5);
      expect(recommendations.filters).toContain('high_confidence_only');
    });

    it('should adjust recommendations based on confidence', () => {
      const highConfidence = detector.getRegimeRecommendations('trending', 0.9);
      const lowConfidence = detector.getRegimeRecommendations('trending', 0.3);

      expect(highConfidence.riskMultiplier).toBeGreaterThan(lowConfidence.riskMultiplier);
    });
  });

  describe('helper methods', () => {
    describe('calculateEMA', () => {
      it('should calculate EMA correctly', () => {
        const prices = [100, 101, 102, 103, 104, 105];

        const ema = detector.calculateEMA(prices, 3);

        expect(ema).toBeGreaterThan(100);
        expect(ema).toBeLessThan(110);
      });

      it('should return null for insufficient data', () => {
        const prices = [100, 101];

        const ema = detector.calculateEMA(prices, 5);

        expect(ema).toBeNull();
      });
    });

    describe('calculateSMA', () => {
      it('should calculate SMA correctly', () => {
        const prices = [100, 102, 104, 106, 108];

        const sma = detector.calculateSMA(prices, 3);

        expect(sma).toBeCloseTo(106, 1); // Average of last 3: (104+106+108)/3
      });

      it('should return null for insufficient data', () => {
        const prices = [100, 101];

        const sma = detector.calculateSMA(prices, 5);

        expect(sma).toBeNull();
      });
    });

    describe('calculateSlope', () => {
      it('should calculate positive slope for increasing data', () => {
        const data = [1, 2, 3, 4, 5];

        const slope = detector.calculateSlope(data);

        expect(slope).toBeGreaterThan(0);
      });

      it('should calculate negative slope for decreasing data', () => {
        const data = [5, 4, 3, 2, 1];

        const slope = detector.calculateSlope(data);

        expect(slope).toBeLessThan(0);
      });

      it('should return 0 for insufficient data', () => {
        const data = [1];

        const slope = detector.calculateSlope(data);

        expect(slope).toBe(0);
      });
    });
  });
});