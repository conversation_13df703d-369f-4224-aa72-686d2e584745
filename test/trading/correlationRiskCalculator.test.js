const CorrelationRiskCalculator = require('../../lib/trading/correlationRiskCalculator');

describe('CorrelationRiskCalculator', () => {
  let calculator;
  const mockConfig = {
    correlationThreshold: 0.8,
    maxCorrelatedPositions: 3
  };

  beforeEach(() => {
    calculator = new CorrelationRiskCalculator(mockConfig);
  });

  describe('Initialization', () => {
    test('should initialize with correct configuration', () => {
      expect(calculator.config.correlationThreshold).toBe(0.8);
      expect(calculator.config.maxCorrelatedPositions).toBe(3);
      expect(calculator.correlationCache).toBeInstanceOf(Map);
    });

    test('should have predefined static correlations', () => {
      expect(calculator.config.staticCorrelations).toHaveProperty('BTCUSDT');
      expect(calculator.config.staticCorrelations).toHaveProperty('ETHUSDT');
      expect(calculator.config.staticCorrelations.BTCUSDT).toHaveProperty('ETHUSDT');
    });
  });

  describe('Correlation Risk Calculation', () => {
    test('should calculate correlation risk with empty portfolio', async () => {
      const risk = await calculator.calculateCorrelationRisk('BTCUSDT', {});

      expect(risk.maxCorrelation).toBe(0);
      expect(risk.correlatedPositions).toHaveLength(0);
      expect(risk.riskLevel).toBe('low');
      expect(risk.positionSizeMultiplier).toBe(1.0);
    });

    test('should calculate correlation risk with existing positions', async () => {
      const portfolio = {
        'ETHUSDT': { size: 1, value: 3000, weight: 0.3 },
        'ADAUSDT': { size: 1000, value: 500, weight: 0.05 }
      };

      const risk = await calculator.calculateCorrelationRisk('BTCUSDT', portfolio);

      expect(risk.maxCorrelation).toBeGreaterThan(0);
      expect(risk.correlatedPositions.length).toBeGreaterThan(0);
      expect(risk.correlatedPositions[0]).toHaveProperty('symbol');
      expect(risk.correlatedPositions[0]).toHaveProperty('correlation');
      expect(risk.correlatedPositions[0]).toHaveProperty('positionSize');
    });

    test('should sort correlated positions by correlation', async () => {
      const portfolio = {
        'ETHUSDT': { size: 1, value: 3000, weight: 0.3 },
        'ADAUSDT': { size: 1000, value: 500, weight: 0.05 },
        'SOLUSDT': { size: 10, value: 1000, weight: 0.1 }
      };

      const risk = await calculator.calculateCorrelationRisk('BTCUSDT', portfolio);

      // Should be sorted by correlation (highest first)
      for (let i = 1; i < risk.correlatedPositions.length; i++) {
        expect(risk.correlatedPositions[i-1].correlation)
          .toBeGreaterThanOrEqual(risk.correlatedPositions[i].correlation);
      }
    });

    test('should handle calculation errors gracefully', async () => {
      // Mock getCorrelation to throw error
      const originalGetCorrelation = calculator.getCorrelation;
      calculator.getCorrelation = jest.fn().mockRejectedValue(new Error('Test error'));

      const portfolio = { 'ETHUSDT': { size: 1, value: 3000, weight: 0.3 } };
      const risk = await calculator.calculateCorrelationRisk('BTCUSDT', portfolio);

      expect(risk.riskLevel).toBe('unknown');
      expect(risk.positionSizeMultiplier).toBe(0.5); // Conservative on error

      // Restore original method
      calculator.getCorrelation = originalGetCorrelation;
    });
  });

  describe('Correlation Retrieval', () => {
    test('should get static correlation between symbols', async () => {
      const correlation = await calculator.getCorrelation('BTCUSDT', 'ETHUSDT');
      expect(correlation).toBe(0.85); // From static correlation matrix
    });

    test('should return 1.0 for same symbol', async () => {
      const correlation = await calculator.getCorrelation('BTCUSDT', 'BTCUSDT');
      expect(correlation).toBe(1.0);
    });

    test('should use cached correlations', async () => {
      // First call
      const correlation1 = await calculator.getCorrelation('BTCUSDT', 'ETHUSDT');

      // Second call should use cache
      const correlation2 = await calculator.getCorrelation('BTCUSDT', 'ETHUSDT');

      expect(correlation1).toBe(correlation2);
      expect(calculator.correlationCache.size).toBeGreaterThan(0);
    });

    test('should calculate dynamic correlation for unknown pairs', async () => {
      const correlation = await calculator.getCorrelation('UNKNOWN1', 'UNKNOWN2');
      expect(correlation).toBeGreaterThanOrEqual(0);
      expect(correlation).toBeLessThanOrEqual(1);
    });
  });

  describe('Static Correlation Retrieval', () => {
    test('should get static correlation from matrix', () => {
      const correlation = calculator.getStaticCorrelation('BTCUSDT', 'ETHUSDT');
      expect(correlation).toBe(0.85);
    });

    test('should get symmetric correlation', () => {
      const correlation1 = calculator.getStaticCorrelation('BTCUSDT', 'ETHUSDT');
      const correlation2 = calculator.getStaticCorrelation('ETHUSDT', 'BTCUSDT');
      expect(correlation1).toBe(correlation2);
    });

    test('should return null for unknown pairs', () => {
      const correlation = calculator.getStaticCorrelation('UNKNOWN1', 'UNKNOWN2');
      expect(correlation).toBe(null);
    });
  });

  describe('Dynamic Correlation Calculation', () => {
    test('should calculate correlation based on symbol categories', async () => {
      // Same category (major cryptos)
      const correlation1 = await calculator.calculateDynamicCorrelation('BTCUSDT', 'ETHUSDT');
      expect(correlation1).toBe(0.7);

      // Different categories
      const correlation2 = await calculator.calculateDynamicCorrelation('BTCUSDT', 'DOGEUSDT');
      expect(correlation2).toBe(0.5); // Major with meme

      // Both non-major
      const correlation3 = await calculator.calculateDynamicCorrelation('ADAUSDT', 'SOLUSDT');
      expect(correlation3).toBe(0.7); // Same category (altcoins)
    });
  });

  describe('Symbol Categorization', () => {
    test('should categorize symbols correctly', () => {
      expect(calculator.getSymbolCategory('BTCUSDT')).toBe('major');
      expect(calculator.getSymbolCategory('ETHUSDT')).toBe('major');
      expect(calculator.getSymbolCategory('ADAUSDT')).toBe('altcoin');
      expect(calculator.getSymbolCategory('SOLUSDT')).toBe('altcoin');
      expect(calculator.getSymbolCategory('DOGEUSDT')).toBe('meme');
      expect(calculator.getSymbolCategory('BNBUSDT')).toBe('utility');
      expect(calculator.getSymbolCategory('UNKNOWN')).toBe('other');
    });
  });

  describe('Risk Assessment', () => {
    test('should assess low risk correctly', () => {
      const risk = { maxCorrelation: 0.5, correlatedPositions: [], totalCorrelationExposure: 0.3 };
      const assessment = calculator.assessCorrelationRisk(risk);

      expect(assessment.level).toBe('low');
      expect(assessment.multiplier).toBe(1.0);
    });

    test('should assess high correlation risk', () => {
      const risk = {
        maxCorrelation: 0.85,
        correlatedPositions: [
          { correlation: 0.85 },
          { correlation: 0.75 }
        ],
        totalCorrelationExposure: 0.6
      };

      const assessment = calculator.assessCorrelationRisk(risk);

      expect(assessment.level).toBe('high');
      expect(assessment.multiplier).toBe(0.4);
      expect(assessment.recommendations.length).toBeGreaterThan(0);
    });

    test('should assess very high correlation risk', () => {
      const risk = {
        maxCorrelation: 0.95,
        correlatedPositions: [],
        totalCorrelationExposure: 0.3
      };

      const assessment = calculator.assessCorrelationRisk(risk);

      expect(assessment.level).toBe('very_high');
      expect(assessment.multiplier).toBe(0.2);
    });

    test('should assess risk based on number of correlated positions', () => {
      const risk = {
        maxCorrelation: 0.6,
        correlatedPositions: [
          { correlation: 0.8 },
          { correlation: 0.75 },
          { correlation: 0.7 },
          { correlation: 0.72 } // 4 highly correlated positions
        ],
        totalCorrelationExposure: 0.5
      };

      const assessment = calculator.assessCorrelationRisk(risk);

      expect(assessment.level).toBe('high');
      expect(assessment.multiplier).toBeLessThanOrEqual(0.3);
    });
  });

  describe('Cache Management', () => {
    test('should generate cache keys correctly', () => {
      const key1 = calculator.getCacheKey('BTCUSDT', 'ETHUSDT');
      const key2 = calculator.getCacheKey('ETHUSDT', 'BTCUSDT');

      expect(key1).toBe(key2); // Should be symmetric
      expect(key1).toBe('BTCUSDT_ETHUSDT');
    });

    test('should clear cache', () => {
      calculator.correlationCache.set('test', 0.5);
      calculator.lastCacheUpdate.set('test', Date.now());

      expect(calculator.correlationCache.size).toBe(1);

      calculator.clearCache();

      expect(calculator.correlationCache.size).toBe(0);
      expect(calculator.lastCacheUpdate.size).toBe(0);
    });

    test('should provide cache statistics', async () => {
      await calculator.getCorrelation('BTCUSDT', 'ETHUSDT');

      const stats = calculator.getCacheStats();

      expect(stats).toHaveProperty('cacheSize');
      expect(stats).toHaveProperty('lastUpdates');
      expect(stats.cacheSize).toBeGreaterThan(0);
      expect(Array.isArray(stats.lastUpdates)).toBe(true);
    });
  });

  describe('Portfolio Analysis', () => {
    test('should calculate portfolio correlation matrix', async () => {
      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000 },
        'ETHUSDT': { size: 1, value: 3000 },
        'ADAUSDT': { size: 1000, value: 500 }
      };

      const matrix = await calculator.calculatePortfolioCorrelationMatrix(portfolio);

      expect(matrix).toHaveProperty('BTCUSDT');
      expect(matrix).toHaveProperty('ETHUSDT');
      expect(matrix).toHaveProperty('ADAUSDT');

      expect(matrix.BTCUSDT.BTCUSDT).toBe(1.0);
      expect(matrix.BTCUSDT.ETHUSDT).toBe(matrix.ETHUSDT.BTCUSDT);
    });

    test('should calculate diversification score', async () => {
      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000 },
        'ETHUSDT': { size: 1, value: 3000 }
      };

      const diversification = await calculator.getPortfolioDiversificationScore(portfolio);

      expect(diversification).toHaveProperty('score');
      expect(diversification).toHaveProperty('level');
      expect(diversification).toHaveProperty('avgCorrelation');

      expect(diversification.score).toBeGreaterThanOrEqual(0);
      expect(diversification.score).toBeLessThanOrEqual(100);
      expect(['excellent', 'good', 'fair', 'poor'].includes(diversification.level)).toBe(true);
    });

    test('should handle single position portfolio', async () => {
      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000 }
      };

      const diversification = await calculator.getPortfolioDiversificationScore(portfolio);

      expect(diversification.score).toBe(100);
      expect(diversification.level).toBe('excellent');
    });
  });

  describe('Position Recommendations', () => {
    test('should generate position recommendations', async () => {
      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000 },
        'ETHUSDT': { size: 1, value: 3000 } // High correlation with BTC
      };

      const recommendations = await calculator.getPositionRecommendations(portfolio);

      expect(Array.isArray(recommendations)).toBe(true);

      if (recommendations.length > 0) {
        expect(recommendations[0]).toHaveProperty('type');
        expect(recommendations[0]).toHaveProperty('action');
        expect(recommendations[0]).toHaveProperty('priority');
      }
    });

    test('should recommend reducing high correlation positions', async () => {
      // Mock high correlation scenario
      const originalCalculatePortfolioCorrelationMatrix = calculator.calculatePortfolioCorrelationMatrix;
      calculator.calculatePortfolioCorrelationMatrix = jest.fn().mockResolvedValue({
        'BTCUSDT': { 'BTCUSDT': 1.0, 'ETHUSDT': 0.9 },
        'ETHUSDT': { 'BTCUSDT': 0.9, 'ETHUSDT': 1.0 }
      });

      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000 },
        'ETHUSDT': { size: 1, value: 3000 }
      };

      const recommendations = await calculator.getPositionRecommendations(portfolio);

      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations[0].type).toBe('reduce_correlation');

      // Restore original method
      calculator.calculatePortfolioCorrelationMatrix = originalCalculatePortfolioCorrelationMatrix;
    });
  });

  describe('Category Exposure Analysis', () => {
    test('should calculate category exposure correctly', () => {
      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000, weight: 0.5 },
        'ETHUSDT': { size: 1, value: 3000, weight: 0.3 },
        'ADAUSDT': { size: 1000, value: 2000, weight: 0.2 }
      };

      const exposure = calculator.calculateCategoryExposure(portfolio);

      expect(exposure).toHaveProperty('major');
      expect(exposure).toHaveProperty('altcoin');

      expect(exposure.major.weight).toBe(0.8); // BTC + ETH
      expect(exposure.altcoin.weight).toBe(0.2); // ADA
      expect(exposure.major.symbols).toContain('BTCUSDT');
      expect(exposure.major.symbols).toContain('ETHUSDT');
    });

    test('should handle empty portfolio', () => {
      const exposure = calculator.calculateCategoryExposure({});
      expect(Object.keys(exposure)).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    test('should handle portfolio correlation matrix errors', async () => {
      // Mock getCorrelation to throw error
      const originalGetCorrelation = calculator.getCorrelation;
      calculator.getCorrelation = jest.fn().mockRejectedValue(new Error('Test error'));

      const portfolio = {
        'BTCUSDT': { size: 0.1, value: 5000 },
        'ETHUSDT': { size: 1, value: 3000 }
      };

      const matrix = await calculator.calculatePortfolioCorrelationMatrix(portfolio);
      expect(matrix).toEqual({});

      // Restore original method
      calculator.getCorrelation = originalGetCorrelation;
    });

    test('should handle diversification score calculation errors', async () => {
      // Mock calculatePortfolioCorrelationMatrix to throw error
      const originalCalculateMatrix = calculator.calculatePortfolioCorrelationMatrix;
      calculator.calculatePortfolioCorrelationMatrix = jest.fn().mockRejectedValue(new Error('Test error'));

      const portfolio = { 'BTCUSDT': { size: 0.1, value: 5000 } };
      const diversification = await calculator.getPortfolioDiversificationScore(portfolio);

      expect(diversification.score).toBe(0);
      expect(diversification.level).toBe('unknown');

      // Restore original method
      calculator.calculatePortfolioCorrelationMatrix = originalCalculateMatrix;
    });

    test('should handle recommendation generation errors', async () => {
      // Mock calculatePortfolioCorrelationMatrix to throw error
      const originalCalculateMatrix = calculator.calculatePortfolioCorrelationMatrix;
      calculator.calculatePortfolioCorrelationMatrix = jest.fn().mockRejectedValue(new Error('Test error'));

      const portfolio = { 'BTCUSDT': { size: 0.1, value: 5000 } };
      const recommendations = await calculator.getPositionRecommendations(portfolio);

      expect(recommendations).toEqual([]);

      // Restore original method
      calculator.calculatePortfolioCorrelationMatrix = originalCalculateMatrix;
    });
  });
});