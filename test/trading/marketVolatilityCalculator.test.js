const MarketVolatilityCalculator = require('../../lib/trading/marketVolatilityCalculator');

describe('MarketVolatilityCalculator', () => {
  let calculator;
  let mockCandles;

  beforeEach(() => {
    calculator = new MarketVolatilityCalculator();

    // Create mock candle data
    mockCandles = [];
    const basePrice = 50000;

    for (let i = 0; i < 30; i++) {
      const volatility = 0.02; // 2% daily volatility
      const change = (Math.random() - 0.5) * 2 * volatility;
      const price = i === 0 ? basePrice : mockCandles[i-1].close * (1 + change);

      mockCandles.push({
        open: price * (1 + (Math.random() - 0.5) * 0.01),
        high: price * (1 + Math.random() * 0.02),
        low: price * (1 - Math.random() * 0.02),
        close: price,
        volume: 1000000 + Math.random() * 500000,
        timestamp: new Date(Date.now() - (30 - i) * 24 * 60 * 60 * 1000)
      });
    }
  });

  describe('Basic Volatility Calculation', () => {
    test('should calculate volatility metrics', () => {
      const result = calculator.calculateVolatility(mockCandles);

      expect(result).toHaveProperty('atrVolatility');
      expect(result).toHaveProperty('priceChangeVolatility');
      expect(result).toHaveProperty('combinedVolatility');
      expect(result).toHaveProperty('level');
      expect(result).toHaveProperty('confidence');

      expect(result.atrVolatility).toBeGreaterThanOrEqual(0);
      expect(result.priceChangeVolatility).toBeGreaterThanOrEqual(0);
      expect(result.combinedVolatility).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    test('should handle insufficient data', () => {
      const shortCandles = mockCandles.slice(0, 5);
      const result = calculator.calculateVolatility(shortCandles);

      expect(result.level).toBe('unknown');
      expect(result.confidence).toBe(0);
    });

    test('should handle empty or null data', () => {
      expect(calculator.calculateVolatility([])).toHaveProperty('level', 'unknown');
      expect(calculator.calculateVolatility(null)).toHaveProperty('level', 'unknown');
      expect(calculator.calculateVolatility(undefined)).toHaveProperty('level', 'unknown');
    });
  });

  describe('ATR Volatility Calculation', () => {
    test('should calculate ATR volatility correctly', () => {
      const result = calculator.calculateATRVolatility(mockCandles);

      expect(result).toHaveProperty('value');
      expect(result).toHaveProperty('raw');
      expect(result).toHaveProperty('percent');
      expect(result).toHaveProperty('confidence');

      expect(result.value).toBeGreaterThan(0);
      expect(result.raw).toBeGreaterThan(0);
      expect(result.percent).toBeGreaterThan(0);
      expect(result.confidence).toBe(0.8);
    });

    test('should handle ATR calculation errors', () => {
      const invalidCandles = [{ close: 100 }]; // Insufficient data for ATR
      const result = calculator.calculateATRVolatility(invalidCandles);

      expect(result.value).toBe(0);
      expect(result.confidence).toBe(0);
    });
  });

  describe('Price Change Volatility Calculation', () => {
    test('should calculate price change volatility', () => {
      const result = calculator.calculatePriceChangeVolatility(mockCandles);

      expect(result).toHaveProperty('value');
      expect(result).toHaveProperty('annualized');
      expect(result).toHaveProperty('mean');
      expect(result).toHaveProperty('confidence');

      expect(result.value).toBeGreaterThanOrEqual(0);
      expect(result.annualized).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBe(0.7);
    });

    test('should calculate standard deviation correctly', () => {
      // Create candles with known volatility
      const testCandles = [
        { close: 100 },
        { close: 102 }, // 2% change
        { close: 100 }, // -1.96% change
        { close: 103 }, // 3% change
        { close: 101 }  // -1.94% change
      ];

      const result = calculator.calculatePriceChangeVolatility(testCandles);
      expect(result.value).toBeGreaterThan(0);
    });
  });

  describe('Range Volatility Calculation', () => {
    test('should calculate range volatility', () => {
      const result = calculator.calculateRangeVolatility(mockCandles);

      expect(result).toHaveProperty('value');
      expect(result).toHaveProperty('stdDev');
      expect(result).toHaveProperty('confidence');

      expect(result.value).toBeGreaterThan(0);
      expect(result.stdDev).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBe(0.6);
    });
  });

  describe('Gap Volatility Calculation', () => {
    test('should calculate gap volatility', () => {
      const result = calculator.calculateGapVolatility(mockCandles);

      expect(result).toHaveProperty('value');
      expect(result).toHaveProperty('maxGap');
      expect(result).toHaveProperty('confidence');

      expect(result.value).toBeGreaterThanOrEqual(0);
      expect(result.maxGap).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBe(0.5);
    });
  });

  describe('Volatility Level Determination', () => {
    test('should classify volatility levels correctly', () => {
      expect(calculator.determineVolatilityLevel(0.01)).toBe('very_low');
      expect(calculator.determineVolatilityLevel(0.025)).toBe('low');
      expect(calculator.determineVolatilityLevel(0.04)).toBe('medium');
      expect(calculator.determineVolatilityLevel(0.06)).toBe('high');
      expect(calculator.determineVolatilityLevel(0.08)).toBe('very_high');
    });
  });

  describe('Volatility Trend Analysis', () => {
    test('should calculate volatility trends', () => {
      const trends = calculator.getVolatilityTrend(mockCandles);

      expect(trends).toHaveProperty('5period');
      expect(trends).toHaveProperty('10period');
      expect(trends).toHaveProperty('20period');

      for (const period of Object.keys(trends)) {
        expect(trends[period]).toHaveProperty('change');
        expect(trends[period]).toHaveProperty('changePercent');
        expect(trends[period]).toHaveProperty('direction');
        expect(['increasing', 'decreasing', 'stable']).toContain(trends[period].direction);
      }
    });

    test('should handle insufficient data for trends', () => {
      const shortCandles = mockCandles.slice(0, 8);
      const trends = calculator.getVolatilityTrend(shortCandles);

      // Should only have 5period trend with 8 candles
      expect(trends).toHaveProperty('5period');
      expect(trends).not.toHaveProperty('10period');
      expect(trends).not.toHaveProperty('20period');
    });
  });

  describe('Abnormal Volatility Detection', () => {
    test('should detect normal volatility', () => {
      const result = calculator.isAbnormalVolatility(mockCandles);

      expect(result).toHaveProperty('abnormal');
      expect(result).toHaveProperty('currentVolatility');
      expect(result).toHaveProperty('percentile95');
      expect(result).toHaveProperty('percentileRank');

      expect(typeof result.abnormal).toBe('boolean');
      expect(result.currentVolatility).toBeGreaterThanOrEqual(0);
      expect(result.percentileRank).toBeGreaterThanOrEqual(0);
      expect(result.percentileRank).toBeLessThanOrEqual(100);
    });

    test('should detect abnormal volatility with high volatility candles', () => {
      // Create high volatility candles
      const highVolCandles = [];
      const basePrice = 50000;

      for (let i = 0; i < 50; i++) {
        let volatility = 0.02; // Normal volatility for historical data

        // Make recent candles highly volatile
        if (i >= 30) {
          volatility = 0.08; // Very high volatility
        }

        const change = (Math.random() - 0.5) * 2 * volatility;
        const price = i === 0 ? basePrice : highVolCandles[i-1].close * (1 + change);

        highVolCandles.push({
          open: price * (1 + (Math.random() - 0.5) * volatility),
          high: price * (1 + Math.random() * volatility * 2),
          low: price * (1 - Math.random() * volatility * 2),
          close: price,
          volume: 1000000,
          timestamp: new Date(Date.now() - (50 - i) * 24 * 60 * 60 * 1000)
        });
      }

      const result = calculator.isAbnormalVolatility(highVolCandles);
      expect(result.abnormal).toBe(true);
      expect(result.percentileRank).toBeGreaterThan(90);
    });

    test('should handle insufficient historical data', () => {
      const shortCandles = mockCandles.slice(0, 15);
      const result = calculator.isAbnormalVolatility(shortCandles);

      expect(result.abnormal).toBe(false);
      expect(result.reason).toBe('insufficient_data');
    });
  });

  describe('Percentile Rank Calculation', () => {
    test('should calculate percentile rank correctly', () => {
      const sortedArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

      expect(calculator.calculatePercentileRank(1, sortedArray)).toBe(10);
      expect(calculator.calculatePercentileRank(5, sortedArray)).toBe(50);
      expect(calculator.calculatePercentileRank(10, sortedArray)).toBe(100);
      expect(calculator.calculatePercentileRank(15, sortedArray)).toBe(100);
    });
  });

  describe('Combined Volatility Calculation', () => {
    test('should combine volatilities with proper weights', () => {
      const volatilities = {
        atr: { value: 0.03, confidence: 0.8 },
        priceChange: { value: 0.025, confidence: 0.7 },
        range: { value: 0.035, confidence: 0.6 },
        gap: { value: 0.02, confidence: 0.5 }
      };

      const result = calculator.combineVolatilities(volatilities);

      expect(result).toHaveProperty('value');
      expect(result).toHaveProperty('confidence');
      expect(result.value).toBeGreaterThan(0);
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    test('should handle zero confidence volatilities', () => {
      const volatilities = {
        atr: { value: 0.03, confidence: 0 },
        priceChange: { value: 0.025, confidence: 0 },
        range: { value: 0.035, confidence: 0 },
        gap: { value: 0.02, confidence: 0 }
      };

      const result = calculator.combineVolatilities(volatilities);
      expect(result.value).toBe(0);
      expect(result.confidence).toBe(0);
    });
  });

  describe('Configuration Options', () => {
    test('should use custom configuration', () => {
      const customConfig = {
        atrPeriod: 21,
        priceChangePeriod: 30,
        volatilityThresholds: {
          low: 0.01,
          medium: 0.03,
          high: 0.06,
          veryHigh: 0.1
        }
      };

      const customCalculator = new MarketVolatilityCalculator(customConfig);
      expect(customCalculator.config.atrPeriod).toBe(21);
      expect(customCalculator.config.priceChangePeriod).toBe(30);
      expect(customCalculator.config.volatilityThresholds.high).toBe(0.06);
    });
  });

  describe('Error Handling', () => {
    test('should handle calculation errors gracefully', () => {
      // Mock console.error to avoid test output pollution
      const originalError = console.error;
      console.error = jest.fn();

      // Test with malformed candle data
      const badCandles = [
        { close: 'invalid' },
        { high: null, low: undefined, close: 100 }
      ];

      const result = calculator.calculateVolatility(badCandles);
      expect(result.level).toBe('unknown');
      expect(result.confidence).toBe(0);

      // Restore console.error
      console.error = originalError;
    });
  });
});