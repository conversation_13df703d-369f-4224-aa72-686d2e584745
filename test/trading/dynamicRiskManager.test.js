const DynamicRiskManager = require('../../lib/trading/dynamicRiskManager');

describe('DynamicRiskManager', () => {
  let riskManager;
  const mockConfig = {
    maxConsecutiveLosses: 5,
    maxDrawdownPercent: 15,
    volatilityThreshold: 0.05,
    correlationThreshold: 0.8,
    pauseDurationHours: 1, // Shorter for testing
    riskMultipliers: {
      consecutive3Losses: 0.5,
      consecutive5Losses: 0.0,
      highVolatility: 0.7,
      highCorrelation: 0.6,
      highDrawdown: 0.3
    }
  };

  beforeEach(() => {
    riskManager = new DynamicRiskManager(mockConfig);
    riskManager.initialize(10000); // $10,000 starting balance
  });

  describe('Initialization', () => {
    test('should initialize with correct default values', () => {
      const metrics = riskManager.getRiskMetrics();
      expect(metrics.consecutiveLosses).toBe(0);
      expect(metrics.currentDrawdown).toBe(0);
      expect(metrics.riskMultiplier).toBe(1.0);
      expect(metrics.tradingPaused).toBe(false);
      expect(metrics.currentBalance).toBe(10000);
      expect(metrics.peakBalance).toBe(10000);
    });
  });

  describe('Consecutive Loss Risk Assessment', () => {
    test('should allow trading with no consecutive losses', async () => {
      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.allowTrade).toBe(true);
      expect(assessment.positionSizeMultiplier).toBe(1.0);
      expect(assessment.riskLevel).toBe('normal');
    });

    test('should reduce position size after 3 consecutive losses', async () => {
      // Simulate 3 consecutive losses
      for (let i = 0; i < 3; i++) {
        await riskManager.updateRiskState({
          pnlPercent: -2,
          pnlAmount: -200,
          symbol: 'BTCUSDT',
          exitReason: 'stop_loss'
        });
      }

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.allowTrade).toBe(true);
      expect(assessment.positionSizeMultiplier).toBe(0.5);
      expect(assessment.riskLevel).toBe('high');
    });

    test('should pause trading after 5 consecutive losses', async () => {
      // Simulate 5 consecutive losses
      for (let i = 0; i < 5; i++) {
        await riskManager.updateRiskState({
          pnlPercent: -2,
          pnlAmount: -200,
          symbol: 'BTCUSDT',
          exitReason: 'stop_loss'
        });
      }

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.allowTrade).toBe(false);
      expect(assessment.reasonsToReject).toContain('5 consecutive losses - trading paused');
    });

    test('should reset consecutive losses after a win', async () => {
      // Simulate 3 losses then 1 win
      for (let i = 0; i < 3; i++) {
        await riskManager.updateRiskState({
          pnlPercent: -2,
          pnlAmount: -200,
          symbol: 'BTCUSDT',
          exitReason: 'stop_loss'
        });
      }

      await riskManager.updateRiskState({
        pnlPercent: 3,
        pnlAmount: 300,
        symbol: 'BTCUSDT',
        exitReason: 'take_profit'
      });

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.consecutiveLosses).toBe(0);
    });
  });

  describe('Drawdown Risk Assessment', () => {
    test('should track drawdown correctly', async () => {
      // Simulate losses that create drawdown
      await riskManager.updateRiskState({
        pnlPercent: -10,
        pnlAmount: -1000,
        symbol: 'BTCUSDT',
        exitReason: 'stop_loss'
      });

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.currentDrawdown).toBeCloseTo(10, 1);
    });

    test('should reduce position size on high drawdown', async () => {
      // Simulate large loss to trigger high drawdown
      await riskManager.updateRiskState({
        pnlPercent: -12,
        pnlAmount: -1200,
        symbol: 'BTCUSDT',
        exitReason: 'stop_loss'
      });

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.riskLevel).toBe('critical');
      expect(assessment.positionSizeMultiplier).toBeLessThan(1.0);
    });

    test('should pause trading on maximum drawdown', async () => {
      // Simulate loss that exceeds maximum drawdown
      await riskManager.updateRiskState({
        pnlPercent: -16,
        pnlAmount: -1600,
        symbol: 'BTCUSDT',
        exitReason: 'stop_loss'
      });

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.tradingPaused).toBe(true);
    });
  });

  describe('Volatility Risk Assessment', () => {
    test('should assess volatility risk for different symbols', async () => {
      const signal = { symbol: 'DOGEUSDT', entry: 0.1 }; // Typically more volatile
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.riskFactors.volatility).toBeDefined();
      expect(assessment.riskFactors.volatility.level).toBeDefined();
    });

    test('should reduce position size for high volatility', async () => {
      // Mock high volatility scenario
      const originalAssessVolatilityRisk = riskManager.assessVolatilityRisk;
      riskManager.assessVolatilityRisk = jest.fn().mockResolvedValue({
        high: true,
        level: 'very_high',
        volatility: 0.08
      });

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.positionSizeMultiplier).toBeLessThan(1.0);
      expect(assessment.reasonsToReject).toContain('High market volatility detected');

      // Restore original method
      riskManager.assessVolatilityRisk = originalAssessVolatilityRisk;
    });
  });

  describe('News Event Risk Assessment', () => {
    test('should pause trading for high impact news', async () => {
      // Mock high impact news event
      const originalAssessNewsRisk = riskManager.assessNewsRisk;
      riskManager.assessNewsRisk = jest.fn().mockResolvedValue({
        highImpact: true,
        events: [{
          title: 'Fed Rate Decision',
          impact: 'high',
          time: new Date()
        }],
        level: 'high'
      });

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.allowTrade).toBe(false);
      expect(assessment.reasonsToReject).toContain('High-impact news event detected');

      // Restore original method
      riskManager.assessNewsRisk = originalAssessNewsRisk;
    });
  });

  describe('Correlation Risk Assessment', () => {
    test('should assess correlation risk with portfolio', async () => {
      const portfolio = {
        'ETHUSDT': { size: 1, value: 3000, weight: 0.3 },
        'ADAUSDT': { size: 1000, value: 500, weight: 0.05 }
      };

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal, portfolio);

      expect(assessment.riskFactors.correlation).toBeDefined();
      expect(assessment.riskFactors.correlation.correlatedSymbols).toBeDefined();
    });

    test('should reduce position size for high correlation', async () => {
      // Mock high correlation scenario
      const originalAssessCorrelationRisk = riskManager.assessCorrelationRisk;
      riskManager.assessCorrelationRisk = jest.fn().mockResolvedValue({
        high: true,
        maxCorrelation: 0.9,
        correlatedSymbols: [{ symbol: 'ETHUSDT', correlation: 0.9 }],
        level: 'high'
      });

      const portfolio = { 'ETHUSDT': { size: 1, value: 3000, weight: 0.3 } };
      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal, portfolio);

      expect(assessment.positionSizeMultiplier).toBeLessThan(1.0);
      expect(assessment.reasonsToReject).toContain('High correlation with existing positions');

      // Restore original method
      riskManager.assessCorrelationRisk = originalAssessCorrelationRisk;
    });
  });

  describe('Risk State Management', () => {
    test('should update risk state correctly after trades', async () => {
      const tradeResult = {
        pnlPercent: -3,
        pnlAmount: -300,
        symbol: 'BTCUSDT',
        exitReason: 'stop_loss'
      };

      await riskManager.updateRiskState(tradeResult);

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.consecutiveLosses).toBe(1);
      expect(metrics.currentBalance).toBe(9700);
      expect(metrics.recentTradesCount).toBe(1);
    });

    test('should maintain recent trades history', async () => {
      // Add multiple trades
      for (let i = 0; i < 5; i++) {
        await riskManager.updateRiskState({
          pnlPercent: i % 2 === 0 ? 2 : -2,
          pnlAmount: i % 2 === 0 ? 200 : -200,
          symbol: 'BTCUSDT',
          exitReason: i % 2 === 0 ? 'take_profit' : 'stop_loss'
        });
      }

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.recentTradesCount).toBe(5);
    });
  });

  describe('Trading Pause Management', () => {
    test('should pause and resume trading automatically', (done) => {
      riskManager.pauseTrading('test_pause');

      expect(riskManager.isTradingPaused()).toBe(true);

      // Should auto-resume after pause duration (1 hour in test config)
      setTimeout(() => {
        expect(riskManager.isTradingPaused()).toBe(false);
        done();
      }, 100); // Short timeout for testing
    }, 10000);

    test('should allow manual resume', () => {
      riskManager.pauseTrading('test_pause');
      expect(riskManager.isTradingPaused()).toBe(true);

      riskManager.resumeTrading();
      expect(riskManager.isTradingPaused()).toBe(false);
    });
  });

  describe('Risk Metrics and Reporting', () => {
    test('should calculate recent win rate correctly', async () => {
      // Add mix of winning and losing trades
      const trades = [
        { pnlPercent: 2, pnlAmount: 200 },
        { pnlPercent: -1, pnlAmount: -100 },
        { pnlPercent: 3, pnlAmount: 300 },
        { pnlPercent: -2, pnlAmount: -200 },
        { pnlPercent: 1, pnlAmount: 100 }
      ];

      for (const trade of trades) {
        await riskManager.updateRiskState({
          ...trade,
          symbol: 'BTCUSDT',
          exitReason: trade.pnlPercent > 0 ? 'take_profit' : 'stop_loss'
        });
      }

      const winRate = riskManager.calculateRecentWinRate();
      expect(winRate).toBe(60); // 3 wins out of 5 trades
    });

    test('should generate appropriate risk recommendations', async () => {
      // Simulate scenario that should generate recommendations
      for (let i = 0; i < 2; i++) {
        await riskManager.updateRiskState({
          pnlPercent: -2,
          pnlAmount: -200,
          symbol: 'BTCUSDT',
          exitReason: 'stop_loss'
        });
      }

      const summary = riskManager.getRiskSummary();
      expect(summary.recommendations).toHaveLength(1);
      expect(summary.recommendations[0].type).toBe('position_sizing');
    });

    test('should provide comprehensive risk summary', () => {
      const summary = riskManager.getRiskSummary();

      expect(summary).toHaveProperty('consecutiveLosses');
      expect(summary).toHaveProperty('currentDrawdown');
      expect(summary).toHaveProperty('riskLevel');
      expect(summary).toHaveProperty('recentWinRate');
      expect(summary).toHaveProperty('recommendations');
    });
  });

  describe('Error Handling', () => {
    test('should handle assessment errors gracefully', async () => {
      // Mock an error in volatility assessment
      const originalAssessVolatilityRisk = riskManager.assessVolatilityRisk;
      riskManager.assessVolatilityRisk = jest.fn().mockRejectedValue(new Error('Test error'));

      const signal = { symbol: 'BTCUSDT', entry: 50000 };
      const assessment = await riskManager.assessRisk(signal);

      expect(assessment.allowTrade).toBe(false);
      expect(assessment.reasonsToReject).toContain('Risk assessment error - trading paused for safety');

      // Restore original method
      riskManager.assessVolatilityRisk = originalAssessVolatilityRisk;
    });

    test('should handle update errors gracefully', async () => {
      // This should not throw an error
      await expect(riskManager.updateRiskState(null)).resolves.not.toThrow();
      await expect(riskManager.updateRiskState({})).resolves.not.toThrow();
    });
  });

  describe('Reset Functionality', () => {
    test('should reset all risk state', async () => {
      // Set up some risk state
      await riskManager.updateRiskState({
        pnlPercent: -5,
        pnlAmount: -500,
        symbol: 'BTCUSDT',
        exitReason: 'stop_loss'
      });

      riskManager.pauseTrading('test');

      // Reset
      riskManager.reset();

      const metrics = riskManager.getRiskMetrics();
      expect(metrics.consecutiveLosses).toBe(0);
      expect(metrics.tradingPaused).toBe(false);
      expect(metrics.currentBalance).toBe(10000);
      expect(metrics.recentTradesCount).toBe(0);
    });
  });
});