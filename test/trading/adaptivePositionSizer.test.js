const AdaptivePositionSizer = require('../../lib/trading/adaptivePositionSizer');

describe('AdaptivePositionSizer', () => {
  let positionSizer;
  const mockPortfolioValue = 10000;

  beforeEach(() => {
    positionSizer = new AdaptivePositionSizer({
      baseRiskPercent: 1.0,
      maxRiskPercent: 3.0,
      minPositionSize: 0.001,
      maxPositionSize: 0.1
    });
  });

  describe('calculateOptimalSize', () => {
    it('should calculate basic position size correctly', async () => {
      const signal = {
        symbol: 'BTCUSDT',
        entry: 50000,
        stopLoss: 49000,
        qualityScore: 80
      };

      const riskAssessment = {
        positionSizeMultiplier: 1.0
      };

      const result = await positionSizer.calculateOptimalSize(
        signal,
        riskAssessment,
        mockPortfolioValue
      );

      expect(result.finalSize).toBeGreaterThan(0);
      expect(result.riskAmount).toBe(mockPortfolioValue * 0.01); // 1% base risk
      expect(result.multipliers).toHaveProperty('quality');
      expect(result.multipliers).toHaveProperty('symbol');
      expect(result.multipliers).toHaveProperty('time');
      expect(result.multipliers).toHaveProperty('volatility');
    });

    it('should apply quality multiplier correctly', async () => {
      const highQualitySignal = {
        symbol: 'BTCUSDT',
        entry: 50000,
        stopLoss: 49000,
        qualityScore: 95
      };

      const lowQualitySignal = {
        symbol: 'BTCUSDT',
        entry: 50000,
        stopLoss: 49000,
        qualityScore: 55
      };

      const riskAssessment = { positionSizeMultiplier: 1.0 };

      const highQualityResult = await positionSizer.calculateOptimalSize(
        highQualitySignal,
        riskAssessment,
        mockPortfolioValue
      );

      const lowQualityResult = await positionSizer.calculateOptimalSize(
        lowQualitySignal,
        riskAssessment,
        mockPortfolioValue
      );

      expect(highQualityResult.multipliers.quality).toBeGreaterThan(
        lowQualityResult.multipliers.quality
      );
    });

    it('should handle zero stop loss distance', async () => {
      const signal = {
        symbol: 'BTCUSDT',
        entry: 50000,
        stopLoss: 50000, // Same as entry
        qualityScore: 80
      };

      const riskAssessment = { positionSizeMultiplier: 1.0 };

      const result = await positionSizer.calculateOptimalSize(
        signal,
        riskAssessment,
        mockPortfolioValue
      );

      expect(result.finalSize).toBe(positionSizer.config.minPositionSize);
    });
  });

  describe('getQualityMultiplier', () => {
    it('should return correct multipliers for different quality scores', () => {
      expect(positionSizer.getQualityMultiplier(95)).toBe(1.5); // excellent
      expect(positionSizer.getQualityMultiplier(85)).toBe(1.2); // good
      expect(positionSizer.getQualityMultiplier(75)).toBe(1.0); // average
      expect(positionSizer.getQualityMultiplier(65)).toBe(0.5); // weak
      expect(positionSizer.getQualityMultiplier(55)).toBe(0.2); // poor
    });
  });

  describe('getTimeBasedMultiplier', () => {
    it('should return different multipliers for different hours', () => {
      // Mock different hours
      const originalDate = Date;
      
      // Peak hour (9 UTC)
      global.Date = jest.fn(() => ({ getUTCHours: () => 9 }));
      const peakMultiplier = positionSizer.getTimeBasedMultiplier();
      
      // Poor hour (2 UTC)
      global.Date = jest.fn(() => ({ getUTCHours: () => 2 }));
      const poorMultiplier = positionSizer.getTimeBasedMultiplier();
      
      global.Date = originalDate;
      
      expect(peakMultiplier).toBeGreaterThan(poorMultiplier);
    });
  });

  describe('validatePositionSize', () => {
    it('should validate minimum position size', async () => {
      const signal = { symbol: 'BTCUSDT', entry: 50000, stopLoss: 49000 };
      const tooSmallSize = 0.0001;
      
      const validation = await positionSizer.validatePositionSize(
        tooSmallSize,
        100,
        signal,
        mockPortfolioValue,
        {}
      );

      expect(validation.isValid).toBe(false);
      expect(validation.adjustedSize).toBe(positionSizer.config.minPositionSize);
      expect(validation.violations).toContain('below_minimum_size');
    });

    it('should validate maximum position size', async () => {
      const signal = { symbol: 'BTCUSDT', entry: 50000, stopLoss: 49000 };
      const tooLargeSize = mockPortfolioValue * 0.2; // 20% of portfolio
      
      const validation = await positionSizer.validatePositionSize(
        tooLargeSize,
        100,
        signal,
        mockPortfolioValue,
        {}
      );

      expect(validation.isValid).toBe(false);
      expect(validation.adjustedSize).toBe(mockPortfolioValue * 0.1); // Max 10%
      expect(validation.violations).toContain('above_maximum_size');
    });

    it('should validate daily risk limit', async () => {
      const signal = { symbol: 'BTCUSDT', entry: 50000, stopLoss: 49000 };
      
      // Set daily risk to near limit
      positionSizer.dailyRiskUsed = 4.5; // 4.5% already used
      
      const validation = await positionSizer.validatePositionSize(
        100,
        mockPortfolioValue * 0.02, // 2% risk (would exceed 5% limit)
        signal,
        mockPortfolioValue,
        {}
      );

      expect(validation.violations).toContain('daily_risk_adjusted');
    });
  });

  describe('updateSymbolPerformance', () => {
    it('should update symbol performance correctly', () => {
      const symbol = 'BTCUSDT';
      const tradeResult = {
        pnl: 100,
        pnlPercent: 2.0,
        exitReason: 'take_profit'
      };

      positionSizer.updateSymbolPerformance(symbol, tradeResult);

      const performance = positionSizer.symbolPerformance.get(symbol);
      expect(performance).toBeDefined();
      expect(performance.trades).toHaveLength(1);
      expect(performance.totalPnl).toBe(100);
    });

    it('should calculate win rate correctly', () => {
      const symbol = 'BTCUSDT';
      
      // Add winning trades
      positionSizer.updateSymbolPerformance(symbol, { pnl: 100, pnlPercent: 2.0 });
      positionSizer.updateSymbolPerformance(symbol, { pnl: 50, pnlPercent: 1.0 });
      
      // Add losing trade
      positionSizer.updateSymbolPerformance(symbol, { pnl: -30, pnlPercent: -0.6 });

      const performance = positionSizer.symbolPerformance.get(symbol);
      expect(performance.winRate).toBeCloseTo(66.67, 1); // 2 wins out of 3 trades
    });
  });

  describe('updateRiskTracking', () => {
    it('should update daily and weekly risk correctly', () => {
      const riskAmount = 100;
      
      positionSizer.updateRiskTracking(riskAmount, mockPortfolioValue);
      
      expect(positionSizer.dailyRiskUsed).toBe(1.0); // 1% risk
      expect(positionSizer.weeklyRiskUsed).toBe(1.0);
    });

    it('should reset daily risk on new day', () => {
      positionSizer.dailyRiskUsed = 2.0;
      
      // Mock new day
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      positionSizer.lastResetDate = new Date(); // Yesterday
      
      const originalDate = Date;
      global.Date = jest.fn(() => tomorrow);
      
      positionSizer.updateRiskTracking(100, mockPortfolioValue);
      
      global.Date = originalDate;
      
      expect(positionSizer.dailyRiskUsed).toBe(1.0); // Reset + new risk
    });
  });

  describe('getPositionSizingRecommendations', () => {
    it('should provide quality-based recommendations', async () => {
      const signal = {
        symbol: 'BTCUSDT',
        entry: 50000,
        stopLoss: 49000,
        qualityScore: 95 // High quality
      };

      const recommendations = await positionSizer.getPositionSizingRecommendations(
        signal.symbol,
        signal,
        mockPortfolioValue
      );

      const qualityRec = recommendations.find(r => r.type === 'quality');
      expect(qualityRec).toBeDefined();
      expect(qualityRec.message).toContain('High quality signal');
    });

    it('should provide risk limit recommendations', async () => {
      const signal = {
        symbol: 'BTCUSDT',
        entry: 50000,
        stopLoss: 49000,
        qualityScore: 80
      };

      // Set daily risk near limit
      positionSizer.dailyRiskUsed = 4.5; // 90% of 5% limit

      const recommendations = await positionSizer.getPositionSizingRecommendations(
        signal.symbol,
        signal,
        mockPortfolioValue
      );

      const riskRec = recommendations.find(r => r.type === 'risk_limit');
      expect(riskRec).toBeDefined();
      expect(riskRec.message).toContain('daily risk limit');
    });
  });

  describe('utility methods', () => {
    it('should reset risk tracking correctly', () => {
      positionSizer.dailyRiskUsed = 3.0;
      positionSizer.weeklyRiskUsed = 8.0;
      
      positionSizer.resetRiskTracking();
      
      expect(positionSizer.dailyRiskUsed).toBe(0);
      expect(positionSizer.weeklyRiskUsed).toBe(0);
    });

    it('should reset symbol performance correctly', () => {
      positionSizer.updateSymbolPerformance('BTCUSDT', { pnl: 100, pnlPercent: 2.0 });
      positionSizer.updateSymbolPerformance('ETHUSDT', { pnl: 50, pnlPercent: 1.0 });
      
      expect(positionSizer.symbolPerformance.size).toBe(2);
      
      positionSizer.resetSymbolPerformance('BTCUSDT');
      expect(positionSizer.symbolPerformance.size).toBe(1);
      expect(positionSizer.symbolPerformance.has('BTCUSDT')).toBe(false);
      
      positionSizer.resetSymbolPerformance();
      expect(positionSizer.symbolPerformance.size).toBe(0);
    });

    it('should provide configuration summary', () => {
      const summary = positionSizer.getConfigSummary();
      
      expect(summary).toHaveProperty('baseRiskPercent');
      expect(summary).toHaveProperty('maxRiskPercent');
      expect(summary).toHaveProperty('safetyLimits');
      expect(summary).toHaveProperty('qualityMultipliers');
    });
  });
});
