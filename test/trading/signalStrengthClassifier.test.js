const { describe, it, expect, beforeEach, vi } = require('vitest');
const SignalStrengthClassifier = require('../../lib/trading/signalStrengthClassifier');

// Mock logger
vi.mock('../../lib/logger');

describe('SignalStrengthClassifier', () => {
  let classifier;
  let mockSignal;
  let mockCandles;

  beforeEach(() => {
    classifier = new SignalStrengthClassifier();

    // Mock signal data
    mockSignal = {
      symbol: 'BTCUSDT',
      timeframe: '1h',
      type: 'BUY',
      entry: 50000,
      stopLoss: 49000,
      takeProfit: 52000,
      riskReward: 2.0,
      indicators: {
        ema50: 49500,
        ema200: 48000,
        macd: {
          macd: 100,
          signal: 90,
          histogram: 10
        },
        rsi: 60,
        strongBody: {
          isStrong: true,
          bodyPercent: 70
        },
        engulfing: 'bullish'
      }
    };

    // Mock candle data
    mockCandles = Array.from({ length: 50 }, (_, i) => ({
      openTime: new Date(Date.now() - (50 - i) * 60000),
      open: 49000 + i * 20,
      high: 49200 + i * 20,
      low: 48800 + i * 20,
      close: 49000 + i * 20 + 10,
      volume: 1000 + Math.random() * 500
    }));
  });

  describe('classifySignalStrength', () => {
    it('should return insufficient_data for invalid inputs', async () => {
      const result = await classifier.classifySignalStrength(null, mockCandles);

      expect(result.strength).toBe('insufficient_data');
      expect(result.score).toBe(0);
      expect(result.reason).toContain('Insufficient data');
    });

    it('should classify strong signal correctly', async () => {
      const result = await classifier.classifySignalStrength(mockSignal, mockCandles);

      expect(result.strength).toBe('strong');
      expect(result.score).toBeGreaterThan(0.75);
      expect(result.breakdown).toBeDefined();
      expect(result.components).toBeDefined();
    });

    it('should classify medium signal correctly', async () => {
      // Modify signal to be medium strength
      mockSignal.indicators.strongBody.bodyPercent = 50;
      mockSignal.indicators.engulfing = 'none';
      mockSignal.riskReward = 1.5;

      const result = await classifier.classifySignalStrength(mockSignal, mockCandles);

      expect(result.score).toBeGreaterThan(0.5);
      expect(result.score).toBeLessThan(0.75);
    });

    it('should classify weak signal correctly', async () => {
      // Modify signal to be weak
      mockSignal.indicators.ema50 = 50500; // Wrong EMA alignment
      mockSignal.indicators.macd.histogram = -5; // Wrong MACD
      mockSignal.indicators.rsi = 45; // Wrong RSI zone
      mockSignal.indicators.strongBody.isStrong = false;
      mockSignal.indicators.engulfing = 'none';
      mockSignal.riskReward = 1.1;

      const result = await classifier.classifySignalStrength(mockSignal, mockCandles);

      expect(result.score).toBeLessThan(0.5);
    });

    it('should handle errors gracefully', async () => {
      // Mock error in calculation
      vi.spyOn(classifier, 'calculateStrengthComponents').mockImplementation(() => {
        throw new Error('Test error');
      });

      const result = await classifier.classifySignalStrength(mockSignal, mockCandles);

      expect(result.strength).toBe('error');
      expect(result.score).toBe(0);
      expect(result.reason).toContain('Error in strength classification');
    });
  });

  describe('calculateStrengthComponents', () => {
    it('should calculate all strength components', () => {
      const components = classifier.calculateStrengthComponents(mockSignal, mockCandles);

      expect(components).toHaveProperty('technicalAlignment');
      expect(components).toHaveProperty('patternStrength');
      expect(components).toHaveProperty('momentumConfirmation');
      expect(components).toHaveProperty('volumeSupport');
      expect(components).toHaveProperty('marketStructure');
      expect(components).toHaveProperty('riskReward');

      // All components should be between 0 and 1
      Object.values(components).forEach(value => {
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('assessTechnicalAlignment', () => {
    it('should give high score for perfect BUY alignment', () => {
      const score = classifier.assessTechnicalAlignment(mockSignal, mockCandles);

      expect(score).toBeGreaterThan(0.7);
    });

    it('should give low score for wrong alignment', () => {
      // Wrong alignment for BUY signal
      mockSignal.indicators.ema50 = 47000; // Below EMA200
      mockSignal.indicators.macd.histogram = -10; // Negative histogram
      mockSignal.indicators.rsi = 45; // Wrong RSI zone

      const score = classifier.assessTechnicalAlignment(mockSignal, mockCandles);

      expect(score).toBeLessThan(0.5);
    });

    it('should handle SELL signals correctly', () => {
      mockSignal.type = 'SELL';
      mockSignal.entry = 47000;
      mockSignal.indicators.ema50 = 47500; // Below EMA200 for SELL
      mockSignal.indicators.ema200 = 48000;
      mockSignal.indicators.macd.macd = -100;
      mockSignal.indicators.macd.signal = -90;
      mockSignal.indicators.macd.histogram = -10;
      mockSignal.indicators.rsi = 40;

      const score = classifier.assessTechnicalAlignment(mockSignal, mockCandles);

      expect(score).toBeGreaterThan(0.5);
    });

    it('should handle missing indicators', () => {
      mockSignal.indicators = {};

      const score = classifier.assessTechnicalAlignment(mockSignal, mockCandles);

      expect(score).toBe(0);
    });
  });

  describe('assessPatternStrength', () => {
    it('should give high score for strong patterns', () => {
      const score = classifier.assessPatternStrength(mockSignal, mockCandles);

      expect(score).toBeGreaterThan(0.7);
    });

    it('should give medium score for partial patterns', () => {
      mockSignal.indicators.strongBody.bodyPercent = 50;
      mockSignal.indicators.engulfing = 'none';

      const score = classifier.assessPatternStrength(mockSignal, mockCandles);

      expect(score).toBeGreaterThan(0.2);
      expect(score).toBeLessThan(0.7);
    });

    it('should give low score for weak patterns', () => {
      mockSignal.indicators.strongBody.isStrong = false;
      mockSignal.indicators.strongBody.bodyPercent = 30;
      mockSignal.indicators.engulfing = 'none';

      const score = classifier.assessPatternStrength(mockSignal, mockCandles);

      expect(score).toBeLessThan(0.5);
    });
  });

  describe('assessVolumeSupport', () => {
    it('should give high score for high volume', () => {
      // Set last candle to have high volume
      mockCandles[mockCandles.length - 1].volume = 3000; // 3x average

      const score = classifier.assessVolumeSupport(mockSignal, mockCandles);

      expect(score).toBeGreaterThan(0.6);
    });

    it('should give low score for low volume', () => {
      // Set all candles to have low volume
      mockCandles.forEach(candle => {
        candle.volume = 500;
      });

      const score = classifier.assessVolumeSupport(mockSignal, mockCandles);

      expect(score).toBeLessThan(0.5);
    });

    it('should handle insufficient candles', () => {
      const smallCandles = mockCandles.slice(0, 5);

      const score = classifier.assessVolumeSupport(mockSignal, smallCandles);

      expect(score).toBe(0);
    });
  });

  describe('assessRiskReward', () => {
    it('should give high score for excellent RR', () => {
      mockSignal.riskReward = 3.5;

      const score = classifier.assessRiskReward(mockSignal);

      expect(score).toBe(1.0);
    });

    it('should give medium score for good RR', () => {
      mockSignal.riskReward = 2.0;

      const score = classifier.assessRiskReward(mockSignal);

      expect(score).toBe(0.7);
    });

    it('should give low score for poor RR', () => {
      mockSignal.riskReward = 1.1;

      const score = classifier.assessRiskReward(mockSignal);

      expect(score).toBe(0.1);
    });

    it('should handle missing RR', () => {
      delete mockSignal.riskReward;

      const score = classifier.assessRiskReward(mockSignal);

      expect(score).toBe(0);
    });
  });

  describe('calculateWeightedScore', () => {
    it('should calculate weighted score correctly', () => {
      const components = {
        technicalAlignment: 0.8,
        patternStrength: 0.7,
        momentumConfirmation: 0.6,
        volumeSupport: 0.5,
        marketStructure: 0.4,
        riskReward: 0.9
      };

      const score = classifier.calculateWeightedScore(components);

      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(1);

      // Should be weighted average
      const expectedScore =
        (0.8 * 0.25) + // technicalAlignment
        (0.7 * 0.20) + // patternStrength
        (0.6 * 0.20) + // momentumConfirmation
        (0.5 * 0.15) + // volumeSupport
        (0.4 * 0.10) + // marketStructure
        (0.9 * 0.10);  // riskReward

      expect(score).toBeCloseTo(expectedScore, 2);
    });

    it('should handle missing components', () => {
      const components = {
        technicalAlignment: 0.8,
        patternStrength: 0.7
        // Missing other components
      };

      const score = classifier.calculateWeightedScore(components);

      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThan(1);
    });
  });

  describe('classifyByScore', () => {
    it('should classify scores correctly', () => {
      expect(classifier.classifyByScore(0.85)).toBe('strong');
      expect(classifier.classifyByScore(0.65)).toBe('medium');
      expect(classifier.classifyByScore(0.35)).toBe('weak');
      expect(classifier.classifyByScore(0.15)).toBe('very_weak');
    });
  });

  describe('generateBreakdown', () => {
    it('should generate detailed breakdown', () => {
      const components = {
        technicalAlignment: 0.8,
        patternStrength: 0.7,
        momentumConfirmation: 0.6,
        volumeSupport: 0.5,
        marketStructure: 0.4,
        riskReward: 0.9
      };
      const totalScore = 0.7;

      const breakdown = classifier.generateBreakdown(components, totalScore);

      expect(breakdown).toHaveProperty('technicalAlignment');
      expect(breakdown).toHaveProperty('total');

      // Check structure of component breakdown
      const techAlignment = breakdown.technicalAlignment;
      expect(techAlignment).toHaveProperty('score');
      expect(techAlignment).toHaveProperty('weight');
      expect(techAlignment).toHaveProperty('contribution');
      expect(techAlignment).toHaveProperty('rating');

      // Check total
      expect(breakdown.total.score).toBe(70); // 0.7 * 100
      expect(breakdown.total.rating).toBe('medium');
    });
  });

  describe('helper methods', () => {
    describe('checkPatternConsistency', () => {
      it('should return high consistency for aligned patterns', () => {
        // Create bullish candles for BUY signal
        const bullishCandles = mockCandles.slice(-5).map(candle => ({
          ...candle,
          close: candle.open + 10 // Bullish candles
        }));

        const consistency = classifier.checkPatternConsistency(bullishCandles, 'BUY');

        expect(consistency).toBe(1.0);
      });

      it('should return low consistency for misaligned patterns', () => {
        // Create bearish candles for BUY signal
        const bearishCandles = mockCandles.slice(-5).map(candle => ({
          ...candle,
          close: candle.open - 10 // Bearish candles
        }));

        const consistency = classifier.checkPatternConsistency(bearishCandles, 'BUY');

        expect(consistency).toBe(0);
      });
    });

    describe('calculatePriceMomentum', () => {
      it('should return high momentum for trending price', () => {
        // Create trending candles
        const trendingCandles = mockCandles.map((candle, i) => ({
          ...candle,
          close: 49000 + i * 50 // Strong uptrend
        }));

        const momentum = classifier.calculatePriceMomentum(trendingCandles, 'BUY');

        expect(momentum).toBeGreaterThan(0.5);
      });

      it('should return low momentum for sideways price', () => {
        // Create sideways candles
        const sidewaysCandles = mockCandles.map(candle => ({
          ...candle,
          close: 49000 + (Math.random() - 0.5) * 100 // Random around 49000
        }));

        const momentum = classifier.calculatePriceMomentum(sidewaysCandles, 'BUY');

        expect(momentum).toBeLessThan(0.5);
      });
    });

    describe('calculateSlope', () => {
      it('should calculate positive slope correctly', () => {
        const data = [1, 2, 3, 4, 5];

        const slope = classifier.calculateSlope(data);

        expect(slope).toBeGreaterThan(0);
      });

      it('should calculate negative slope correctly', () => {
        const data = [5, 4, 3, 2, 1];

        const slope = classifier.calculateSlope(data);

        expect(slope).toBeLessThan(0);
      });

      it('should handle insufficient data', () => {
        const data = [1];

        const slope = classifier.calculateSlope(data);

        expect(slope).toBe(0);
      });
    });

    describe('getRatingFromScore', () => {
      it('should return correct ratings', () => {
        expect(classifier.getRatingFromScore(0.9)).toBe('excellent');
        expect(classifier.getRatingFromScore(0.7)).toBe('good');
        expect(classifier.getRatingFromScore(0.5)).toBe('fair');
        expect(classifier.getRatingFromScore(0.3)).toBe('poor');
        expect(classifier.getRatingFromScore(0.1)).toBe('very_poor');
      });
    });
  });
});