const AITrailingManager = require('../../lib/trading/aiTrailingManager');

describe('AITrailingManager', () => {
  let aiTrailingManager;
  let mockSignal;

  beforeEach(() => {
    aiTrailingManager = new AITrailingManager({
      aiTrailing: {
        enabled: true,
        confidenceThreshold: 0.7,
        adaptiveTrailingDistance: true,
        marketConditionAware: true
      },
      partialExit: {
        enabled: true,
        firstExitPercent: 50,
        firstExitRR: 1.5,
        secondExitPercent: 30,
        secondExitRR: 2.5
      }
    });

    mockSignal = {
      _id: 'test-signal-123',
      symbol: 'BTCUSDT',
      type: 'BUY',
      entry: 50000,
      stopLoss: 49000,
      takeProfit: 52000
    };
  });

  describe('addSignalToAITrailing', () => {
    it('should add signal to AI trailing management', async () => {
      const aiAnalysis = {
        confidence: 0.8,
        marketCondition: 'trending',
        trailingStrategy: 'aggressive'
      };

      const result = await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);

      expect(result).toBeDefined();
      expect(result.confidence).toBe(0.8);
      expect(result.marketCondition).toBe('trending');
      expect(result.partialExitTargets).toHaveLength(2);
      expect(aiTrailingManager.aiTrailingData.has(mockSignal._id)).toBe(true);
    });

    it('should calculate partial exit targets correctly', async () => {
      const aiAnalysis = { confidence: 0.7 };
      const result = await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);

      const targets = result.partialExitTargets;
      expect(targets).toHaveLength(2);
      
      // First target: entry + (risk * 1.5)
      const riskAmount = mockSignal.entry - mockSignal.stopLoss; // 1000
      const expectedFirstTarget = mockSignal.entry + (riskAmount * 1.5); // 51500
      expect(targets[0].price).toBe(expectedFirstTarget);
      expect(targets[0].percentage).toBe(50);

      // Second target: entry + (risk * 2.5)
      const expectedSecondTarget = mockSignal.entry + (riskAmount * 2.5); // 52500
      expect(targets[1].price).toBe(expectedSecondTarget);
      expect(targets[1].percentage).toBe(30);
    });
  });

  describe('updateAITrailing', () => {
    beforeEach(async () => {
      const aiAnalysis = { confidence: 0.8, marketCondition: 'trending' };
      await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);
    });

    it('should update performance metrics correctly', async () => {
      const currentPrice = 51000; // 2% profit
      
      const result = await aiTrailingManager.updateAITrailing(
        mockSignal._id,
        currentPrice,
        { volatility: 0.03, trend: 0.02 }
      );

      const aiData = aiTrailingManager.aiTrailingData.get(mockSignal._id);
      expect(aiData.performanceMetrics.currentExcursion).toBeCloseTo(0.02, 2);
      expect(aiData.performanceMetrics.maxFavorableExcursion).toBeCloseTo(0.02, 2);
    });

    it('should execute partial exits when targets are reached', async () => {
      const firstTargetPrice = 51500; // First partial exit target
      
      const result = await aiTrailingManager.updateAITrailing(
        mockSignal._id,
        firstTargetPrice
      );

      expect(result.partialExitResult.executed).toBe(true);
      expect(result.partialExitResult.type).toBe('partial_exit_1');
      expect(result.partialExitResult.percentage).toBe(50);

      const aiData = aiTrailingManager.aiTrailingData.get(mockSignal._id);
      expect(aiData.partialExitTargets[0].executed).toBe(true);
    });

    it('should update trailing stop when price moves favorably', async () => {
      const currentPrice = 52000; // Significant profit
      
      const result = await aiTrailingManager.updateAITrailing(
        mockSignal._id,
        currentPrice,
        { volatility: 0.03, trend: 0.02 }
      );

      const trailingData = aiTrailingManager.trailingSignals.get(mockSignal._id);
      expect(trailingData.highestPrice).toBe(currentPrice);
      
      if (result.trailingUpdate.activated) {
        expect(result.trailingUpdate.newStopLoss).toBeGreaterThan(mockSignal.stopLoss);
      }
    });
  });

  describe('calculateAdaptiveTrailingDistance', () => {
    it('should adjust trailing distance based on confidence', () => {
      const mockTrailingData = { type: 'BUY', entry: 50000 };
      const highConfidenceAI = { 
        confidence: 0.9, 
        performanceMetrics: { trailingEfficiency: 0.8, currentExcursion: 0.02 } 
      };
      const lowConfidenceAI = { 
        confidence: 0.3, 
        performanceMetrics: { trailingEfficiency: 0.8, currentExcursion: 0.02 } 
      };

      const highConfidenceDistance = aiTrailingManager.calculateAdaptiveTrailingDistance(
        'test-id', 51000, mockTrailingData, highConfidenceAI, 'trending'
      );
      
      const lowConfidenceDistance = aiTrailingManager.calculateAdaptiveTrailingDistance(
        'test-id', 51000, mockTrailingData, lowConfidenceAI, 'trending'
      );

      expect(highConfidenceDistance).toBeLessThan(lowConfidenceDistance);
    });

    it('should adjust trailing distance based on market condition', () => {
      const mockTrailingData = { type: 'BUY', entry: 50000 };
      const mockAI = { 
        confidence: 0.7, 
        performanceMetrics: { trailingEfficiency: 0.8, currentExcursion: 0.02 } 
      };

      const trendingDistance = aiTrailingManager.calculateAdaptiveTrailingDistance(
        'test-id', 51000, mockTrailingData, mockAI, 'trending'
      );
      
      const volatileDistance = aiTrailingManager.calculateAdaptiveTrailingDistance(
        'test-id', 51000, mockTrailingData, mockAI, 'volatile'
      );

      expect(volatileDistance).toBeGreaterThan(trendingDistance);
    });

    it('should clamp trailing distance within bounds', () => {
      const mockTrailingData = { type: 'BUY', entry: 50000 };
      const extremeAI = { 
        confidence: 0.1, 
        performanceMetrics: { trailingEfficiency: 0.1, currentExcursion: 0.001 } 
      };

      const distance = aiTrailingManager.calculateAdaptiveTrailingDistance(
        'test-id', 51000, mockTrailingData, extremeAI, 'volatile'
      );

      expect(distance).toBeGreaterThanOrEqual(0.003); // Min 0.3%
      expect(distance).toBeLessThanOrEqual(0.025);    // Max 2.5%
    });
  });

  describe('getAITrailingRecommendations', () => {
    beforeEach(async () => {
      const aiAnalysis = { confidence: 0.8, marketCondition: 'trending' };
      await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);
    });

    it('should provide profit-taking recommendations for high profits', async () => {
      const highProfitPrice = 55000; // 10% profit
      
      const recommendations = await aiTrailingManager.getAITrailingRecommendations(
        mockSignal._id,
        highProfitPrice,
        { volatility: 0.03 }
      );

      expect(recommendations.recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'profit_taking',
            action: 'Consider taking partial profits'
          })
        ])
      );
    });

    it('should provide breakeven recommendations for moderate profits', async () => {
      const moderateProfitPrice = 50600; // 1.2% profit
      
      const recommendations = await aiTrailingManager.getAITrailingRecommendations(
        mockSignal._id,
        moderateProfitPrice,
        { volatility: 0.03 }
      );

      expect(recommendations.recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'breakeven',
            action: 'Move stop loss to breakeven'
          })
        ])
      );
    });

    it('should provide volatility-based recommendations', async () => {
      const currentPrice = 51000;
      
      const recommendations = await aiTrailingManager.getAITrailingRecommendations(
        mockSignal._id,
        currentPrice,
        { volatility: 0.08 } // High volatility
      );

      expect(recommendations.recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'risk_management',
            action: 'Use wider trailing stops'
          })
        ])
      );
    });
  });

  describe('updatePerformanceHistory', () => {
    beforeEach(async () => {
      const aiAnalysis = { confidence: 0.8 };
      await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);
    });

    it('should update performance history correctly', () => {
      const tradeResult = {
        symbol: 'BTCUSDT',
        pnl: 500,
        pnlPercent: 1.0,
        exitReason: 'take_profit'
      };

      aiTrailingManager.updatePerformanceHistory(mockSignal._id, tradeResult);

      const history = aiTrailingManager.performanceHistory.get('BTCUSDT');
      expect(history).toHaveLength(1);
      expect(history[0].pnl).toBe(500);
      expect(history[0].pnlPercent).toBe(1.0);
    });

    it('should limit history to 100 records per symbol', () => {
      const tradeResult = {
        symbol: 'BTCUSDT',
        pnl: 100,
        pnlPercent: 0.2,
        exitReason: 'trailing_stop'
      };

      // Add 105 records
      for (let i = 0; i < 105; i++) {
        aiTrailingManager.updatePerformanceHistory(mockSignal._id, {
          ...tradeResult,
          pnl: i
        });
      }

      const history = aiTrailingManager.performanceHistory.get('BTCUSDT');
      expect(history).toHaveLength(100);
      expect(history[0].pnl).toBe(5); // First 5 records should be removed
    });
  });

  describe('getPerformanceAnalytics', () => {
    beforeEach(async () => {
      const aiAnalysis = { confidence: 0.8 };
      await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);

      // Add some performance history
      const winningTrade = {
        symbol: 'BTCUSDT',
        pnl: 500,
        pnlPercent: 1.0,
        exitReason: 'take_profit'
      };

      const losingTrade = {
        symbol: 'BTCUSDT',
        pnl: -200,
        pnlPercent: -0.4,
        exitReason: 'stop_loss'
      };

      aiTrailingManager.updatePerformanceHistory(mockSignal._id, winningTrade);
      aiTrailingManager.updatePerformanceHistory(mockSignal._id, winningTrade);
      aiTrailingManager.updatePerformanceHistory(mockSignal._id, losingTrade);
    });

    it('should calculate performance analytics correctly', () => {
      const analytics = aiTrailingManager.getPerformanceAnalytics('BTCUSDT');

      expect(analytics.totalTrades).toBe(3);
      expect(analytics.winRate).toBeCloseTo(66.67, 1); // 2 wins out of 3
      expect(analytics.avgPnl).toBeCloseTo(0.53, 1); // (1.0 + 1.0 - 0.4) / 3
    });

    it('should return default values for symbols with no history', () => {
      const analytics = aiTrailingManager.getPerformanceAnalytics('ETHUSDT');

      expect(analytics.totalTrades).toBe(0);
      expect(analytics.winRate).toBe(0);
      expect(analytics.avgPnl).toBe(0);
    });
  });

  describe('utility methods', () => {
    beforeEach(async () => {
      const aiAnalysis = { confidence: 0.8 };
      await aiTrailingManager.addSignalToAITrailing(mockSignal, aiAnalysis);
    });

    it('should remove signal from AI trailing correctly', () => {
      const removed = aiTrailingManager.removeSignalFromAITrailing(mockSignal._id);

      expect(removed).toBe(true);
      expect(aiTrailingManager.trailingSignals.has(mockSignal._id)).toBe(false);
      expect(aiTrailingManager.aiTrailingData.has(mockSignal._id)).toBe(false);
    });

    it('should get active AI trailing signals', () => {
      const activeSignals = aiTrailingManager.getActiveAITrailingSignals();

      expect(activeSignals).toHaveLength(1);
      expect(activeSignals[0].signalId).toBe(mockSignal._id);
      expect(activeSignals[0].symbol).toBe('BTCUSDT');
      expect(activeSignals[0].confidence).toBe(0.8);
    });

    it('should get and update AI trailing configuration', () => {
      const config = aiTrailingManager.getAITrailingConfig();
      expect(config.aiTrailing.enabled).toBe(true);

      const updated = aiTrailingManager.updateAITrailingConfig({
        aiTrailing: { enabled: false }
      });

      expect(updated).toBe(true);
      expect(aiTrailingManager.config.aiTrailing.enabled).toBe(false);
    });
  });
});
