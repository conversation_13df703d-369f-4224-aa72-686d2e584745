const assert = require('assert');
const EnhancedSignalAnalyzer = require('../../lib/trading/enhancedSignalAnalyzer');

// Simple test framework
function describe(name, fn) {
  console.log(`\n=== ${name} ===`);
  fn();
}

function it(name, fn) {
  try {
    fn();
    console.log(`✓ ${name}`);
  } catch (error) {
    console.log(`✗ ${name}: ${error.message}`);
  }
}

function expect(actual) {
  return {
    toBe: (expected) => assert.strictEqual(actual, expected),
    toBeNull: () => assert.strictEqual(actual, null),
    toBeGreaterThan: (expected) => assert(actual > expected, `Expected ${actual} > ${expected}`),
    toBeLessThan: (expected) => assert(actual < expected, `Expected ${actual} < ${expected}`),
    toBeLessThanOrEqual: (expected) => assert(actual <= expected, `Expected ${actual} <= ${expected}`),
    toContain: (expected) => assert(actual.includes(expected), `Expected ${actual} to contain ${expected}`),
    toBeDefined: () => assert(actual !== undefined, `Expected ${actual} to be defined`),
    toHaveProperty: (prop) => assert(actual.hasOwnProperty(prop), `Expected ${actual} to have property ${prop}`)
  };
}

describe('EnhancedSignalAnalyzer', () => {
  let analyzer;
  let mockCandles;
  let mockBaseSignal;

  beforeEach(() => {
    analyzer = new EnhancedSignalAnalyzer();

    // Mock candle data
    mockCandles = Array.from({ length: 100 }, (_, i) => ({
      openTime: new Date(Date.now() - (100 - i) * 60000),
      open: 100 + Math.random() * 10,
      high: 105 + Math.random() * 10,
      low: 95 + Math.random() * 10,
      close: 100 + Math.random() * 10,
      volume: 1000 + Math.random() * 500
    }));

    // Mock base signal
    mockBaseSignal = {
      symbol: 'BTCUSDT',
      timeframe: '1h',
      type: 'BUY',
      entry: 50000,
      stopLoss: 49000,
      takeProfit: 52000,
      riskReward: 2.0,
      indicators: {
        ema50: 49500,
        ema200: 48000,
        macd: { macd: 100, signal: 90, histogram: 10 },
        rsi: 60
      }
    };

    // Setup mocks
    analyzer.volumeAnalyzer = {
      analyzeVolumeConfirmation: vi.fn().mockResolvedValue({
        isAboveAverage: true,
        volumeRatio: 1.8,
        strength: 'moderate'
      })
    };

    analyzer.marketRegimeDetector = {
      detectMarketRegime: vi.fn().mockResolvedValue({
        regime: 'trending',
        confidence: 0.8
      })
    };

    analyzer.strengthClassifier = {
      classifySignalStrength: vi.fn().mockResolvedValue({
        strength: 'strong',
        score: 0.85
      })
    };
  });

  describe('analyzeSignalWithEnhancements', () => {
    it('should return null when base signal is invalid', async () => {
      // Mock parent class to return null
      vi.spyOn(analyzer.__proto__.__proto__, 'analyzeSignal').mockResolvedValue(null);

      const result = await analyzer.analyzeSignalWithEnhancements('BTCUSDT', '1h', mockCandles);

      expect(result).toBeNull();
    });

    it('should return null when quality score is below threshold', async () => {
      // Mock parent class to return valid signal
      vi.spyOn(analyzer.__proto__.__proto__, 'analyzeSignal').mockResolvedValue(mockBaseSignal);

      // Mock low quality score
      vi.spyOn(analyzer, 'calculateQualityScore').mockReturnValue(60); // Below 70 threshold

      const result = await analyzer.analyzeSignalWithEnhancements('BTCUSDT', '1h', mockCandles);

      expect(result).toBeNull();
    });

    it('should return enhanced signal when quality score is above threshold', async () => {
      // Mock parent class to return valid signal
      vi.spyOn(analyzer.__proto__.__proto__, 'analyzeSignal').mockResolvedValue(mockBaseSignal);

      // Mock high quality score
      vi.spyOn(analyzer, 'calculateQualityScore').mockReturnValue(85);
      vi.spyOn(analyzer, 'calculateDynamicRR').mockReturnValue(2.5);

      const result = await analyzer.analyzeSignalWithEnhancements('BTCUSDT', '1h', mockCandles);

      expect(result).not.toBeNull();
      expect(result.qualityScore).toBe(85);
      expect(result.isValid).toBe(true);
      expect(result.recommendedRR).toBe(2.5);
      expect(result.enhancedAnalysis).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      // Mock parent class to throw error
      vi.spyOn(analyzer.__proto__.__proto__, 'analyzeSignal').mockRejectedValue(new Error('Test error'));

      const result = await analyzer.analyzeSignalWithEnhancements('BTCUSDT', '1h', mockCandles);

      expect(result).toBeNull();
    });
  });

  describe('calculateQualityScore', () => {
    let mockEnhancements;

    beforeEach(() => {
      mockEnhancements = {
        volumeConfirmation: {
          isAboveAverage: true,
          volumeRatio: 1.8
        },
        marketRegime: {
          regime: 'trending',
          confidence: 0.8
        },
        signalStrength: {
          strength: 'strong'
        },
        timeFilter: {
          isOptimal: true
        },
        supportResistanceFilter: {
          tooClose: false
        }
      };
    });

    it('should calculate high score for strong signal with all confirmations', () => {
      const score = analyzer.calculateQualityScore(mockBaseSignal, mockEnhancements);

      expect(score).toBeGreaterThan(70);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should reduce score for sideways market', () => {
      mockEnhancements.marketRegime.regime = 'sideways';

      const score = analyzer.calculateQualityScore(mockBaseSignal, mockEnhancements);

      expect(score).toBeLessThan(70);
    });

    it('should reduce score for weak signal strength', () => {
      mockEnhancements.signalStrength.strength = 'weak';

      const score = analyzer.calculateQualityScore(mockBaseSignal, mockEnhancements);

      expect(score).toBeLessThan(80);
    });

    it('should reduce score when too close to S/R levels', () => {
      mockEnhancements.supportResistanceFilter.tooClose = true;

      const score = analyzer.calculateQualityScore(mockBaseSignal, mockEnhancements);

      expect(score).toBeLessThan(90);
    });

    it('should handle missing volume confirmation', () => {
      mockEnhancements.volumeConfirmation.isAboveAverage = false;

      const score = analyzer.calculateQualityScore(mockBaseSignal, mockEnhancements);

      expect(score).toBeLessThan(85);
    });
  });

  describe('calculateDynamicRR', () => {
    it('should return higher RR for strong trending signals', () => {
      const signalStrength = { strength: 'strong' };
      const marketRegime = { regime: 'trending', confidence: 0.9 };

      const rr = analyzer.calculateDynamicRR(signalStrength, marketRegime);

      expect(rr).toBeGreaterThan(2.0);
      expect(rr).toBeLessThanOrEqual(3.0);
    });

    it('should return lower RR for weak sideways signals', () => {
      const signalStrength = { strength: 'weak' };
      const marketRegime = { regime: 'sideways', confidence: 0.7 };

      const rr = analyzer.calculateDynamicRR(signalStrength, marketRegime);

      expect(rr).toBeGreaterThanOrEqual(1.2);
      expect(rr).toBeLessThan(1.5);
    });

    it('should adjust RR based on confidence', () => {
      const signalStrength = { strength: 'medium' };
      const highConfidenceRegime = { regime: 'trending', confidence: 0.9 };
      const lowConfidenceRegime = { regime: 'trending', confidence: 0.3 };

      const highConfidenceRR = analyzer.calculateDynamicRR(signalStrength, highConfidenceRegime);
      const lowConfidenceRR = analyzer.calculateDynamicRR(signalStrength, lowConfidenceRegime);

      expect(highConfidenceRR).toBeGreaterThan(lowConfidenceRR);
    });
  });

  describe('checkOptimalTradingTime', () => {
    it('should identify low liquidity periods', () => {
      // Mock UTC hour to be in low liquidity period (23:00 UTC)
      const mockDate = new Date();
      mockDate.setUTCHours(23);
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const result = analyzer.checkOptimalTradingTime();

      expect(result.isOptimal).toBe(false);
      expect(result.reason).toContain('Low liquidity');
    });

    it('should identify optimal trading periods', () => {
      // Mock UTC hour to be in optimal period (10:00 UTC)
      const mockDate = new Date();
      mockDate.setUTCHours(10);
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const result = analyzer.checkOptimalTradingTime();

      expect(result.isOptimal).toBe(true);
      expect(result.reason).toContain('Optimal');
    });
  });

  describe('checkSRProximity', () => {
    it('should detect proximity to resistance levels', () => {
      const mockIndicators = {
        findAdvancedSupportResistance: vi.fn().mockReturnValue({
          supports: [],
          resistances: [
            { price: 50050, strength: 3 }, // Very close to entry (50000)
            { price: 51000, strength: 2 }
          ]
        })
      };

      // Mock the indicators module
      vi.doMock('../../lib/trading/indicators', () => mockIndicators);

      const result = analyzer.checkSRProximity(50000, mockCandles);

      expect(result.tooClose).toBe(true);
      expect(result.type).toBe('resistance');
    });

    it('should return false when not near S/R levels', () => {
      const mockIndicators = {
        findAdvancedSupportResistance: vi.fn().mockReturnValue({
          supports: [{ price: 48000, strength: 2 }],
          resistances: [{ price: 52000, strength: 2 }]
        })
      };

      vi.doMock('../../lib/trading/indicators', () => mockIndicators);

      const result = analyzer.checkSRProximity(50000, mockCandles);

      expect(result.tooClose).toBe(false);
    });

    it('should handle errors gracefully', () => {
      const mockIndicators = {
        findAdvancedSupportResistance: vi.fn().mockImplementation(() => {
          throw new Error('Test error');
        })
      };

      vi.doMock('../../lib/trading/indicators', () => mockIndicators);

      const result = analyzer.checkSRProximity(50000, mockCandles);

      expect(result.tooClose).toBe(false);
      expect(result.reason).toContain('Error');
    });
  });

  describe('applyEnhancedFilters', () => {
    it('should call all filter components', async () => {
      vi.spyOn(analyzer, 'checkOptimalTradingTime').mockReturnValue({ isOptimal: true });
      vi.spyOn(analyzer, 'checkSRProximity').mockReturnValue({ tooClose: false });

      const result = await analyzer.applyEnhancedFilters('BTCUSDT', '1h', mockCandles, mockBaseSignal);

      expect(analyzer.volumeAnalyzer.analyzeVolumeConfirmation).toHaveBeenCalledWith(mockCandles, 'BUY');
      expect(analyzer.marketRegimeDetector.detectMarketRegime).toHaveBeenCalledWith(mockCandles);
      expect(analyzer.strengthClassifier.classifySignalStrength).toHaveBeenCalledWith(mockBaseSignal, mockCandles);
      expect(analyzer.checkOptimalTradingTime).toHaveBeenCalled();
      expect(analyzer.checkSRProximity).toHaveBeenCalledWith(mockBaseSignal.entry, mockCandles);

      expect(result).toHaveProperty('volumeConfirmation');
      expect(result).toHaveProperty('marketRegime');
      expect(result).toHaveProperty('signalStrength');
      expect(result).toHaveProperty('timeFilter');
      expect(result).toHaveProperty('supportResistanceFilter');
    });
  });
});