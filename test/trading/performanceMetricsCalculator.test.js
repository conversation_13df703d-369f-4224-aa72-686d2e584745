const PerformanceMetricsCalculator = require('../../lib/trading/performanceMetricsCalculator');

describe('PerformanceMetricsCalculator', () => {
  let calculator;
  let mockTrades;

  beforeEach(() => {
    calculator = new PerformanceMetricsCalculator({
      riskFreeRate: 0.02,
      tradingDaysPerYear: 365,
      benchmarkReturn: 0.08
    });

    // Create mock trades data
    mockTrades = [
      { pnlPercent: 2.5, createdAt: new Date('2024-01-01') },
      { pnlPercent: -1.2, createdAt: new Date('2024-01-02') },
      { pnlPercent: 3.1, createdAt: new Date('2024-01-03') },
      { pnlPercent: -0.8, createdAt: new Date('2024-01-04') },
      { pnlPercent: 1.8, createdAt: new Date('2024-01-05') },
      { pnlPercent: -2.1, createdAt: new Date('2024-01-06') },
      { pnlPercent: 4.2, createdAt: new Date('2024-01-07') },
      { pnlPercent: 1.5, createdAt: new Date('2024-01-08') },
      { pnlPercent: -1.5, createdAt: new Date('2024-01-09') },
      { pnlPercent: 2.8, createdAt: new Date('2024-01-10') }
    ];
  });

  describe('calculateComprehensiveMetrics', () => {
    it('should calculate all metrics correctly for valid trades', () => {
      const metrics = calculator.calculateComprehensiveMetrics(mockTrades);

      expect(metrics.totalTrades).toBe(10);
      expect(metrics.winningTrades).toBe(6);
      expect(metrics.losingTrades).toBe(4);
      expect(metrics.winRate).toBe(60);
      expect(metrics.totalPnL).toBeCloseTo(9.3, 1);
      expect(metrics.profitFactor).toBeGreaterThan(1);
      expect(metrics.expectancy).toBeGreaterThan(0);
    });

    it('should return empty metrics for empty trades array', () => {
      const metrics = calculator.calculateComprehensiveMetrics([]);

      expect(metrics.totalTrades).toBe(0);
      expect(metrics.winRate).toBe(0);
      expect(metrics.totalPnL).toBe(0);
      expect(metrics.profitFactor).toBe(0);
      expect(metrics.sharpeRatio).toBe(0);
    });

    it('should handle trades with missing pnlPercent', () => {
      const tradesWithMissing = [
        { pnlPercent: 2.5 },
        { pnlPercent: null },
        { pnlPercent: undefined },
        { pnlPercent: -1.2 }
      ];

      const metrics = calculator.calculateComprehensiveMetrics(tradesWithMissing);

      expect(metrics.totalTrades).toBe(4);
      expect(metrics.winningTrades).toBe(1);
      expect(metrics.losingTrades).toBe(1);
    });
  });

  describe('calculateBasicMetrics', () => {
    it('should calculate basic metrics correctly', () => {
      const metrics = calculator.calculateBasicMetrics(mockTrades);

      expect(metrics.totalTrades).toBe(10);
      expect(metrics.winRate).toBe(60);
      expect(metrics.avgWin).toBeCloseTo(2.65, 1); // Average of positive trades
      expect(metrics.avgLoss).toBeCloseTo(1.4, 1); // Average of negative trades (absolute)
      expect(metrics.largestWin).toBe(4.2);
      expect(metrics.largestLoss).toBe(-2.1);
    });

    it('should handle all winning trades', () => {
      const allWinningTrades = [
        { pnlPercent: 1.5 },
        { pnlPercent: 2.0 },
        { pnlPercent: 1.2 }
      ];

      const metrics = calculator.calculateBasicMetrics(allWinningTrades);

      expect(metrics.winRate).toBe(100);
      expect(metrics.avgLoss).toBe(0);
      expect(metrics.profitFactor).toBe(0);
    });

    it('should handle all losing trades', () => {
      const allLosingTrades = [
        { pnlPercent: -1.5 },
        { pnlPercent: -2.0 },
        { pnlPercent: -1.2 }
      ];

      const metrics = calculator.calculateBasicMetrics(allLosingTrades);

      expect(metrics.winRate).toBe(0);
      expect(metrics.avgWin).toBe(0);
      expect(metrics.expectancy).toBeLessThan(0);
    });
  });

  describe('calculateRiskMetrics', () => {
    it('should calculate Sharpe ratio correctly', () => {
      const metrics = calculator.calculateRiskMetrics(mockTrades);

      expect(metrics.sharpeRatio).toBeDefined();
      expect(typeof metrics.sharpeRatio).toBe('number');
      expect(metrics.annualizedReturn).toBeDefined();
      expect(metrics.volatility).toBeGreaterThan(0);
    });

    it('should calculate Sortino ratio correctly', () => {
      const metrics = calculator.calculateRiskMetrics(mockTrades);

      expect(metrics.sortinoRatio).toBeDefined();
      expect(typeof metrics.sortinoRatio).toBe('number');
      // Sortino should generally be higher than Sharpe for same data
      expect(metrics.sortinoRatio).toBeGreaterThanOrEqual(metrics.sharpeRatio);
    });

    it('should calculate Calmar ratio correctly', () => {
      const metrics = calculator.calculateRiskMetrics(mockTrades);

      expect(metrics.calmarRatio).toBeDefined();
      expect(typeof metrics.calmarRatio).toBe('number');
    });
  });

  describe('calculateAdvancedMetrics', () => {
    it('should calculate information ratio', () => {
      const metrics = calculator.calculateAdvancedMetrics(mockTrades);

      expect(metrics.informationRatio).toBeDefined();
      expect(typeof metrics.informationRatio).toBe('number');
      expect(metrics.trackingError).toBeGreaterThan(0);
    });

    it('should calculate Jensen alpha', () => {
      const metrics = calculator.calculateAdvancedMetrics(mockTrades);

      expect(metrics.jensenAlpha).toBeDefined();
      expect(typeof metrics.jensenAlpha).toBe('number');
    });

    it('should calculate capture ratios', () => {
      const metrics = calculator.calculateAdvancedMetrics(mockTrades);

      expect(metrics.upCaptureRatio).toBeDefined();
      expect(metrics.downCaptureRatio).toBeDefined();
      expect(metrics.upCaptureRatio).toBeGreaterThanOrEqual(0);
      expect(metrics.downCaptureRatio).toBeGreaterThanOrEqual(0);
    });
  });

  describe('calculateDrawdownMetrics', () => {
    it('should calculate maximum drawdown correctly', () => {
      const metrics = calculator.calculateDrawdownMetrics(mockTrades);

      expect(metrics.maxDrawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.avgDrawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.totalDrawdowns).toBeGreaterThanOrEqual(0);
    });

    it('should calculate recovery factor', () => {
      const metrics = calculator.calculateDrawdownMetrics(mockTrades);

      expect(metrics.recoveryFactor).toBeDefined();
      expect(typeof metrics.recoveryFactor).toBe('number');
    });

    it('should calculate Ulcer Index', () => {
      const metrics = calculator.calculateDrawdownMetrics(mockTrades);

      expect(metrics.ulcerIndex).toBeDefined();
      expect(metrics.ulcerIndex).toBeGreaterThanOrEqual(0);
    });

    it('should handle portfolio with no drawdowns', () => {
      const alwaysWinningTrades = [
        { pnlPercent: 1.0 },
        { pnlPercent: 1.5 },
        { pnlPercent: 2.0 },
        { pnlPercent: 1.2 }
      ];

      const metrics = calculator.calculateDrawdownMetrics(alwaysWinningTrades);

      expect(metrics.maxDrawdown).toBe(0);
      expect(metrics.totalDrawdowns).toBe(0);
    });
  });

  describe('calculateConsistencyMetrics', () => {
    it('should calculate consistency ratio', () => {
      const metrics = calculator.calculateConsistencyMetrics(mockTrades);

      expect(metrics.consistencyRatio).toBe(60); // 6 out of 10 positive trades
    });

    it('should calculate gain-to-pain ratio', () => {
      const metrics = calculator.calculateConsistencyMetrics(mockTrades);

      expect(metrics.gainToPainRatio).toBeGreaterThan(0);
      expect(typeof metrics.gainToPainRatio).toBe('number');
    });

    it('should calculate K-ratio', () => {
      const metrics = calculator.calculateConsistencyMetrics(mockTrades);

      expect(metrics.kRatio).toBeDefined();
      expect(typeof metrics.kRatio).toBe('number');
    });

    it('should calculate stability ratio', () => {
      const metrics = calculator.calculateConsistencyMetrics(mockTrades);

      expect(metrics.stabilityRatio).toBeDefined();
      expect(typeof metrics.stabilityRatio).toBe('number');
    });
  });

  describe('calculateStandardDeviation', () => {
    it('should calculate standard deviation correctly', () => {
      const values = [1, 2, 3, 4, 5];
      const stdDev = calculator.calculateStandardDeviation(values);

      expect(stdDev).toBeCloseTo(1.414, 2); // Known standard deviation for this sequence
    });

    it('should return 0 for empty array', () => {
      const stdDev = calculator.calculateStandardDeviation([]);
      expect(stdDev).toBe(0);
    });

    it('should return 0 for single value', () => {
      const stdDev = calculator.calculateStandardDeviation([5]);
      expect(stdDev).toBe(0);
    });
  });

  describe('calculateMaxDrawdown', () => {
    it('should calculate maximum drawdown from trade sequence', () => {
      const drawdownTrades = [
        { pnlPercent: 5 },   // Portfolio: 105
        { pnlPercent: -3 },  // Portfolio: 101.85
        { pnlPercent: -4 },  // Portfolio: 97.78 (drawdown from 105)
        { pnlPercent: 2 },   // Portfolio: 99.73
        { pnlPercent: 8 }    // Portfolio: 107.71
      ];

      const maxDrawdown = calculator.calculateMaxDrawdown(drawdownTrades);

      expect(maxDrawdown).toBeGreaterThan(0);
      expect(maxDrawdown).toBeLessThan(10); // Should be reasonable
    });

    it('should return 0 for always increasing portfolio', () => {
      const increasingTrades = [
        { pnlPercent: 1 },
        { pnlPercent: 2 },
        { pnlPercent: 1.5 }
      ];

      const maxDrawdown = calculator.calculateMaxDrawdown(increasingTrades);
      expect(maxDrawdown).toBe(0);
    });
  });

  describe('getEmptyMetrics', () => {
    it('should return properly structured empty metrics', () => {
      const emptyMetrics = calculator.getEmptyMetrics();

      expect(emptyMetrics.totalTrades).toBe(0);
      expect(emptyMetrics.winRate).toBe(0);
      expect(emptyMetrics.sharpeRatio).toBe(0);
      expect(emptyMetrics.maxDrawdown).toBe(0);
      expect(emptyMetrics.calculationDate).toBeInstanceOf(Date);
    });
  });

  describe('edge cases', () => {
    it('should handle trades with extreme values', () => {
      const extremeTrades = [
        { pnlPercent: 100 },  // 100% gain
        { pnlPercent: -50 },  // 50% loss
        { pnlPercent: 0 }     // Break-even
      ];

      const metrics = calculator.calculateComprehensiveMetrics(extremeTrades);

      expect(metrics.totalTrades).toBe(3);
      expect(metrics.winRate).toBeCloseTo(33.33, 1);
      expect(metrics.largestWin).toBe(100);
      expect(metrics.largestLoss).toBe(-50);
    });

    it('should handle very small PnL values', () => {
      const smallTrades = [
        { pnlPercent: 0.01 },
        { pnlPercent: -0.005 },
        { pnlPercent: 0.02 }
      ];

      const metrics = calculator.calculateComprehensiveMetrics(smallTrades);

      expect(metrics.totalTrades).toBe(3);
      expect(metrics.totalPnL).toBeCloseTo(0.025, 3);
    });
  });
});
