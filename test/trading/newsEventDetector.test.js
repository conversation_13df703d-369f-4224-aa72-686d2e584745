const NewsEventDetector = require('../../lib/trading/newsEventDetector');

describe('NewsEventDetector', () => {
  let detector;
  const mockConfig = {
    highImpactWindow: 2 * 60 * 60 * 1000, // 2 hours
    mediumImpactWindow: 1 * 60 * 60 * 1000, // 1 hour
  };

  beforeEach(() => {
    detector = new NewsEventDetector(mockConfig);
  });

  describe('Initialization', () => {
    test('should initialize with correct configuration', () => {
      expect(detector.config.highImpactWindow).toBe(2 * 60 * 60 * 1000);
      expect(detector.config.mediumImpactWindow).toBe(1 * 60 * 60 * 1000);
      expect(detector.eventCache).toBeInstanceOf(Map);
    });

    test('should have predefined event categories', () => {
      expect(detector.config.eventCategories).toHaveProperty('fed_announcement');
      expect(detector.config.eventCategories).toHaveProperty('employment_data');
      expect(detector.config.eventCategories).toHaveProperty('crypto_regulation');
    });
  });

  describe('News Event Checking', () => {
    test('should check news events for symbol', async () => {
      const result = await detector.checkNewsEvents('BTCUSDT');

      expect(result).toHaveProperty('highImpact');
      expect(result).toHaveProperty('events');
      expect(result).toHaveProperty('analysis');
      expect(result).toHaveProperty('nextEventTime');
      expect(result).toHaveProperty('recommendedAction');

      expect(typeof result.highImpact).toBe('boolean');
      expect(Array.isArray(result.events)).toBe(true);
    });

    test('should handle errors gracefully', async () => {
      // Mock getCurrentEvents to throw error
      const originalGetCurrentEvents = detector.getCurrentEvents;
      detector.getCurrentEvents = jest.fn().mockRejectedValue(new Error('API Error'));

      const result = await detector.checkNewsEvents('BTCUSDT');

      expect(result.highImpact).toBe(false);
      expect(result.events).toEqual([]);
      expect(result.recommendedAction).toBe('proceed');

      // Restore original method
      detector.getCurrentEvents = originalGetCurrentEvents;
    });
  });

  describe('Event Analysis', () => {
    test('should analyze high impact events correctly', () => {
      const now = new Date();
      const events = [
        {
          id: 'test_high',
          title: 'Fed Rate Decision',
          category: 'fed_announcement',
          impact: 'high',
          time: new Date(now.getTime() + 30 * 60 * 1000), // 30 minutes from now
          symbols: ['BTCUSDT']
        }
      ];

      const analysis = detector.analyzeEvents(events);

      expect(analysis.hasHighImpact).toBe(true);
      expect(analysis.recommendedAction).toBe('pause_trading');
      expect(analysis.riskLevel).toBe('high');
      expect(analysis.affectedEvents).toHaveLength(1);
    });

    test('should analyze medium impact events correctly', () => {
      const now = new Date();
      const events = [
        {
          id: 'test_medium',
          title: 'GDP Release',
          category: 'gdp_release',
          impact: 'medium',
          time: new Date(now.getTime() + 30 * 60 * 1000), // 30 minutes from now
          symbols: ['BTCUSDT']
        }
      ];

      const analysis = detector.analyzeEvents(events);

      expect(analysis.hasMediumImpact).toBe(true);
      expect(analysis.recommendedAction).toBe('reduce_position_size');
      expect(analysis.riskLevel).toBe('medium');
    });

    test('should handle events outside impact window', () => {
      const now = new Date();
      const events = [
        {
          id: 'test_future',
          title: 'Future Event',
          category: 'fed_announcement',
          impact: 'high',
          time: new Date(now.getTime() + 5 * 60 * 60 * 1000), // 5 hours from now
          symbols: ['BTCUSDT']
        }
      ];

      const analysis = detector.analyzeEvents(events);

      expect(analysis.hasHighImpact).toBe(false);
      expect(analysis.recommendedAction).toBe('proceed');
      expect(analysis.nextEventTime).toEqual(events[0].time);
    });

    test('should handle no events', () => {
      const analysis = detector.analyzeEvents([]);

      expect(analysis.hasHighImpact).toBe(false);
      expect(analysis.hasMediumImpact).toBe(false);
      expect(analysis.recommendedAction).toBe('proceed');
      expect(analysis.affectedEvents).toHaveLength(0);
    });
  });

  describe('Fed Announcement Detection', () => {
    test('should detect Fed announcement day correctly', () => {
      // Test first Wednesday of month
      const firstWednesday = new Date(2024, 0, 3); // January 3, 2024 is a Wednesday
      expect(detector.isFedAnnouncementDay(firstWednesday)).toBe(true);

      // Test second Wednesday of month
      const secondWednesday = new Date(2024, 0, 10);
      expect(detector.isFedAnnouncementDay(secondWednesday)).toBe(false);

      // Test non-Wednesday
      const tuesday = new Date(2024, 0, 2);
      expect(detector.isFedAnnouncementDay(tuesday)).toBe(false);
    });

    test('should calculate Fed announcement day correctly', () => {
      const january2024 = new Date(2024, 0, 15); // Mid January 2024
      const fedDay = detector.getFedAnnouncementDay(january2024);

      expect(fedDay).toBeGreaterThan(0);
      expect(fedDay).toBeLessThanOrEqual(7);
    });
  });

  describe('Custom Event Management', () => {
    test('should add custom events', () => {
      const customEvent = {
        title: 'Custom Event',
        category: 'custom',
        impact: 'high',
        time: new Date(),
        symbols: ['BTCUSDT']
      };

      detector.addCustomEvent(customEvent);

      const allEvents = detector.getAllEvents();
      expect(allEvents).toHaveLength(1);
      expect(allEvents[0].title).toBe('Custom Event');
      expect(allEvents[0]).toHaveProperty('id');
    });

    test('should remove events', () => {
      const customEvent = {
        id: 'test_event',
        title: 'Test Event',
        category: 'custom',
        impact: 'medium',
        time: new Date()
      };

      detector.addCustomEvent(customEvent);
      expect(detector.getAllEvents()).toHaveLength(1);

      const removed = detector.removeEvent('test_event');
      expect(removed).toBe(true);
      expect(detector.getAllEvents()).toHaveLength(0);
    });

    test('should handle removing non-existent events', () => {
      const removed = detector.removeEvent('non_existent');
      expect(removed).toBe(false);
    });
  });

  describe('Symbol-Specific Events', () => {
    test('should filter events by symbol', () => {
      detector.addCustomEvent({
        id: 'btc_event',
        title: 'BTC Event',
        symbols: ['BTCUSDT'],
        time: new Date()
      });

      detector.addCustomEvent({
        id: 'eth_event',
        title: 'ETH Event',
        symbols: ['ETHUSDT'],
        time: new Date()
      });

      detector.addCustomEvent({
        id: 'general_event',
        title: 'General Event',
        symbols: [], // Affects all symbols
        time: new Date()
      });

      const btcEvents = detector.getEventsForSymbol('BTCUSDT');
      const ethEvents = detector.getEventsForSymbol('ETHUSDT');

      expect(btcEvents).toHaveLength(2); // BTC event + general event
      expect(ethEvents).toHaveLength(2); // ETH event + general event
    });
  });

  describe('Trading Pause Recommendations', () => {
    test('should recommend pausing for high impact events', () => {
      const now = new Date();
      detector.addCustomEvent({
        id: 'high_impact',
        title: 'High Impact Event',
        impact: 'high',
        time: new Date(now.getTime() + 30 * 60 * 1000), // 30 minutes from now
        symbols: ['BTCUSDT']
      });

      const result = detector.shouldPauseTrading('BTCUSDT');

      expect(result.shouldPause).toBe(true);
      expect(result.reason).toBe('high_impact_news_event');
      expect(result.events).toHaveLength(1);
    });

    test('should not recommend pausing for medium impact events', () => {
      const now = new Date();
      detector.addCustomEvent({
        id: 'medium_impact',
        title: 'Medium Impact Event',
        impact: 'medium',
        time: new Date(now.getTime() + 30 * 60 * 1000),
        symbols: ['BTCUSDT']
      });

      const result = detector.shouldPauseTrading('BTCUSDT');

      expect(result.shouldPause).toBe(false);
      expect(result.reason).toBe(null);
    });
  });

  describe('Risk Assessment', () => {
    test('should provide comprehensive risk assessment', () => {
      const now = new Date();
      detector.addCustomEvent({
        id: 'test_event',
        title: 'Test Event',
        impact: 'medium',
        time: new Date(now.getTime() + 30 * 60 * 1000),
        symbols: ['BTCUSDT']
      });

      const assessment = detector.getNewsRiskAssessment('BTCUSDT');

      expect(assessment).toHaveProperty('riskLevel');
      expect(assessment).toHaveProperty('hasHighImpact');
      expect(assessment).toHaveProperty('hasMediumImpact');
      expect(assessment).toHaveProperty('recommendedAction');
      expect(assessment).toHaveProperty('positionSizeMultiplier');

      expect(assessment.hasMediumImpact).toBe(true);
      expect(assessment.positionSizeMultiplier).toBe(0.5);
    });

    test('should calculate position size multipliers correctly', () => {
      const highImpactAnalysis = { hasHighImpact: true, hasMediumImpact: false };
      const mediumImpactAnalysis = { hasHighImpact: false, hasMediumImpact: true };
      const noImpactAnalysis = { hasHighImpact: false, hasMediumImpact: false };

      expect(detector.calculatePositionSizeMultiplier(highImpactAnalysis)).toBe(0.0);
      expect(detector.calculatePositionSizeMultiplier(mediumImpactAnalysis)).toBe(0.5);
      expect(detector.calculatePositionSizeMultiplier(noImpactAnalysis)).toBe(1.0);
    });
  });

  describe('Cache Management', () => {
    test('should clean up old events', () => {
      const oldTime = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago

      detector.addCustomEvent({
        id: 'old_event',
        title: 'Old Event',
        time: oldTime
      });

      detector.addCustomEvent({
        id: 'recent_event',
        title: 'Recent Event',
        time: new Date()
      });

      expect(detector.getAllEvents()).toHaveLength(2);

      // Force cleanup
      detector.lastCacheCleanup = 0;
      detector.cleanupEventCache();

      const remainingEvents = detector.getAllEvents();
      expect(remainingEvents).toHaveLength(1);
      expect(remainingEvents[0].id).toBe('recent_event');
    });

    test('should not cleanup frequently', () => {
      detector.lastCacheCleanup = Date.now();

      const oldTime = new Date(Date.now() - 25 * 60 * 60 * 1000);
      detector.addCustomEvent({
        id: 'old_event',
        title: 'Old Event',
        time: oldTime
      });

      detector.cleanupEventCache(); // Should not cleanup due to recent lastCacheCleanup

      expect(detector.getAllEvents()).toHaveLength(1); // Event should still be there
    });
  });

  describe('Error Handling', () => {
    test('should handle risk assessment errors', () => {
      // Mock getEventsForSymbol to throw error
      const originalGetEventsForSymbol = detector.getEventsForSymbol;
      detector.getEventsForSymbol = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      const assessment = detector.getNewsRiskAssessment('BTCUSDT');

      expect(assessment.riskLevel).toBe('unknown');
      expect(assessment.hasHighImpact).toBe(false);
      expect(assessment.positionSizeMultiplier).toBe(1.0);

      // Restore original method
      detector.getEventsForSymbol = originalGetEventsForSymbol;
    });

    test('should handle shouldPauseTrading errors', () => {
      // Mock getEventsForSymbol to throw error
      const originalGetEventsForSymbol = detector.getEventsForSymbol;
      detector.getEventsForSymbol = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      const result = detector.shouldPauseTrading('BTCUSDT');

      expect(result.shouldPause).toBe(false);
      expect(result.reason).toBe(null);
      expect(result.events).toEqual([]);

      // Restore original method
      detector.getEventsForSymbol = originalGetEventsForSymbol;
    });
  });
});