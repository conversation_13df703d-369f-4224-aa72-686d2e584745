const { describe, it, expect, beforeEach, vi } = require('vitest');
const VolumeAnalyzer = require('../../lib/trading/volumeAnalyzer');

// Mock logger
vi.mock('../../lib/logger');

describe('VolumeAnalyzer', () => {
  let analyzer;
  let mockCandles;

  beforeEach(() => {
    analyzer = new VolumeAnalyzer();

    // Create mock candle data with varying volumes
    mockCandles = Array.from({ length: 25 }, (_, i) => ({
      openTime: new Date(Date.now() - (25 - i) * 60000),
      open: 100 + Math.random() * 2,
      high: 102 + Math.random() * 2,
      low: 98 + Math.random() * 2,
      close: 100 + Math.random() * 2,
      volume: 1000 + Math.random() * 200 // Base volume around 1000-1200
    }));

    // Set last candle to have high volume for testing
    mockCandles[mockCandles.length - 1].volume = 2000; // 2x average
  });

  describe('analyzeVolumeConfirmation', () => {
    it('should return insufficient data for small datasets', async () => {
      const smallCandles = mockCandles.slice(0, 10);

      const result = await analyzer.analyzeVolumeConfirmation(smallCandles, 'BUY');

      expect(result.isAboveAverage).toBe(false);
      expect(result.strength).toBe('insufficient_data');
      expect(result.reason).toContain('Not enough candle data');
    });

    it('should detect above average volume', async () => {
      const result = await analyzer.analyzeVolumeConfirmation(mockCandles, 'BUY');

      expect(result.isAboveAverage).toBe(true);
      expect(result.volumeRatio).toBeGreaterThan(1.5);
      expect(result.strength).toBe('strong');
      expect(result.currentVolume).toBe(2000);
    });

    it('should detect below average volume', async () => {
      // Set last candle to low volume
      mockCandles[mockCandles.length - 1].volume = 500; // 0.5x average

      const result = await analyzer.analyzeVolumeConfirmation(mockCandles, 'BUY');

      expect(result.isAboveAverage).toBe(false);
      expect(result.volumeRatio).toBeLessThan(1.5);
      expect(result.strength).toBe('weak');
    });

    it('should analyze volume pattern for BUY signals', async () => {
      // Set up bullish price action with volume
      const current = mockCandles[mockCandles.length - 1];
      const previous = mockCandles[mockCandles.length - 2];
      current.close = previous.close * 1.02; // 2% price increase
      current.volume = 2000; // High volume

      const result = await analyzer.analyzeVolumeConfirmation(mockCandles, 'BUY');

      expect(result.pattern.pattern).toBe('bullish_confirmation');
      expect(result.pattern.confidence).toBeGreaterThan(0.7);
    });

    it('should analyze volume pattern for SELL signals', async () => {
      // Set up bearish price action with volume
      const current = mockCandles[mockCandles.length - 1];
      const previous = mockCandles[mockCandles.length - 2];
      current.close = previous.close * 0.98; // 2% price decrease
      current.volume = 2000; // High volume

      const result = await analyzer.analyzeVolumeConfirmation(mockCandles, 'SELL');

      expect(result.pattern.pattern).toBe('bearish_confirmation');
      expect(result.pattern.confidence).toBeGreaterThan(0.7);
    });

    it('should handle errors gracefully', async () => {
      // Pass invalid data to trigger error
      const result = await analyzer.analyzeVolumeConfirmation(null, 'BUY');

      expect(result.isAboveAverage).toBe(false);
      expect(result.strength).toBe('error');
      expect(result.reason).toContain('Error analyzing volume');
    });
  });

  describe('calculateVolumeMetrics', () => {
    it('should calculate correct volume metrics', () => {
      const result = analyzer.calculateVolumeMetrics(mockCandles);

      expect(result.averageVolume).toBeGreaterThan(0);
      expect(result.currentVolume).toBe(2000);
      expect(result.currentRatio).toBeCloseTo(2000 / result.averageVolume, 2);
      expect(result.volumeWindow).toBe(20);
    });

    it('should handle small datasets', () => {
      const smallCandles = mockCandles.slice(0, 5);

      const result = analyzer.calculateVolumeMetrics(smallCandles);

      expect(result.volumeWindow).toBe(4); // length - 1
      expect(result.averageVolume).toBeGreaterThan(0);
    });
  });

  describe('analyzeVolumePattern', () => {
    it('should detect bullish confirmation pattern', () => {
      // Set up bullish pattern
      const current = mockCandles[mockCandles.length - 1];
      const previous = mockCandles[mockCandles.length - 2];
      current.close = previous.close * 1.02; // Price up
      current.volume = previous.volume * 1.5; // Volume up

      const result = analyzer.analyzeVolumePattern(mockCandles, 'BUY');

      expect(result.pattern).toBe('bullish_confirmation');
      expect(result.confidence).toBeGreaterThan(0.7);
    });

    it('should detect bearish divergence pattern', () => {
      // Set up bearish divergence
      const current = mockCandles[mockCandles.length - 1];
      const previous = mockCandles[mockCandles.length - 2];
      current.close = previous.close * 0.98; // Price down
      current.volume = previous.volume * 0.7; // Volume down significantly

      const result = analyzer.analyzeVolumePattern(mockCandles, 'SELL');

      expect(result.pattern).toBe('bearish_divergence');
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should handle insufficient data', () => {
      const smallCandles = mockCandles.slice(0, 2);

      const result = analyzer.analyzeVolumePattern(smallCandles, 'BUY');

      expect(result.pattern).toBe('insufficient_data');
      expect(result.confidence).toBe(0);
    });
  });

  describe('getVolumeStrength', () => {
    it('should classify volume strength correctly', () => {
      expect(analyzer.getVolumeStrength(3.5)).toBe('exceptional');
      expect(analyzer.getVolumeStrength(2.5)).toBe('strong');
      expect(analyzer.getVolumeStrength(1.8)).toBe('moderate');
      expect(analyzer.getVolumeStrength(1.3)).toBe('minimal');
      expect(analyzer.getVolumeStrength(0.8)).toBe('weak');
    });
  });

  describe('checkBreakoutVolumeSupport', () => {
    it('should confirm breakout with high volume', () => {
      // Set increasing volume trend
      for (let i = mockCandles.length - 5; i < mockCandles.length; i++) {
        mockCandles[i].volume = 1000 + (i - (mockCandles.length - 5)) * 200;
      }

      const result = analyzer.checkBreakoutVolumeSupport(mockCandles, 50100, 'BUY');

      expect(result.hasSupport).toBe(true);
      expect(result.volumeRatio).toBeGreaterThan(2.0);
      expect(result.volumeTrend).toBe('increasing');
    });

    it('should reject breakout with low volume', () => {
      // Set low volume
      mockCandles[mockCandles.length - 1].volume = 800;

      const result = analyzer.checkBreakoutVolumeSupport(mockCandles, 50100, 'BUY');

      expect(result.hasSupport).toBe(false);
      expect(result.volumeRatio).toBeLessThan(2.0);
    });

    it('should handle insufficient data', () => {
      const smallCandles = mockCandles.slice(0, 5);

      const result = analyzer.checkBreakoutVolumeSupport(smallCandles, 50100, 'BUY');

      expect(result.hasSupport).toBe(false);
    });
  });

  describe('analyzeVolumeProfile', () => {
    it('should create volume profile', () => {
      const result = analyzer.analyzeVolumeProfile(mockCandles, []);

      expect(result.highVolumeNodes).toBeDefined();
      expect(result.highVolumeNodes.length).toBeGreaterThan(0);
      expect(result.totalLevels).toBeGreaterThan(0);

      // Check structure of high volume nodes
      const firstNode = result.highVolumeNodes[0];
      expect(firstNode).toHaveProperty('price');
      expect(firstNode).toHaveProperty('volume');
      expect(firstNode).toHaveProperty('strength');
      expect(firstNode).toHaveProperty('avgVolume');
    });

    it('should handle insufficient data', () => {
      const smallCandles = mockCandles.slice(0, 10);

      const result = analyzer.analyzeVolumeProfile(smallCandles, []);

      expect(result).toEqual({});
    });

    it('should sort nodes by volume', () => {
      const result = analyzer.analyzeVolumeProfile(mockCandles, []);

      if (result.highVolumeNodes && result.highVolumeNodes.length > 1) {
        for (let i = 1; i < result.highVolumeNodes.length; i++) {
          expect(result.highVolumeNodes[i-1].volume).toBeGreaterThanOrEqual(
            result.highVolumeNodes[i].volume
          );
        }
      }
    });
  });

  describe('calculateVolumeSlope', () => {
    it('should calculate positive slope for increasing volumes', () => {
      const increasingVolumes = [1000, 1100, 1200, 1300, 1400];

      const slope = analyzer.calculateVolumeSlope(increasingVolumes);

      expect(slope).toBeGreaterThan(0);
    });

    it('should calculate negative slope for decreasing volumes', () => {
      const decreasingVolumes = [1400, 1300, 1200, 1100, 1000];

      const slope = analyzer.calculateVolumeSlope(decreasingVolumes);

      expect(slope).toBeLessThan(0);
    });

    it('should handle insufficient data', () => {
      const slope = analyzer.calculateVolumeSlope([1000]);

      expect(slope).toBe(0);
    });
  });

  describe('getPatternAnalysis', () => {
    it('should return correct analysis for each pattern', () => {
      expect(analyzer.getPatternAnalysis('bullish_confirmation')).toContain('Strong bullish');
      expect(analyzer.getPatternAnalysis('bearish_confirmation')).toContain('Strong bearish');
      expect(analyzer.getPatternAnalysis('bullish_divergence')).toContain('Weak bullish');
      expect(analyzer.getPatternAnalysis('bearish_divergence')).toContain('Weak bearish');
      expect(analyzer.getPatternAnalysis('accumulation')).toContain('accumulation');
      expect(analyzer.getPatternAnalysis('distribution')).toContain('distribution');
      expect(analyzer.getPatternAnalysis('neutral')).toContain('Neutral');
      expect(analyzer.getPatternAnalysis('unknown_pattern')).toBe('Unknown pattern');
    });
  });
});