const { expect } = require('chai');
const sinon = require('sinon');
const AIClient = require('../../lib/ai/aiClient');
const AIPerformanceAnalyzer = require('../../lib/ai/aiPerformanceAnalyzer');
const AIResponseParser = require('../../lib/ai/aiResponseParser');
const AIFallbackManager = require('../../lib/ai/aiFallbackManager');
const PerformanceAnalytics = require('../../lib/models/performanceAnalytics');
const TradingSignal = require('../../lib/models/tradingSignal');

describe('AI Integration Tests', function() {
  this.timeout(10000); // Increase timeout for AI calls

  let aiClient;
  let performanceAnalyzer;
  let responseParser;
  let fallbackManager;
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Initialize components with test configuration
    aiClient = new AIClient({
      timeout: 5000,
      maxRetries: 2
    });

    performanceAnalyzer = new AIPerformanceAnalyzer({
      aiClient: {
        timeout: 5000,
        maxRetries: 2
      },
      autoImplement: false
    });

    responseParser = new AIResponseParser({
      strictMode: false
    });

    fallbackManager = new AIFallbackManager({
      fallbackEnabled: true,
      cacheEnabled: true,
      maxConsecutiveFailures: 3
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('AIClient', () => {
    describe('sendRequest', () => {
      it('should successfully send request to AI service', async () => {
        // Mock successful axios response
        const mockResponse = {
          data: {
            choices: [{
              message: {
                content: JSON.stringify({
                  insights: [],
                  recommendations: [],
                  confidenceScore: 75
                })
              }
            }],
            usage: {
              prompt_tokens: 100,
              completion_tokens: 200,
              total_tokens: 300
            }
          }
        };

        const axiosStub = sandbox.stub(require('axios'), 'post').resolves(mockResponse);

        const result = await aiClient.sendRequest('test prompt');

        expect(result.success).to.be.true;
        expect(result.content).to.be.a('string');
        expect(result.usage).to.deep.equal(mockResponse.data.usage);
        expect(axiosStub.calledOnce).to.be.true;
      });

      it('should handle AI service errors gracefully', async () => {
        const axiosStub = sandbox.stub(require('axios'), 'post').rejects(new Error('Network error'));

        const result = await aiClient.sendRequest('test prompt');

        expect(result.success).to.be.false;
        expect(result.error).to.equal('Network error');
        expect(axiosStub.calledTwice).to.be.true; // Should retry once
      });

      it('should retry on temporary failures', async () => {
        const axiosStub = sandbox.stub(require('axios'), 'post');
        axiosStub.onFirstCall().rejects(new Error('Temporary error'));
        axiosStub.onSecondCall().resolves({
          data: {
            choices: [{
              message: { content: '{"test": "success"}' }
            }],
            usage: { total_tokens: 100 }
          }
        });

        const result = await aiClient.sendRequest('test prompt');

        expect(result.success).to.be.true;
        expect(axiosStub.calledTwice).to.be.true;
      });

      it('should not retry on authentication errors', async () => {
        const error = new Error('Unauthorized');
        error.response = { status: 401, statusText: 'Unauthorized' };

        const axiosStub = sandbox.stub(require('axios'), 'post').rejects(error);

        const result = await aiClient.sendRequest('test prompt');

        expect(result.success).to.be.false;
        expect(axiosStub.calledOnce).to.be.true; // Should not retry
      });
    });

    describe('analyzePerformance', () => {
      it('should analyze performance data and return structured results', async () => {
        const mockAIResponse = {
          insights: [{
            category: 'risk_management',
            finding: 'High drawdown detected',
            impact: 'high',
            confidence: 85
          }],
          recommendations: [{
            type: 'parameter_change',
            description: 'Reduce position size',
            expectedImprovement: 'Lower drawdown',
            riskLevel: 'low',
            priority: 8
          }],
          parameterSuggestions: {
            riskManagement: {
              stopLossMultiplier: 0.8,
              takeProfitMultiplier: 2.5
            }
          },
          riskAssessment: {
            currentRiskLevel: 'high',
            mainConcerns: ['High drawdown'],
            urgentActions: ['Reduce position sizes']
          },
          confidenceScore: 85
        };

        sandbox.stub(aiClient, 'sendRequest').resolves({
          success: true,
          content: JSON.stringify(mockAIResponse),
          usage: { total_tokens: 500 }
        });

        const performanceData = {
          totalTrades: 100,
          winRate: 45,
          profitFactor: 1.1,
          maxDrawdown: 18
        };

        const result = await aiClient.analyzePerformance(performanceData);

        expect(result.insights).to.be.an('array');
        expect(result.recommendations).to.be.an('array');
        expect(result.confidenceScore).to.equal(85);
        expect(result.metadata).to.exist;
      });

      it('should handle malformed AI responses', async () => {
        sandbox.stub(aiClient, 'sendRequest').resolves({
          success: true,
          content: 'Invalid JSON response',
          usage: { total_tokens: 100 }
        });

        const performanceData = { totalTrades: 50 };

        const result = await aiClient.analyzePerformance(performanceData);

        expect(result.success).to.be.false;
        expect(result.error).to.include('Failed to parse');
      });
    });

    describe('testConnection', () => {
      it('should test AI service connectivity', async () => {
        sandbox.stub(aiClient, 'sendRequest').resolves({
          success: true,
          content: '{"status": "connected"}',
          usage: { total_tokens: 50 }
        });

        const result = await aiClient.testConnection();

        expect(result.success).to.be.true;
        expect(result.connected).to.be.true;
      });
    });
  });

  describe('AIPerformanceAnalyzer', () => {
    describe('analyzePerformance', () => {
      beforeEach(() => {
        // Mock database operations
        sandbox.stub(TradingSignal, 'find').resolves([
          {
            _id: 'signal1',
            symbol: 'BTCUSDT',
            timeframe: '1h',
            pnlPercent: 2.5,
            createdAt: new Date(),
            status: 'completed'
          },
          {
            _id: 'signal2',
            symbol: 'ETHUSDT',
            timeframe: '1h',
            pnlPercent: -1.2,
            createdAt: new Date(),
            status: 'completed'
          }
        ]);

        sandbox.stub(PerformanceAnalytics.prototype, 'save').resolves();
      });

      it('should perform comprehensive performance analysis', async () => {
        // Mock AI client response
        sandbox.stub(performanceAnalyzer.aiClient, 'analyzePerformance').resolves({
          insights: [{
            category: 'entry',
            finding: 'Entry timing could be improved',
            impact: 'medium',
            confidence: 70
          }],
          recommendations: [{
            type: 'strategy_adjustment',
            description: 'Adjust entry criteria',
            expectedImprovement: 'Better timing',
            riskLevel: 'low',
            priority: 6
          }],
          confidenceScore: 75
        });

        const result = await performanceAnalyzer.analyzePerformance('30d');

        expect(result.success).to.be.true;
        expect(result.analysisId).to.exist;
        expect(result.analysis).to.exist;
      });

      it('should handle insufficient trade data', async () => {
        // Mock insufficient trades
        TradingSignal.find.resolves([]);

        const result = await performanceAnalyzer.analyzePerformance('30d');

        expect(result.success).to.be.false;
        expect(result.error).to.include('Insufficient trade data');
      });

      it('should calculate performance metrics correctly', async () => {
        const trades = [
          { pnlPercent: 2.0, createdAt: new Date() },
          { pnlPercent: -1.0, createdAt: new Date() },
          { pnlPercent: 1.5, createdAt: new Date() },
          { pnlPercent: -0.5, createdAt: new Date() }
        ];

        TradingSignal.find.resolves(trades);

        const performanceData = await performanceAnalyzer.gatherPerformanceData('30d');

        expect(performanceData.totalTrades).to.equal(4);
        expect(performanceData.winRate).to.equal(50);
        expect(performanceData.totalPnL).to.equal(2.0);
      });
    });

    describe('gatherPerformanceData', () => {
      it('should gather and calculate performance statistics', async () => {
        const mockTrades = [
          {
            symbol: 'BTCUSDT',
            timeframe: '1h',
            pnlPercent: 2.5,
            createdAt: new Date(),
            status: 'completed'
          },
          {
            symbol: 'BTCUSDT',
            timeframe: '1h',
            pnlPercent: -1.0,
            createdAt: new Date(),
            status: 'completed'
          }
        ];

        sandbox.stub(TradingSignal, 'find').resolves(mockTrades);

        const result = await performanceAnalyzer.gatherPerformanceData('7d');

        expect(result.totalTrades).to.equal(2);
        expect(result.winRate).to.equal(50);
        expect(result.symbolStats).to.be.an('array');
        expect(result.timeframeStats).to.be.an('array');
      });
    });
  });

  describe('AIResponseParser', () => {
    describe('parsePerformanceAnalysis', () => {
      it('should parse valid JSON response', () => {
        const validResponse = JSON.stringify({
          insights: [{
            category: 'risk_management',
            finding: 'Test finding',
            impact: 'high',
            confidence: 80
          }],
          recommendations: [{
            type: 'parameter_change',
            description: 'Test recommendation',
            expectedImprovement: 'Test improvement',
            riskLevel: 'low',
            priority: 5
          }],
          confidenceScore: 75
        });

        const result = responseParser.parsePerformanceAnalysis(validResponse);

        expect(result.success).to.be.true;
        expect(result.data.insights).to.have.length(1);
        expect(result.data.recommendations).to.have.length(1);
        expect(result.data.confidenceScore).to.equal(75);
      });

      it('should handle JSON in code blocks', () => {
        const responseWithCodeBlock = `
Here's the analysis:

\`\`\`json
{
  "insights": [],
  "recommendations": [],
  "confidenceScore": 60
}
\`\`\`

That's the result.
        `;

        const result = responseParser.parsePerformanceAnalysis(responseWithCodeBlock);

        expect(result.success).to.be.true;
        expect(result.data.confidenceScore).to.equal(60);
      });

      it('should sanitize invalid data', () => {
        const invalidResponse = JSON.stringify({
          insights: [{
            category: 'invalid_category',
            finding: 'Test',
            impact: 'invalid_impact',
            confidence: 150 // Invalid confidence
          }],
          recommendations: [],
          confidenceScore: -10 // Invalid confidence
        });

        const result = responseParser.parsePerformanceAnalysis(invalidResponse);

        expect(result.success).to.be.true;
        expect(result.data.insights[0].category).to.equal('risk_management'); // Sanitized
        expect(result.data.insights[0].impact).to.equal('medium'); // Sanitized
        expect(result.data.insights[0].confidence).to.equal(100); // Capped
        expect(result.data.confidenceScore).to.equal(0); // Sanitized
      });

      it('should handle malformed JSON gracefully', () => {
        const malformedResponse = '{ invalid json }';

        const result = responseParser.parsePerformanceAnalysis(malformedResponse);

        expect(result.success).to.be.false;
        expect(result.data).to.exist; // Should have safe defaults
      });
    });

    describe('parseTrailingRecommendation', () => {
      it('should parse valid trailing recommendation', () => {
        const validResponse = JSON.stringify({
          action: 'tighten',
          newDistance: 0.5,
          confidence: 80,
          reason: 'Good profit achieved',
          riskLevel: 'low'
        });

        const result = responseParser.parseTrailingRecommendation(validResponse);

        expect(result.success).to.be.true;
        expect(result.data.action).to.equal('tighten');
        expect(result.data.newDistance).to.equal(0.5);
      });

      it('should provide safe defaults for invalid trailing data', () => {
        const invalidResponse = 'not json';

        const result = responseParser.parseTrailingRecommendation(invalidResponse);

        expect(result.success).to.be.false;
        expect(result.data.action).to.equal('hold'); // Safe default
        expect(result.data.confidence).to.equal(0);
      });
    });
  });

  describe('AIFallbackManager', () => {
    describe('executeWithFallback', () => {
      it('should execute AI function successfully', async () => {
        const mockAIFunction = sandbox.stub().resolves({ result: 'success' });

        const result = await fallbackManager.executeWithFallback(
          mockAIFunction,
          { test: 'params' }
        );

        expect(result.success).to.be.true;
        expect(result.source).to.equal('ai');
        expect(result.data.result).to.equal('success');
      });

      it('should use fallback when AI function fails', async () => {
        const mockAIFunction = sandbox.stub().rejects(new Error('AI service down'));

        const result = await fallbackManager.executeWithFallback(
          mockAIFunction,
          { test: 'params' },
          { analysisType: 'performance' }
        );

        expect(result.success).to.be.true;
        expect(result.source).to.equal('fallback');
        expect(result.warning).to.include('AI service unavailable');
      });

      it('should use cached results when available', async () => {
        const mockAIFunction = sandbox.stub().resolves({ result: 'success' });

        // First call should succeed and cache result
        await fallbackManager.executeWithFallback(mockAIFunction, { test: 'params' });

        // Second call should fail but use cache
        mockAIFunction.rejects(new Error('Service down'));

        const result = await fallbackManager.executeWithFallback(
          mockAIFunction,
          { test: 'params' }
        );

        expect(result.success).to.be.true;
        expect(result.source).to.equal('cache');
      });

      it('should implement circuit breaker pattern', async () => {
        const mockAIFunction = sandbox.stub().rejects(new Error('Service down'));

        // Trigger multiple failures to open circuit breaker
        for (let i = 0; i < 3; i++) {
          await fallbackManager.executeWithFallback(mockAIFunction, { test: i });
        }

        expect(fallbackManager.isServiceHealthy()).to.be.false;

        // Next call should immediately use fallback without trying AI
        const result = await fallbackManager.executeWithFallback(
          mockAIFunction,
          { test: 'circuit_breaker' }
        );

        expect(result.source).to.equal('fallback');
        // AI function should not be called due to circuit breaker
        expect(mockAIFunction.callCount).to.equal(3); // Only the first 3 calls
      });
    });

    describe('generatePerformanceFallback', () => {
      it('should generate rule-based performance analysis', () => {
        const performanceData = {
          winRate: 35, // Low win rate
          profitFactor: 0.8, // Poor profit factor
          maxDrawdown: 20, // High drawdown
          consecutiveLosses: 6 // High consecutive losses
        };

        const result = fallbackManager.generatePerformanceFallback(performanceData);

        expect(result.insights).to.be.an('array');
        expect(result.recommendations).to.be.an('array');
        expect(result.riskAssessment.currentRiskLevel).to.equal('critical');
        expect(result.confidenceScore).to.be.a('number');
      });

      it('should identify good performance correctly', () => {
        const performanceData = {
          winRate: 75, // High win rate
          profitFactor: 2.5, // Good profit factor
          maxDrawdown: 5, // Low drawdown
          consecutiveLosses: 2 // Low consecutive losses
        };

        const result = fallbackManager.generatePerformanceFallback(performanceData);

        expect(result.riskAssessment.currentRiskLevel).to.not.equal('critical');
        expect(result.insights.some(i => i.finding.includes('High win rate'))).to.be.true;
      });
    });

    describe('health management', () => {
      it('should track service health correctly', () => {
        expect(fallbackManager.isServiceHealthy()).to.be.true;

        // Record failures
        fallbackManager.recordFailure(new Error('Test error'));
        fallbackManager.recordFailure(new Error('Test error'));
        fallbackManager.recordFailure(new Error('Test error'));

        expect(fallbackManager.isServiceHealthy()).to.be.false;

        // Record success should restore health
        fallbackManager.recordSuccess();
        expect(fallbackManager.isServiceHealthy()).to.be.true;
      });

      it('should provide health status', () => {
        const status = fallbackManager.getHealthStatus();

        expect(status).to.have.property('isHealthy');
        expect(status).to.have.property('consecutiveFailures');
        expect(status).to.have.property('cacheSize');
        expect(status).to.have.property('fallbackEnabled');
      });
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete AI analysis workflow', async () => {
      // Mock database
      sandbox.stub(TradingSignal, 'find').resolves([
        {
          _id: 'test1',
          symbol: 'BTCUSDT',
          pnlPercent: 2.0,
          createdAt: new Date(),
          status: 'completed'
        }
      ]);

      sandbox.stub(PerformanceAnalytics.prototype, 'save').resolves();

      // Mock AI response
      sandbox.stub(aiClient, 'analyzePerformance').resolves({
        insights: [{ category: 'entry', finding: 'Test', impact: 'medium', confidence: 70 }],
        recommendations: [{ type: 'parameter_change', description: 'Test', expectedImprovement: 'Test', riskLevel: 'low', priority: 5 }],
        confidenceScore: 75
      });

      const result = await performanceAnalyzer.analyzePerformance('30d');

      expect(result.success).to.be.true;
      expect(result.analysisId).to.exist;
    });

    it('should handle AI service failure with graceful fallback', async () => {
      // Mock AI service failure
      sandbox.stub(aiClient, 'analyzePerformance').rejects(new Error('Service unavailable'));

      // Use fallback manager
      const mockPerformanceData = {
        winRate: 45,
        profitFactor: 1.2,
        maxDrawdown: 12
      };

      const result = await fallbackManager.executeWithFallback(
        () => aiClient.analyzePerformance(mockPerformanceData),
        mockPerformanceData,
        { analysisType: 'performance' }
      );

      expect(result.success).to.be.true;
      expect(result.source).to.equal('fallback');
      expect(result.data.insights).to.be.an('array');
    });
  });
});