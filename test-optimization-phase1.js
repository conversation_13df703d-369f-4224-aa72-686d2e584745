/**
 * Test script cho Phase 1 optimization - Advanced Signal Analysis
 * Ki<PERSON><PERSON> tra các cải tiến trong thuật toán phân tích signal
 */

const signalAnalyzer = require('./lib/trading/signalAnalyzer');
const indicators = require('./lib/trading/indicators');
const binanceClient = require('./lib/trading/binanceClient');

// Mock config nếu chưa có
if (!global.config) {
  global.config = {
    trading: {
      indicators: {
        ema: { fast: 50, slow: 200 },
        macd: { fast: 12, slow: 26, signal: 9 },
        rsi: { period: 14, buyZone: [50, 70], sellZone: [30, 50] }
      },
      riskManagement: {
        stopLossPercent: 0.5,
        minRiskReward: 1.2,
        atrMultiplier: { stopLoss: 1.2, takeProfit: 2.0 }
      }
    }
  };
}

// Mock logger
if (!global.logger) {
  global.logger = {
    logInfo: console.log,
    logError: console.error,
    warn: console.warn
  };
}

async function testAdvancedSignalAnalysis() {
  console.log('🚀 Testing Phase 1 Optimization - Advanced Signal Analysis\n');

  try {
    // Test với BTCUSDT
    const symbol = 'BTCUSDT';
    const timeframe = '5m';

    console.log(`📊 Testing with ${symbol} ${timeframe}...`);

    // Lấy dữ liệu thực từ Binance (nếu có)
    let candles;
    try {
      candles = await binanceClient.getKlines(symbol, timeframe, 300);
      console.log(`✅ Fetched ${candles.length} candles from Binance`);
    } catch (error) {
      console.log('⚠️ Cannot fetch real data, using mock data');
      candles = generateMockCandles(300);
    }

    // Test 1: Kiểm tra tính toán indicators mới
    console.log('\n📈 Test 1: Advanced Indicators Calculation');
    const indicatorData = indicators.calculateAllIndicators(candles);

    if (indicatorData) {
      console.log('✅ Indicators calculated successfully:');
      console.log(`  - EMA50: ${indicatorData.ema50?.toFixed(6)}`);
      console.log(`  - EMA200: ${indicatorData.ema200?.toFixed(6)}`);
      console.log(`  - RSI: ${indicatorData.rsi?.toFixed(2)}`);
      console.log(`  - MACD: ${indicatorData.macd?.macd?.toFixed(6)}`);
      console.log(`  - Stochastic: K=${indicatorData.stochastic?.k}, D=${indicatorData.stochastic?.d}`);
      console.log(`  - Engulfing: ${indicatorData.engulfing}`);
      console.log(`  - Strong Body: ${indicatorData.strongBody?.isStrong} (${indicatorData.strongBody?.bodyPercent?.toFixed(1)}%)`);
    } else {
      console.log('❌ Failed to calculate indicators');
      return;
    }

    // Test 2: Kiểm tra market condition analysis
    console.log('\n🌊 Test 2: Market Condition Analysis');
    const marketCondition = indicators.calculateMarketCondition(candles);

    if (marketCondition) {
      console.log('✅ Market condition analyzed:');
      console.log(`  - Condition: ${marketCondition.condition}`);
      console.log(`  - Volatility: ${(marketCondition.volatility * 100).toFixed(3)}%`);
      console.log(`  - Risk Multiplier: ${marketCondition.riskMultiplier}`);
      console.log(`  - Recommended RR: ${marketCondition.recommendedRR}`);
    } else {
      console.log('❌ Failed to analyze market condition');
    }

    // Test 3: Kiểm tra advanced buy conditions
    console.log('\n📈 Test 3: Advanced BUY Conditions Check');
    const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, candles);
    const buySignal = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover, candles);

    console.log('✅ BUY conditions analyzed:');
    console.log(`  - Valid: ${buySignal.isValid}`);
    console.log(`  - Signal Score: ${buySignal.signalScore}/3`);
    console.log(`  - Core Conditions: ${buySignal.details.coreConditions}`);
    console.log(`  - Signal Condition: ${buySignal.details.signalCondition}`);
    console.log(`  - Pattern Condition: ${buySignal.details.patternCondition}`);
    console.log(`  - Momentum Condition: ${buySignal.details.momentumCondition}`);

    if (buySignal.reason) {
      console.log('  - Conditions Summary:');
      console.log(buySignal.reason.split('\n').map(line => `    ${line}`).join('\n'));
    }

    // Test 4: Kiểm tra advanced sell conditions
    console.log('\n📉 Test 4: Advanced SELL Conditions Check');
    const sellSignal = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover, candles);

    console.log('✅ SELL conditions analyzed:');
    console.log(`  - Valid: ${sellSignal.isValid}`);
    console.log(`  - Signal Score: ${sellSignal.signalScore}/3`);
    console.log(`  - Core Conditions: ${sellSignal.details.coreConditions}`);
    console.log(`  - Signal Condition: ${sellSignal.details.signalCondition}`);
    console.log(`  - Pattern Condition: ${sellSignal.details.patternCondition}`);
    console.log(`  - Momentum Condition: ${sellSignal.details.momentumCondition}`);

    // Test 5: Kiểm tra adaptive SL/TP calculation
    console.log('\n💰 Test 5: Adaptive SL/TP Calculation');
    const entry = indicatorData.currentPrice;
    const sltpBuy = indicators.calculateSLTP(entry, 'BUY', candles);
    const sltpSell = indicators.calculateSLTP(entry, 'SELL', candles);

    console.log('✅ Adaptive SL/TP calculated:');
    console.log('  BUY Signal:');
    console.log(`    - Entry: ${entry.toFixed(6)}`);
    console.log(`    - Stop Loss: ${sltpBuy.stopLoss}`);
    console.log(`    - Take Profit: ${sltpBuy.takeProfit}`);
    console.log(`    - Risk/Reward: ${sltpBuy.riskReward}`);
    console.log(`    - TP Method: ${sltpBuy.tpMethod}`);
    console.log(`    - Market Condition: ${sltpBuy.marketCondition}`);
    console.log(`    - Volatility: ${(sltpBuy.volatility * 100).toFixed(3)}%`);

    console.log('  SELL Signal:');
    console.log(`    - Entry: ${entry.toFixed(6)}`);
    console.log(`    - Stop Loss: ${sltpSell.stopLoss}`);
    console.log(`    - Take Profit: ${sltpSell.takeProfit}`);
    console.log(`    - Risk/Reward: ${sltpSell.riskReward}`);
    console.log(`    - TP Method: ${sltpSell.tpMethod}`);

    // Test 6: Kiểm tra volume confirmation
    console.log('\n📊 Test 6: Volume Confirmation');
    const volumeConfirmBuy = signalAnalyzer.checkVolumeConfirmation(candles, 'BUY');
    const volumeConfirmSell = signalAnalyzer.checkVolumeConfirmation(candles, 'SELL');

    console.log('✅ Volume confirmation checked:');
    console.log(`  - BUY Volume Confirmation: ${volumeConfirmBuy}`);
    console.log(`  - SELL Volume Confirmation: ${volumeConfirmSell}`);

    const currentVolume = candles[candles.length - 1].volume;
    const avgVolume = candles.slice(-20).reduce((sum, c) => sum + c.volume, 0) / 20;
    console.log(`  - Current Volume: ${currentVolume.toFixed(0)}`);
    console.log(`  - Average Volume (20): ${avgVolume.toFixed(0)}`);
    console.log(`  - Volume Ratio: ${(currentVolume / avgVolume).toFixed(2)}x`);

    // Test 7: Kiểm tra EMA slope
    console.log('\n📈 Test 7: EMA Slope Analysis');
    const emaSlopeBullish = signalAnalyzer.checkEMASlope(candles, 'bullish');
    const emaSlopeBearish = signalAnalyzer.checkEMASlope(candles, 'bearish');

    console.log('✅ EMA slope analyzed:');
    console.log(`  - Bullish EMA Slope: ${emaSlopeBullish}`);
    console.log(`  - Bearish EMA Slope: ${emaSlopeBearish}`);

    // Test 8: Tạo signal hoàn chỉnh nếu có điều kiện
    console.log('\n🎯 Test 8: Complete Signal Generation');
    const signal = await signalAnalyzer.analyzeSignal(symbol, timeframe, candles);

    if (signal) {
      console.log('✅ Signal generated successfully:');
      console.log(`  - Symbol: ${signal.symbol}`);
      console.log(`  - Type: ${signal.type}`);
      console.log(`  - Entry: ${signal.entry}`);
      console.log(`  - Stop Loss: ${signal.stopLoss}`);
      console.log(`  - Take Profit: ${signal.takeProfit}`);
      console.log(`  - Risk/Reward: ${signal.riskReward}`);
      console.log(`  - TP Method: ${signal.tpMethod}`);
      console.log(`  - Market Condition: ${signal.marketCondition || 'N/A'}`);
    } else {
      console.log('ℹ️ No signal generated (conditions not met)');
    }

    console.log('\n🎉 Phase 1 Optimization Test Completed Successfully!');
    console.log('\n📋 Summary of Improvements:');
    console.log('  ✅ Advanced signal conditions (2/3 signal score required)');
    console.log('  ✅ Volume confirmation added');
    console.log('  ✅ EMA slope analysis for momentum');
    console.log('  ✅ Stochastic oscillator integration');
    console.log('  ✅ Market condition-based adaptive SL/TP');
    console.log('  ✅ Enhanced pattern recognition');
    console.log('  ✅ Volatility-based risk adjustment');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Generate mock candles for testing
function generateMockCandles(count) {
  const candles = [];
  let price = 50000; // Starting price

  for (let i = 0; i < count; i++) {
    const change = (Math.random() - 0.5) * 1000; // Random price change
    const open = price;
    const close = price + change;
    const high = Math.max(open, close) + Math.random() * 200;
    const low = Math.min(open, close) - Math.random() * 200;
    const volume = 1000000 + Math.random() * 5000000;

    candles.push({
      openTime: Date.now() - (count - i) * 5 * 60 * 1000, // 5 minutes apart
      open,
      high,
      low,
      close,
      volume
    });

    price = close;
  }

  return candles;
}

// Run the test
if (require.main === module) {
  testAdvancedSignalAnalysis().catch(console.error);
}

module.exports = { testAdvancedSignalAnalysis };