// Sử dụng global imports như trong index.js
global._ = require('lodash');
global.config = require('./config/default.json');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.logger = Logger(`${__dirname}/logs`);
global.moment = require('moment');

const marketConditionAnalyzer = require('./lib/trading/marketConditionAnalyzer');
const SignalAnalyzer = require('./lib/trading/signalAnalyzer');
const BinanceClient = require('./lib/trading/binanceClient');

async function testMarketConditionFilter() {
    console.log('=== TEST MARKET CONDITION FILTER ===');

    try {
        const binanceClient = BinanceClient;
        const testSymbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOGEUSDT'];

        for (const symbol of testSymbols) {
            console.log(`\n📊 Testing ${symbol}:`);

            // 1. Analyze market condition
            const marketCondition = await marketConditionAnalyzer.analyzeMarketCondition(symbol, '5m');
            console.log(`   Market Condition: ${marketCondition.condition}`);
            console.log(`   Confidence: ${marketCondition.confidence.toFixed(2)}`);
            console.log(`   Risk Level: ${marketCondition.riskLevel}`);
            console.log(`   Recommendations: ${marketCondition.recommendations.join(', ')}`);

            if (marketCondition.metrics) {
                console.log(`   Volatility: ${marketCondition.metrics.volatility?.toFixed(2)}%`);
                console.log(`   Trend: ${marketCondition.metrics.trendStrength?.direction} (${marketCondition.metrics.trendStrength?.strength?.toFixed(2)})`);

                if (marketCondition.metrics.sidewaysInfo?.isSideways) {
                    console.log(`   Sideways: Range ${marketCondition.metrics.sidewaysInfo.priceRange.toFixed(2)}%, S/R tests: ${marketCondition.metrics.sidewaysInfo.supportTests}/${marketCondition.metrics.sidewaysInfo.resistanceTests}`);
                }

                if (marketCondition.metrics.falseBreakouts?.frequency > 0.2) {
                    console.log(`   False Breakouts: ${(marketCondition.metrics.falseBreakouts.frequency * 100).toFixed(1)}% frequency`);
                }
            }

            // 2. Get trading recommendations
            const recommendations = marketConditionAnalyzer.getTradingRecommendations(marketCondition);
            console.log(`   Should Trade: ${recommendations.shouldTrade ? '✅' : '❌'}`);
            console.log(`   Position Size: ${(recommendations.positionSizeMultiplier * 100).toFixed(0)}%`);

            if (recommendations.additionalFilters.length > 0) {
                console.log(`   Additional Filters: ${recommendations.additionalFilters.join(', ')}`);
            }

            if (recommendations.riskAdjustments.length > 0) {
                console.log(`   Risk Adjustments: ${recommendations.riskAdjustments.join(', ')}`);
            }

            // 3. Test signal analysis with market condition
            if (recommendations.shouldTrade) {
                console.log(`\n   🔍 Testing signal analysis...`);

                try {
                    const candles = await binanceClient.getKlines(symbol, '5m', 220);
                    if (candles && candles.length >= 220) {
                        const signal = await SignalAnalyzer.analyzeSignal(symbol, '5m', candles);

                        if (signal) {
                            console.log(`   ✅ Signal generated: ${signal.type} at ${signal.entry}`);
                            console.log(`   Market Condition in Signal: ${signal.marketCondition?.condition || 'none'}`);
                            console.log(`   Risk Level: ${signal.marketCondition?.riskLevel || 'unknown'}`);
                        } else {
                            console.log(`   ❌ No signal generated (filtered by conditions)`);
                        }
                    } else {
                        console.log(`   ❌ Insufficient candle data`);
                    }
                } catch (error) {
                    console.log(`   ❌ Signal analysis error: ${error.message}`);
                }
            } else {
                console.log(`   ⏸️  Trading disabled by market condition filter`);
            }
        }

        // 4. Test specific market conditions
        console.log(`\n🧪 Testing specific market conditions:`);

        // Test sideways detection
        console.log(`\n   Testing sideways market detection:`);
        const sidewaysCandles = generateSidewaysCandles();
        const sidewaysCondition = marketConditionAnalyzer.detectMarketCondition(sidewaysCandles);
        console.log(`   Result: ${sidewaysCondition.condition} (confidence: ${sidewaysCondition.confidence.toFixed(2)})`);

        // Test high volatility detection
        console.log(`\n   Testing high volatility detection:`);
        const volatileCandles = generateVolatileCandles();
        const volatileCondition = marketConditionAnalyzer.detectMarketCondition(volatileCandles);
        console.log(`   Result: ${volatileCondition.condition} (confidence: ${volatileCondition.confidence.toFixed(2)})`);

        // Test trend detection
        console.log(`\n   Testing strong trend detection:`);
        const trendCandles = generateTrendCandles();
        const trendCondition = marketConditionAnalyzer.detectMarketCondition(trendCandles);
        console.log(`   Result: ${trendCondition.condition} (confidence: ${trendCondition.confidence.toFixed(2)})`);

    } catch (error) {
        console.error('Test error:', error);
    }
}

// Helper functions to generate test candle data
function generateSidewaysCandles() {
    const candles = [];
    const basePrice = 100;
    const range = 2; // 2% range

    for (let i = 0; i < 100; i++) {
        const noise = (Math.random() - 0.5) * range;
        const price = basePrice + noise;

        candles.push({
            open: price,
            high: price + Math.random() * 0.5,
            low: price - Math.random() * 0.5,
            close: price + (Math.random() - 0.5) * 0.3,
            volume: 1000 + Math.random() * 500
        });
    }

    return candles;
}

function generateVolatileCandles() {
    const candles = [];
    let price = 100;

    for (let i = 0; i < 100; i++) {
        const volatility = 5 + Math.random() * 5; // 5-10% moves
        const direction = Math.random() > 0.5 ? 1 : -1;
        const move = direction * volatility * (Math.random() * 0.5 + 0.5);

        price = price * (1 + move / 100);

        candles.push({
            open: price,
            high: price * (1 + Math.random() * 0.03),
            low: price * (1 - Math.random() * 0.03),
            close: price * (1 + (Math.random() - 0.5) * 0.02),
            volume: 1000 + Math.random() * 2000
        });
    }

    return candles;
}

function generateTrendCandles() {
    const candles = [];
    let price = 100;
    const trendStrength = 0.5; // 0.5% per candle uptrend

    for (let i = 0; i < 100; i++) {
        price = price * (1 + trendStrength / 100 + (Math.random() - 0.5) * 0.2 / 100);

        candles.push({
            open: price,
            high: price * (1 + Math.random() * 0.01),
            low: price * (1 - Math.random() * 0.005),
            close: price * (1 + (Math.random() - 0.3) * 0.005), // Slight upward bias
            volume: 1000 + Math.random() * 1000
        });
    }

    return candles;
}

testMarketConditionFilter().then(() => {
    console.log('\n=== TEST COMPLETED ===');
}).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});