/**
 * Test script để kiểm tra Telegram conflict notification
 */

// Mock config
global.config = {
  telegram: {
    enabled: true,
    botToken: process.env.TELEGRAM_BOT_TOKEN || 'your_bot_token_here',
    chatId: process.env.TELEGRAM_CHAT_ID || 'your_chat_id_here'
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

global.moment = require('moment');

async function testTelegramConflict() {
  console.log('🧪 Testing Telegram Conflict Notification\\n');
  console.log('========================================\\n');

  try {
    console.log('📊 Telegram Config:');
    console.log('===================');
    console.log('Enabled:', config.telegram.enabled);
    console.log('Bot Token:', config.telegram.botToken ? `${config.telegram.botToken.substring(0, 10)}...` : 'NOT SET');
    console.log('Chat ID:', config.telegram.chatId);

    // Load TelegramBot
    const TelegramBot = require('./lib/trading/telegramBot');

    console.log('\\n🤖 Testing Bot Initialization:');
    console.log('===============================');
    console.log('Bot enabled:', TelegramBot.isEnabled);
    console.log('Bot exists:', !!TelegramBot.bot);

    if (!TelegramBot.isEnabled) {
      console.log('❌ Telegram bot is not enabled');
      return;
    }

    if (!TelegramBot.bot) {
      console.log('❌ Telegram bot is not initialized');
      return;
    }

    // Test bot connection
    console.log('\\n🔗 Testing Bot Connection:');
    console.log('===========================');

    try {
      const botInfo = await TelegramBot.bot.getMe();
      console.log('✅ Bot connection successful');
      console.log('Bot info:', {
        id: botInfo.id,
        username: botInfo.username,
        first_name: botInfo.first_name
      });
    } catch (error) {
      console.log('❌ Bot connection failed:', error.message);
      return;
    }

    // Test conflict notification
    console.log('\\n📱 Testing Conflict Notification:');
    console.log('==================================');

    // Mock signals
    const activeSignal = {
      _id: 'mock_active_id',
      symbol: 'BTCUSDT',
      timeframe: '5m',
      type: 'BUY',
      entry: 43000,
      stopLoss: 42500,
      takeProfit: 43500,
      createdAt: new Date(),
      telegramMessageId: null // No reply message
    };

    const newSignal = {
      symbol: 'BTCUSDT',
      timeframe: '5m',
      type: 'BUY',
      entry: 43200,
      stopLoss: 42700,
      takeProfit: 43700
    };

    console.log('Active signal:', {
      symbol: activeSignal.symbol,
      type: activeSignal.type,
      entry: activeSignal.entry
    });

    console.log('New signal:', {
      symbol: newSignal.symbol,
      type: newSignal.type,
      entry: newSignal.entry
    });

    console.log('\\nSending conflict notification...');

    const messageId = await TelegramBot.sendConflictNotification(
      newSignal,
      activeSignal,
      'same_direction'
    );

    if (messageId) {
      console.log(`✅ Conflict notification sent successfully! Message ID: ${messageId}`);
    } else {
      console.log('❌ Failed to send conflict notification');
    }

    // Test with reply message
    console.log('\\n📱 Testing Conflict Notification with Reply:');
    console.log('=============================================');

    const activeSignalWithMessage = {
      ...activeSignal,
      telegramMessageId: '12345' // Mock message ID for reply
    };

    console.log('Sending conflict notification with reply...');

    const messageId2 = await TelegramBot.sendConflictNotification(
      newSignal,
      activeSignalWithMessage,
      'opposite_direction'
    );

    if (messageId2) {
      console.log(`✅ Conflict notification with reply sent successfully! Message ID: ${messageId2}`);
    } else {
      console.log('❌ Failed to send conflict notification with reply');
    }

    // Test format message
    console.log('\\n📝 Testing Message Formatting:');
    console.log('===============================');

    const formattedMessage = TelegramBot.formatConflictMessage(
      newSignal,
      activeSignal,
      'same_direction'
    );

    console.log('Formatted message:');
    console.log('------------------');
    console.log(formattedMessage);
    console.log('------------------');
    console.log(`Message length: ${formattedMessage.length} characters`);

    console.log('\\n🎉 Test Summary:');
    console.log('================');
    console.log('✅ All Telegram conflict notification tests completed');
    console.log('✅ Check the Telegram chat for actual messages');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testTelegramConflict().catch(console.error);
}

module.exports = { testTelegramConflict };