#!/usr/bin/env node

/**
 * Test logic tín hiệu mới với điều kiện đã cập nhật
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testNewSignalLogic() {
  console.log('🔄 Testing New Signal Logic...\n');

  try {
    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    console.log('📊 New Signal Logic:');
    console.log('='.repeat(60));
    console.log('✅ RSI BUY Zone: 50-70 (was 55-65)');
    console.log('✅ RSI SELL Zone: 30-50 (was 35-45)');
    console.log('✅ Pattern: Body mạnh (70%) HOẶC Engulfing');
    console.log('✅ Logic: Core + (MACD HOẶC RSI) + Pattern');
    console.log('');

    // Test với top volume coins
    const topCoins = await binanceClient.getTop24hVolumeCoins(10);
    console.log(`📊 Testing ${topCoins.length} top volume coins...`);

    let totalSignals = 0;
    const foundSignals = [];

    for (const symbol of topCoins) {
      console.log(`\n🔍 Testing ${symbol} 5m:`);
      console.log('-'.repeat(50));

      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        
        if (klines.length >= 220) {
          const indicatorData = indicators.calculateAllIndicators(klines);
          
          if (indicatorData) {
            console.log(`📊 Indicators:`);
            console.log(`   💰 Entry: ${indicatorData.currentPrice}`);
            console.log(`   📈 EMA50: ${indicatorData.ema50.toFixed(6)}`);
            console.log(`   📈 EMA200: ${indicatorData.ema200.toFixed(6)}`);
            console.log(`   📊 RSI: ${indicatorData.rsi?.toFixed(2) || 'N/A'}`);
            console.log(`   🕯️ Engulfing: ${indicatorData.engulfing}`);
            console.log(`   💪 Strong Body: ${indicatorData.strongBody?.isStrong ? 'Yes' : 'No'} (${indicatorData.strongBody?.bodyPercent?.toFixed(1) || 0}%)`);

            const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);
            console.log(`   🔄 MACD Crossover: ${macdCrossover}`);

            // Test BUY conditions
            const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);
            console.log(`\n📋 BUY Conditions:`);
            console.log(`   ✅ Core (Price>EMA200 & EMA50>EMA200): ${buyConditions.details.coreConditions}`);
            console.log(`   ✅ Signal (MACD HOẶC RSI): ${buyConditions.details.signalCondition}`);
            console.log(`   ✅ Pattern (Body mạnh HOẶC Engulfing): ${buyConditions.details.patternCondition}`);
            console.log(`   🎯 Valid BUY: ${buyConditions.isValid}`);

            // Test SELL conditions
            const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);
            console.log(`\n📋 SELL Conditions:`);
            console.log(`   ✅ Core (Price<EMA200 & EMA50<EMA200): ${sellConditions.details.coreConditions}`);
            console.log(`   ✅ Signal (MACD HOẶC RSI): ${sellConditions.details.signalCondition}`);
            console.log(`   ✅ Pattern (Body mạnh HOẶC Engulfing): ${sellConditions.details.patternCondition}`);
            console.log(`   🎯 Valid SELL: ${sellConditions.isValid}`);

            // Test signal generation
            const signal = await signalAnalyzer.analyzeSignal(symbol, '5m', klines);
            
            if (signal) {
              totalSignals++;
              foundSignals.push({
                symbol,
                type: signal.type,
                entry: signal.entry,
                riskReward: signal.riskReward,
                conditions: signal.type === 'BUY' ? buyConditions : sellConditions
              });
              
              console.log(`\n🚀 SIGNAL GENERATED!`);
              console.log(`   📊 Type: ${signal.type}`);
              console.log(`   💰 Entry: ${signal.entry}`);
              console.log(`   🛑 Stop Loss: ${signal.stopLoss}`);
              console.log(`   🎯 Take Profit: ${signal.takeProfit}`);
              console.log(`   ⚖️ Risk/Reward: 1:${signal.riskReward}`);
              console.log(`   📋 Conditions met:`);
              console.log(`${signal.type === 'BUY' ? buyConditions.reason : sellConditions.reason}`);
            } else {
              console.log(`\n❌ No signal generated`);
            }
          }
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }
    }

    console.log(`\n\n📊 RESULTS SUMMARY:`);
    console.log('='.repeat(60));
    console.log(`Total signals found: ${totalSignals} out of ${topCoins.length} symbols`);
    console.log(`Success rate: ${((totalSignals/topCoins.length)*100).toFixed(1)}%`);

    if (foundSignals.length > 0) {
      console.log(`\n🎉 Found Signals:`);
      foundSignals.forEach((signal, i) => {
        console.log(`${i+1}. ${signal.symbol}: ${signal.type} at ${signal.entry} (RR: 1:${signal.riskReward})`);
        console.log(`   Core: ${signal.conditions.details.coreConditions}, Signal: ${signal.conditions.details.signalCondition}, Pattern: ${signal.conditions.details.patternCondition}`);
      });

      console.log(`\n📈 Logic Analysis:`);
      const buySignals = foundSignals.filter(s => s.type === 'BUY');
      const sellSignals = foundSignals.filter(s => s.type === 'SELL');
      
      console.log(`BUY signals: ${buySignals.length}`);
      console.log(`SELL signals: ${sellSignals.length}`);
      
      // Phân tích pattern usage
      const strongBodySignals = foundSignals.filter(s => 
        s.conditions.details.strongBodyPercent >= 70
      );
      const engulfingSignals = foundSignals.filter(s => 
        s.conditions.conditions.strongBodyOrEngulfing && 
        s.conditions.details.strongBodyPercent < 70
      );
      
      console.log(`Strong Body signals: ${strongBodySignals.length}`);
      console.log(`Engulfing signals: ${engulfingSignals.length}`);
      
    } else {
      console.log(`\n⚠️ No signals found with new logic.`);
    }

    console.log('\n✅ New signal logic test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testNewSignalLogic().catch(console.error);
}

module.exports = testNewSignalLogic;
