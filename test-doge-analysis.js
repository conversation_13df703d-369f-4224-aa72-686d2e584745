#!/usr/bin/env node

/**
 * Test cụ thể cho DOGEUSDT để kiểm tra entry và EMA200
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testDogeAnalysis() {
  console.log('🐕 Testing DOGEUSDT Analysis...\n');

  try {
    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    // Lấy dữ liệu DOGEUSDT thực
    console.log('📊 Fetching DOGEUSDT 1m data...');
    const klines = await binanceClient.getKlines('DOGEUSDT', '1m', 200);
    
    if (!klines || klines.length < 200) {
      throw new Error('Không đủ dữ liệu DOGEUSDT');
    }

    console.log(`✅ Lấy được ${klines.length} nến DOGEUSDT`);
    console.log(`📅 Từ: ${klines[0].openTime}`);
    console.log(`📅 Đến: ${klines[klines.length - 1].openTime}`);
    console.log(`💰 Giá hiện tại: ${klines[klines.length - 1].close}\n`);

    // Tính toán indicators
    console.log('🔢 Calculating indicators...');
    const indicatorData = indicators.calculateAllIndicators(klines);

    if (!indicatorData) {
      throw new Error('Không tính được indicators');
    }

    console.log('📊 DOGEUSDT Indicators:');
    console.log(`💰 Entry (Current Price): ${indicatorData.currentPrice}`);
    console.log(`📈 EMA50: ${indicatorData.ema50}`);
    console.log(`📈 EMA200: ${indicatorData.ema200}`);
    console.log(`📊 MACD: ${indicatorData.macd?.macd || 'N/A'}`);
    console.log(`📊 Signal: ${indicatorData.macd?.signal || 'N/A'}`);
    console.log(`📊 RSI: ${indicatorData.rsi}`);
    console.log(`🕯️ Pattern: ${indicatorData.engulfing}\n`);

    // Kiểm tra điều kiện BUY
    console.log('🔍 Checking BUY conditions...');
    const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);
    const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);

    console.log('📋 BUY Conditions:');
    console.log(`✅ Price > EMA200: ${buyConditions.conditions.priceAboveEMA200} (${indicatorData.currentPrice} > ${indicatorData.ema200})`);
    console.log(`✅ EMA50 > EMA200: ${buyConditions.conditions.emaAlignment} (${indicatorData.ema50} > ${indicatorData.ema200})`);
    console.log(`✅ MACD Crossover: ${buyConditions.conditions.macdCrossover} (${macdCrossover})`);
    console.log(`✅ RSI Zone: ${buyConditions.conditions.rsiZone} (RSI: ${indicatorData.rsi})`);
    console.log(`✅ Engulfing: ${buyConditions.conditions.engulfing} (${indicatorData.engulfing})`);
    console.log(`🎯 Valid Signal: ${buyConditions.isValid}\n`);

    // Kiểm tra điều kiện SELL
    console.log('🔍 Checking SELL conditions...');
    const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);

    console.log('📋 SELL Conditions:');
    console.log(`✅ Price < EMA200: ${sellConditions.conditions.priceBelowEMA200} (${indicatorData.currentPrice} < ${indicatorData.ema200})`);
    console.log(`✅ EMA50 < EMA200: ${sellConditions.conditions.emaAlignment} (${indicatorData.ema50} < ${indicatorData.ema200})`);
    console.log(`✅ MACD Crossover: ${sellConditions.conditions.macdCrossover} (${macdCrossover})`);
    console.log(`✅ RSI Zone: ${sellConditions.conditions.rsiZone} (RSI: ${indicatorData.rsi})`);
    console.log(`✅ Engulfing: ${sellConditions.conditions.engulfing} (${indicatorData.engulfing})`);
    console.log(`🎯 Valid Signal: ${sellConditions.isValid}\n`);

    // Phân tích signal
    console.log('🎯 Analyzing signal...');
    const signal = await signalAnalyzer.analyzeSignal('DOGEUSDT', '1m', klines);

    if (signal) {
      console.log('🚀 SIGNAL GENERATED:');
      console.log(`📊 Symbol: ${signal.symbol}`);
      console.log(`📈 Type: ${signal.type}`);
      console.log(`💰 Entry: ${signal.entry}`);
      console.log(`🛑 Stop Loss: ${signal.stopLoss}`);
      console.log(`🎯 Take Profit: ${signal.takeProfit}`);
      console.log(`⚖️ Risk/Reward: 1:${signal.riskReward}`);
      console.log(`🎯 TP Method: ${signal.tpMethod}`);
      console.log(`💸 Risk: ${signal.riskAmount}`);
      console.log(`💰 Reward: ${signal.rewardAmount}\n`);
    } else {
      console.log('❌ NO SIGNAL: Điều kiện chưa đủ\n');
    }

    // So sánh với dữ liệu từ ảnh
    console.log('🔍 COMPARISON WITH SCREENSHOT:');
    console.log('From screenshot:');
    console.log('- Entry: 0.199550');
    console.log('- EMA50: 0.199371'); 
    console.log('- EMA200: 0.199188');
    console.log('- MACD: -0.000036');
    console.log('- Signal: -0.000053');
    console.log('- RSI: 56.62');
    console.log('');
    console.log('From our calculation:');
    console.log(`- Entry: ${indicatorData.currentPrice}`);
    console.log(`- EMA50: ${indicatorData.ema50}`);
    console.log(`- EMA200: ${indicatorData.ema200}`);
    console.log(`- MACD: ${indicatorData.macd?.macd || 'N/A'}`);
    console.log(`- Signal: ${indicatorData.macd?.signal || 'N/A'}`);
    console.log(`- RSI: ${indicatorData.rsi}`);

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testDogeAnalysis().catch(console.error);
}

module.exports = testDogeAnalysis;
