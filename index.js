const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');

// Handle routes
const AuthRoutes = require('./lib/routes/auth');
const UserRoutes = require('./lib/routes/user');

// Trading modules
const marketDataService = require('./lib/services/marketDataService');
const signalService = require('./lib/services/signalService');
const statisticsService = require('./lib/services/statisticsService');
const reportScheduler = require('./lib/services/reportScheduler');
const zombieCleanupService = require('./lib/services/zombieCleanupService');
const orderManager = require('./lib/trading/orderManager');
const scheduler = require('./lib/trading/scheduler');
const telegramBot = require('./lib/trading/telegramBot');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(express.static('public'));

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// API Routes - Example routes for the template
declareRoute('post', '/auth/login', [], AuthRoutes.login);
declareRoute('post', '/auth/register', [], AuthRoutes.register);
declareRoute('post', '/user/profile', [tokenToUserMiddleware], UserRoutes.profile);
declareRoute('post', '/user/update', [tokenToUserMiddleware], UserRoutes.update);

// Trading API endpoints
app.get('/api/v1/trading/status', (_req, res) => {
  try {
    const status = {
      marketData: marketDataService.getStatus(),
      signal: signalService.getStatus(),
      orderManager: orderManager.getMonitoringStatus(),
      scheduler: scheduler.getStatus(),
      timestamp: new Date()
    };
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Advanced dashboard endpoint
app.get('/api/v1/trading/dashboard', async (req, res) => {
  try {
    const dashboard = await generateAdvancedDashboard();
    res.json(dashboard);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/v1/trading/statistics', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const statistics = await signalService.getSignalStatistics(days);
    res.json(statistics);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/v1/trading/signals/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const signals = await signalService.getRecentSignals(limit);
    res.json(signals);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Statistics routes
app.use('/api/statistics', require('./lib/routes/statistics'));

// Zombie cleanup endpoints
app.get('/api/v1/system/zombie-stats', async (req, res) => {
  try {
    const stats = await zombieCleanupService.getZombieStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/v1/system/cleanup-zombies', async (req, res) => {
  try {
    const stats = await zombieCleanupService.cleanupZombieSignals();
    res.json({
      success: true,
      message: `Cleaned up ${stats.totalCleaned} zombie signals`,
      stats
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Test Telegram endpoints
app.post('/api/v1/test/telegram-conflict', async (req, res) => {
  try {
    const telegramBot = require('./lib/trading/telegramBot');
    const messageId = await telegramBot.testConflictNotification();

    if (messageId) {
      res.json({
        success: true,
        message: 'Test conflict notification sent successfully',
        messageId
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to send test conflict notification'
      });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/v1/test/telegram-status', async (req, res) => {
  try {
    const telegramBot = require('./lib/trading/telegramBot');
    const isConnected = await telegramBot.testConnection();

    res.json({
      enabled: telegramBot.isEnabled,
      connected: isConnected,
      botExists: !!telegramBot.bot,
      config: {
        chatId: telegramBot.config?.chatId,
        hasToken: !!telegramBot.config?.botToken
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// Generate advanced dashboard data
async function generateAdvancedDashboard() {
  try {
    const [
      activeSignals,
      todayStats,
      weekStats,
      monthStats,
      systemHealth,
      performanceMetrics
    ] = await Promise.all([
      orderManager.getMonitoringStatus(),
      statisticsService.getStatisticsByPeriod('today'),
      statisticsService.getStatisticsByPeriod('week'),
      statisticsService.getStatisticsByPeriod('month'),
      getSystemHealth(),
      statisticsService.getAdvancedMetrics()
    ]);

    return {
      timestamp: new Date(),
      activeSignals: {
        count: activeSignals.activeSignalsCount,
        signals: activeSignals.activeSignals,
        monitoring: activeSignals.isActive
      },
      performance: {
        today: todayStats || { totalTrades: 0, winRate: 0, totalPnL: 0 },
        week: weekStats || { totalTrades: 0, winRate: 0, totalPnL: 0 },
        month: monthStats || { totalTrades: 0, winRate: 0, totalPnL: 0 }
      },
      metrics: performanceMetrics || {
        sharpeRatio: 0,
        maxDrawdown: 0,
        profitFactor: 0,
        expectancy: 0
      },
      systemHealth,
      alerts: await getActiveAlerts()
    };
  } catch (error) {
    logger.logError('Error generating dashboard:', error.message);
    throw error;
  }
}

// Get system health status
async function getSystemHealth() {
  try {
    const health = {
      database: 'unknown',
      binanceAPI: 'unknown',
      telegram: 'unknown',
      monitoring: orderManager.getMonitoringStatus().isActive ? 'online' : 'offline'
    };

    // Check database
    try {
      await mongoose.connection.db.admin().ping();
      health.database = 'online';
    } catch (error) {
      health.database = 'offline';
    }

    // Check Binance API
    try {
      const binanceClient = require('./lib/trading/binanceClient');
      await binanceClient.testConnectivity();
      health.binanceAPI = 'online';
    } catch (error) {
      health.binanceAPI = 'offline';
    }

    // Check Telegram
    try {
      const telegramBot = require('./lib/trading/telegramBot');
      const telegramStatus = await telegramBot.testConnection();
      health.telegram = telegramStatus ? 'online' : 'offline';
    } catch (error) {
      health.telegram = 'offline';
    }

    return health;
  } catch (error) {
    logger.logError('Error checking system health:', error.message);
    return {
      database: 'error',
      binanceAPI: 'error',
      telegram: 'error',
      monitoring: 'error'
    };
  }
}

// Get active alerts
async function getActiveAlerts() {
  try {
    const alerts = [];

    // Check recent performance
    const recentStats = await statisticsService.getStatisticsByPeriod('week');

    if (recentStats) {
      // Win rate alert
      if (recentStats.winRate < 45) {
        alerts.push({
          type: 'critical',
          message: `Win rate dropped to ${recentStats.winRate.toFixed(1)}% (below 45%)`,
          action: 'Consider reducing position sizes or reviewing strategy',
          timestamp: new Date()
        });
      }

      // Drawdown alert (if available)
      if (recentStats.maxDrawdown && recentStats.maxDrawdown > 15) {
        alerts.push({
          type: 'critical',
          message: `Maximum drawdown reached ${recentStats.maxDrawdown.toFixed(1)}%`,
          action: 'Immediate review required - consider stopping trading',
          timestamp: new Date()
        });
      }

      // Low activity alert
      if (recentStats.totalTrades < 5) {
        alerts.push({
          type: 'warning',
          message: `Low trading activity: only ${recentStats.totalTrades} trades this week`,
          action: 'Check signal generation and market conditions',
          timestamp: new Date()
        });
      }
    }

    return alerts;
  } catch (error) {
    logger.logError('Error getting active alerts:', error.message);
    return [];
  }
}

// Initialize trading system
async function initializeTradingSystem() {
  try {
    logger.logInfo('🚀 Initializing ScalpWizard Trading System...');

    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    logger.logInfo('✅ MongoDB connected successfully');

    // 🧹 CLEANUP ZOMBIE SIGNALS FIRST
    logger.logInfo('🧹 Cleaning up zombie signals...');
    const cleanupStats = await zombieCleanupService.cleanupZombieSignals();

    if (cleanupStats.totalCleaned > 0) {
      logger.logInfo(`🗑️ Cleaned up ${cleanupStats.totalCleaned} zombie signals:`, {
        oldActive: cleanupStats.categories.oldActive,
        orphaned: cleanupStats.categories.orphaned,
        corrupted: cleanupStats.categories.corrupted
      });

      // Gửi thông báo cleanup qua Telegram
      try {
        await telegramBot.sendSystemNotification(
          `🧹 System Startup Cleanup\n\n` +
          `Cleaned up ${cleanupStats.totalCleaned} zombie signals:\n` +
          `• Old active: ${cleanupStats.categories.oldActive}\n` +
          `• Orphaned: ${cleanupStats.categories.orphaned}\n` +
          `• Corrupted: ${cleanupStats.categories.corrupted}\n\n` +
          `System is now clean and ready! ✨`
        );
      } catch (telegramError) {
        logger.logError('Error sending cleanup notification:', telegramError.message);
      }
    } else {
      logger.logInfo('✨ No zombie signals found - system is clean!');
    }

    // Khởi tạo các services theo thứ tự
    logger.logInfo('📊 Initializing market data service...');
    await marketDataService.initialize();

    logger.logInfo('📡 Initializing signal service...');
    await signalService.initialize();

    logger.logInfo('👁️ Starting order monitoring...');
    await orderManager.startMonitoring();

    logger.logInfo('⏰ Starting scheduler...');
    await scheduler.start();

    // Khởi tạo report scheduler cho báo cáo hàng ngày
    logger.logInfo('📈 Starting report scheduler...');
    reportScheduler.start();

    logger.logInfo('🎉 ScalpWizard Trading System initialized successfully');

    // Gửi thông báo khởi động thành công
    try {
      await telegramBot.sendSystemNotification(
        `🚀 ScalpWizard Trading System Started\n\n` +
        `✅ All services initialized successfully\n` +
        `📊 System ready for trading\n` +
        `⏰ Started at: ${new Date().toLocaleString()}`
      );
    } catch (telegramError) {
      logger.logError('Error sending startup notification:', telegramError.message);
    }

  } catch (error) {
    logger.logError('❌ Error initializing trading system:', error.message);

    // Gửi thông báo lỗi qua Telegram
    try {
      await telegramBot.sendErrorNotification(error, 'System Initialization');
    } catch (telegramError) {
      logger.logError('Error sending Telegram notification:', telegramError.message);
    }

    process.exit(1);
  }
}

// Graceful shutdown
async function gracefulShutdown() {
  try {
    logger.logInfo('Graceful shutdown initiated...');

    await scheduler.stop();
    reportScheduler.stop();
    orderManager.stopMonitoring();
    await signalService.stop();
    await marketDataService.stop();

    await mongoose.connection.close();

    logger.logInfo('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.logError('Error during graceful shutdown:', error.message);
    process.exit(1);
  }
}

const port = _.get(config, 'port', 3000);
server.listen(port, async () => {
  logger.logInfo('Server listening at port:', port);

  // Khởi tạo trading system sau khi server start
  await initializeTradingSystem();
});

// Process event handlers
process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  logger.logError('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown();
});

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
