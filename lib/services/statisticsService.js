const TradingSignal = require('../models/tradingSignal');
const performanceMetricsCalculator = require('../trading/performanceMetricsCalculator');
const symbolPerformanceEvaluator = require('../trading/symbolPerformanceEvaluator');
const timeframePerformanceAnalyzer = require('../trading/timeframePerformanceAnalyzer');
const seasonalPatternDetector = require('../trading/seasonalPatternDetector');
const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

class StatisticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 phút
  }

  /**
   * Tính win/loss dựa trên PnL thực tế
   */
  calculateWinLoss(signal) {
    // Win nếu PnL > 0, Loss nếu PnL <= 0
    if (signal.pnlPercent !== undefined && signal.pnlPercent !== null) {
      return signal.pnlPercent > 0 ? 'WIN' : 'LOSS';
    }

    // Fallback về logic cũ nếu không có PnL
    return signal.status === 'hit_tp' ? 'WIN' : 'LOSS';
  }

  /**
   * Lấy thống kê tổng quan
   */
  async getOverallStatistics(days = 30) {
    const cacheKey = `overall_${days}`;

    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const signals = await TradingSignal.find({
        createdAt: { $gte: startDate },
        status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
        isConflictNotification: false
      }).sort({ createdAt: -1 });

      const stats = this.calculateStatistics(signals);

      // Cache kết quả
      this.cache.set(cacheKey, {
        data: stats,
        timestamp: Date.now()
      });

      return stats;
    } catch (error) {
      logger.logError('Error getting overall statistics:', error.message);
      return this.getEmptyStats();
    }
  }

  /**
   * Lấy thống kê theo timeframe
   */
  async getTimeframeStatistics(days = 30) {
    const cacheKey = `timeframe_${days}`;

    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
            isConflictNotification: false
          }
        },
        {
          $group: {
            _id: '$timeframe',
            signals: { $push: '$$ROOT' }
          }
        }
      ];

      const results = await TradingSignal.aggregate(pipeline);
      const timeframeStats = {};

      for (const result of results) {
        const timeframe = result._id;
        const signals = result.signals;
        timeframeStats[timeframe] = this.calculateStatistics(signals);
      }

      // Cache kết quả
      this.cache.set(cacheKey, {
        data: timeframeStats,
        timestamp: Date.now()
      });

      return timeframeStats;
    } catch (error) {
      logger.logError('Error getting timeframe statistics:', error.message);
      return {};
    }
  }

  /**
   * Lấy thống kê theo symbol
   */
  async getSymbolStatistics(days = 30, limit = 10) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
            isConflictNotification: false
          }
        },
        {
          $group: {
            _id: '$symbol',
            signals: { $push: '$$ROOT' }
          }
        },
        {
          $limit: limit
        }
      ];

      const results = await TradingSignal.aggregate(pipeline);
      const symbolStats = {};

      for (const result of results) {
        const symbol = result._id;
        const signals = result.signals;
        symbolStats[symbol] = this.calculateStatistics(signals);
      }

      return symbolStats;
    } catch (error) {
      logger.logError('Error getting symbol statistics:', error.message);
      return {};
    }
  }

  /**
   * Tính toán thống kê từ danh sách signals
   */
  calculateStatistics(signals) {
    if (!signals || signals.length === 0) {
      return this.getEmptyStats();
    }

    let winTrades = 0;
    let lossTrades = 0;
    let totalPnL = 0;
    let winPnL = 0;
    let lossPnL = 0;
    let maxWin = 0;
    let maxLoss = 0;

    for (const signal of signals) {
      const result = this.calculateWinLoss(signal);
      const pnl = signal.pnlPercent || 0;

      totalPnL += pnl;

      if (result === 'WIN') {
        winTrades++;
        winPnL += pnl;
        maxWin = Math.max(maxWin, pnl);
      } else {
        lossTrades++;
        lossPnL += pnl;
        maxLoss = Math.min(maxLoss, pnl);
      }
    }

    const totalTrades = winTrades + lossTrades;
    const winRate = totalTrades > 0 ? (winTrades / totalTrades) * 100 : 0;
    const avgPnL = totalTrades > 0 ? totalPnL / totalTrades : 0;
    const avgWin = winTrades > 0 ? winPnL / winTrades : 0;
    const avgLoss = lossTrades > 0 ? lossPnL / lossTrades : 0;

    return {
      totalTrades,
      winTrades,
      lossTrades,
      winRate,
      totalPnL,
      avgPnL,
      avgWin,
      avgLoss,
      maxWin,
      maxLoss,
      profitFactor: Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : 0
    };
  }

  /**
   * Lấy thống kê rỗng
   */
  getEmptyStats() {
    return {
      totalTrades: 0,
      winTrades: 0,
      lossTrades: 0,
      winRate: 0,
      totalPnL: 0,
      avgPnL: 0,
      avgWin: 0,
      avgLoss: 0,
      maxWin: 0,
      maxLoss: 0,
      profitFactor: 0
    };
  }

  /**
   * Tạo báo cáo định kỳ
   */
  async generateDailyReport() {
    try {
      const overallStats = await this.getOverallStatistics(30);
      const timeframeStats = await this.getTimeframeStatistics(30);
      const symbolStats = await this.getSymbolStatistics(7, 5); // Top 5 symbols trong 7 ngày

      return {
        overall: overallStats,
        timeframes: timeframeStats,
        topSymbols: symbolStats,
        generatedAt: new Date()
      };
    } catch (error) {
      logger.logError('Error generating daily report:', error.message);
      return null;
    }
  }

  /**
   * Xóa cache
   */
  clearCache() {
    this.cache.clear();
    logger.logInfo('Statistics cache cleared');
  }

  /**
   * Lấy thống kê theo khoảng thời gian
   */
  async getStatisticsByPeriod(period) {
    try {
      let startDate = new Date();

      switch (period) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setDate(startDate.getDate() - 30);
          break;
        default:
          startDate.setDate(startDate.getDate() - 30);
      }

      const signals = await TradingSignal.find({
        createdAt: { $gte: startDate },
        status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
        isConflictNotification: false
      });

      return this.calculateStatistics(signals);
    } catch (error) {
      logger.logError(`Error getting ${period} statistics:`, error.message);
      return this.getEmptyStats();
    }
  }

  /**
   * Tính toán advanced metrics (Sharpe ratio, max drawdown, etc.)
   */
  async getAdvancedMetrics(days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const signals = await TradingSignal.find({
        createdAt: { $gte: startDate },
        status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
        isConflictNotification: false,
        pnlPercent: { $exists: true }
      }).sort({ createdAt: 1 });

      if (signals.length === 0) {
        return {
          sharpeRatio: 0,
          maxDrawdown: 0,
          profitFactor: 0,
          expectancy: 0,
          avgWin: 0,
          avgLoss: 0,
          maxWinStreak: 0,
          maxLossStreak: 0,
          totalTrades: 0
        };
      }

      const returns = signals.map(s => s.pnlPercent / 100);
      const winningTrades = signals.filter(s => s.pnlPercent > 0);
      const losingTrades = signals.filter(s => s.pnlPercent <= 0);

      // Sharpe Ratio
      const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
      const stdDev = Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length);
      const sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;

      // Maximum Drawdown
      let peak = 0;
      let maxDrawdown = 0;
      let runningPnL = 0;

      for (const signal of signals) {
        runningPnL += signal.pnlPercent;
        if (runningPnL > peak) peak = runningPnL;
        const drawdown = peak > 0 ? (peak - runningPnL) / peak * 100 : 0;
        if (drawdown > maxDrawdown) maxDrawdown = drawdown;
      }

      // Profit Factor
      const grossProfit = winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0);
      const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnlPercent, 0));
      const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : 0;

      // Win/Loss Streaks
      const streaks = this.calculateStreaks(signals);

      // Expectancy
      const expectancy = avgReturn * 100;

      return {
        sharpeRatio: parseFloat(sharpeRatio.toFixed(3)),
        maxDrawdown: parseFloat(maxDrawdown.toFixed(2)),
        profitFactor: parseFloat(profitFactor.toFixed(2)),
        expectancy: parseFloat(expectancy.toFixed(2)),
        avgWin: winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / winningTrades.length : 0,
        avgLoss: losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / losingTrades.length : 0,
        maxWinStreak: streaks.maxWinStreak,
        maxLossStreak: streaks.maxLossStreak,
        totalTrades: signals.length
      };
    } catch (error) {
      logger.logError('Error calculating advanced metrics:', error.message);
      return {
        sharpeRatio: 0,
        maxDrawdown: 0,
        profitFactor: 0,
        expectancy: 0,
        avgWin: 0,
        avgLoss: 0,
        maxWinStreak: 0,
        maxLossStreak: 0,
        totalTrades: 0
      };
    }
  }

  /**
   * Tính toán win/loss streaks
   */
  calculateStreaks(signals) {
    let currentWinStreak = 0;
    let currentLossStreak = 0;
    let maxWinStreak = 0;
    let maxLossStreak = 0;

    for (const signal of signals) {
      const isWin = signal.pnlPercent > 0;

      if (isWin) {
        currentWinStreak++;
        currentLossStreak = 0;
        maxWinStreak = Math.max(maxWinStreak, currentWinStreak);
      } else {
        currentLossStreak++;
        currentWinStreak = 0;
        maxLossStreak = Math.max(maxLossStreak, currentLossStreak);
      }
    }

    return { maxWinStreak, maxLossStreak };
  }

  /**
   * Get enhanced performance analytics
   */
  async getEnhancedAnalytics(days = 30) {
    try {
      const cacheKey = `enhanced_analytics_${days}`;

      // Check cache
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const signals = await TradingSignal.find({
        createdAt: { $gte: startDate },
        status: { $in: ['hit_tp', 'hit_sl', 'early_exit', 'ai_exit'] },
        isConflictNotification: false
      }).sort({ createdAt: 1 });

      // Calculate enhanced metrics
      const enhancedMetrics = await performanceMetricsCalculator.calculateAdvancedMetrics(signals);

      // Symbol performance analysis
      const symbolAnalysis = await symbolPerformanceEvaluator.evaluateSymbolPerformance(signals);

      // Timeframe performance analysis
      const timeframeAnalysis = await timeframePerformanceAnalyzer.analyzeTimeframePerformance(signals);

      // Seasonal patterns
      const seasonalPatterns = await seasonalPatternDetector.detectPatterns(signals);

      const result = {
        period: days,
        totalSignals: signals.length,
        enhancedMetrics,
        symbolAnalysis,
        timeframeAnalysis,
        seasonalPatterns,
        generatedAt: new Date()
      };

      // Cache result
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });

      return result;

    } catch (error) {
      logger.logError('Error getting enhanced analytics:', error.message);
      return null;
    }
  }

  /**
   * Get AI-enhanced performance insights
   */
  async getAIPerformanceInsights(days = 30) {
    try {
      const enhancedAnalytics = await this.getEnhancedAnalytics(days);

      if (!enhancedAnalytics) {
        return null;
      }

      // Generate AI insights based on performance data
      const insights = {
        performanceScore: this.calculateOverallPerformanceScore(enhancedAnalytics),
        strengths: this.identifyStrengths(enhancedAnalytics),
        weaknesses: this.identifyWeaknesses(enhancedAnalytics),
        recommendations: this.generateRecommendations(enhancedAnalytics),
        riskAssessment: this.assessRiskLevel(enhancedAnalytics),
        optimizationOpportunities: this.identifyOptimizationOpportunities(enhancedAnalytics)
      };

      return {
        ...enhancedAnalytics,
        aiInsights: insights
      };

    } catch (error) {
      logger.logError('Error getting AI performance insights:', error.message);
      return null;
    }
  }

  /**
   * Calculate overall performance score
   */
  calculateOverallPerformanceScore(analytics) {
    const metrics = analytics.enhancedMetrics;

    // Weighted scoring system
    const winRateScore = (metrics.winRate / 100) * 25;
    const profitFactorScore = Math.min(metrics.profitFactor / 2, 1) * 25;
    const sharpeScore = Math.min(Math.max(metrics.sharpeRatio / 2, 0), 1) * 25;
    const drawdownScore = Math.max(0, (20 - metrics.maxDrawdown) / 20) * 25;

    return Math.round(winRateScore + profitFactorScore + sharpeScore + drawdownScore);
  }

  /**
   * Identify performance strengths
   */
  identifyStrengths(analytics) {
    const strengths = [];
    const metrics = analytics.enhancedMetrics;

    if (metrics.winRate > 60) {
      strengths.push(`High win rate: ${metrics.winRate.toFixed(1)}%`);
    }

    if (metrics.profitFactor > 1.5) {
      strengths.push(`Strong profit factor: ${metrics.profitFactor.toFixed(2)}`);
    }

    if (metrics.sharpeRatio > 1.0) {
      strengths.push(`Excellent risk-adjusted returns: Sharpe ${metrics.sharpeRatio.toFixed(2)}`);
    }

    if (metrics.maxDrawdown < 10) {
      strengths.push(`Low drawdown: ${metrics.maxDrawdown.toFixed(1)}%`);
    }

    // Symbol-specific strengths
    if (analytics.symbolAnalysis.topPerformers.length > 0) {
      const topSymbol = analytics.symbolAnalysis.topPerformers[0];
      strengths.push(`Strong performance on ${topSymbol.symbol}: ${topSymbol.winRate.toFixed(1)}% win rate`);
    }

    return strengths;
  }

  /**
   * Identify performance weaknesses
   */
  identifyWeaknesses(analytics) {
    const weaknesses = [];
    const metrics = analytics.enhancedMetrics;

    if (metrics.winRate < 45) {
      weaknesses.push(`Low win rate: ${metrics.winRate.toFixed(1)}%`);
    }

    if (metrics.profitFactor < 1.2) {
      weaknesses.push(`Poor profit factor: ${metrics.profitFactor.toFixed(2)}`);
    }

    if (metrics.maxDrawdown > 15) {
      weaknesses.push(`High drawdown: ${metrics.maxDrawdown.toFixed(1)}%`);
    }

    if (metrics.consecutiveLosses > 5) {
      weaknesses.push(`High consecutive losses: ${metrics.consecutiveLosses}`);
    }

    // Symbol-specific weaknesses
    if (analytics.symbolAnalysis.poorPerformers.length > 0) {
      const poorSymbol = analytics.symbolAnalysis.poorPerformers[0];
      weaknesses.push(`Poor performance on ${poorSymbol.symbol}: ${poorSymbol.winRate.toFixed(1)}% win rate`);
    }

    return weaknesses;
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(analytics) {
    const recommendations = [];
    const metrics = analytics.enhancedMetrics;

    if (metrics.winRate < 50) {
      recommendations.push('Consider tightening entry criteria to improve signal quality');
    }

    if (metrics.profitFactor < 1.3) {
      recommendations.push('Review risk-reward ratios and consider adjusting TP/SL levels');
    }

    if (metrics.maxDrawdown > 12) {
      recommendations.push('Implement stricter risk management and position sizing');
    }

    // Timeframe recommendations
    if (analytics.timeframeAnalysis.bestTimeframe) {
      const best = analytics.timeframeAnalysis.bestTimeframe;
      recommendations.push(`Focus more on ${best.timeframe} timeframe (${best.winRate.toFixed(1)}% win rate)`);
    }

    // Seasonal recommendations
    if (analytics.seasonalPatterns.strongPatterns.length > 0) {
      const pattern = analytics.seasonalPatterns.strongPatterns[0];
      recommendations.push(`Leverage ${pattern.type} pattern: ${pattern.description}`);
    }

    return recommendations;
  }

  /**
   * Assess risk level
   */
  assessRiskLevel(analytics) {
    const metrics = analytics.enhancedMetrics;
    let riskScore = 0;

    // Risk factors
    if (metrics.maxDrawdown > 15) riskScore += 3;
    else if (metrics.maxDrawdown > 10) riskScore += 2;
    else if (metrics.maxDrawdown > 5) riskScore += 1;

    if (metrics.consecutiveLosses > 7) riskScore += 3;
    else if (metrics.consecutiveLosses > 5) riskScore += 2;
    else if (metrics.consecutiveLosses > 3) riskScore += 1;

    if (metrics.sharpeRatio < 0.5) riskScore += 2;
    else if (metrics.sharpeRatio < 1.0) riskScore += 1;

    if (riskScore >= 6) return { level: 'HIGH', score: riskScore, description: 'High risk - consider reducing position sizes' };
    if (riskScore >= 3) return { level: 'MEDIUM', score: riskScore, description: 'Medium risk - monitor closely' };
    return { level: 'LOW', score: riskScore, description: 'Low risk - stable performance' };
  }

  /**
   * Identify optimization opportunities
   */
  identifyOptimizationOpportunities(analytics) {
    const opportunities = [];

    // Symbol optimization
    if (analytics.symbolAnalysis.underperformers.length > 0) {
      opportunities.push({
        type: 'symbol_filtering',
        description: 'Remove or reduce allocation to underperforming symbols',
        impact: 'medium',
        symbols: analytics.symbolAnalysis.underperformers.slice(0, 3).map(s => s.symbol)
      });
    }

    // Timeframe optimization
    if (analytics.timeframeAnalysis.performanceGap > 10) {
      opportunities.push({
        type: 'timeframe_focus',
        description: 'Focus on better performing timeframes',
        impact: 'high',
        recommendation: `Prioritize ${analytics.timeframeAnalysis.bestTimeframe.timeframe}`
      });
    }

    // Risk management optimization
    const metrics = analytics.enhancedMetrics;
    if (metrics.avgLoss > metrics.avgWin * 0.7) {
      opportunities.push({
        type: 'risk_reward',
        description: 'Improve risk-reward ratio',
        impact: 'high',
        recommendation: 'Tighten stop losses or extend take profits'
      });
    }

    return opportunities;
  }

  /**
   * Cập nhật PnL cho signal
   */
  async updateSignalPnL(signalId, exitPrice) {
    try {
      const signal = await TradingSignal.findById(signalId);
      if (!signal) {
        return false;
      }

      const pnlPercent = signal.calculatePnL(exitPrice);

      await TradingSignal.findByIdAndUpdate(signalId, {
        exitPrice,
        pnlPercent,
        exitTime: new Date()
      });

      // Xóa cache để force refresh
      this.clearCache();

      logger.logInfo(`Updated PnL for signal ${signalId}: ${pnlPercent.toFixed(2)}%`);
      return true;
    } catch (error) {
      logger.logError('Error updating signal PnL:', error.message);
      return false;
    }
  }
}

module.exports = new StatisticsService();
