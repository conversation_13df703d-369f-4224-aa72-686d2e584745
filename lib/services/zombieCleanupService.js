/**
 * Zombie Cleanup Service
 * Dọn dẹp các signals zombie khi start service
 */

const TradingSignal = require('../models/tradingSignal');

class ZombieCleanupService {
  constructor() {
    this.maxSignalAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    this.cleanupStats = {
      totalCleaned: 0,
      lastCleanup: null,
      categories: {
        oldActive: 0,
        orphaned: 0,
        corrupted: 0
      }
    };
  }

  /**
   * Thực hiện cleanup zombie signals
   */
  async cleanupZombieSignals() {
    try {
      logger.logInfo('🧹 Starting zombie signals cleanup...');

      const startTime = Date.now();
      const cutoffTime = new Date(Date.now() - this.maxSignalAge);

      // Reset stats
      this.cleanupStats.categories = {
        oldActive: 0,
        orphaned: 0,
        corrupted: 0
      };

      // 1. Clean old active signals (> 24h)
      await this.cleanOldActiveSignals(cutoffTime);

      // 2. Clean orphaned signals (no telegram message ID)
      await this.cleanOrphanedSignals(cutoffTime);

      // 3. Clean corrupted signals (missing required fields)
      await this.cleanCorruptedSignals();

      // 4. Clean duplicate signals
      await this.cleanDuplicateSignals();

      const duration = Date.now() - startTime;
      this.cleanupStats.totalCleaned =
        this.cleanupStats.categories.oldActive +
        this.cleanupStats.categories.orphaned +
        this.cleanupStats.categories.corrupted;
      this.cleanupStats.lastCleanup = new Date();

      logger.logInfo(`✅ Zombie cleanup completed in ${duration}ms`);
      logger.logInfo(`📊 Cleanup Stats:`, this.cleanupStats);

      return this.cleanupStats;
    } catch (error) {
      logger.logError('❌ Error during zombie cleanup:', error.message);
      throw error;
    }
  }

  /**
   * Dọn dẹp signals active cũ (> 24h)
   */
  async cleanOldActiveSignals(cutoffTime) {
    try {
      const oldActiveSignals = await TradingSignal.find({
        status: 'active',
        createdAt: { $lt: cutoffTime }
      });

      if (oldActiveSignals.length > 0) {
        logger.logInfo(`🕰️ Found ${oldActiveSignals.length} old active signals (> 24h)`);

        // Update status to cancelled
        const result = await TradingSignal.updateMany(
          {
            status: 'active',
            createdAt: { $lt: cutoffTime }
          },
          {
            $set: {
              status: 'cancelled',
              exitTime: new Date(),
              exitPrice: null,
              pnlPercent: 0
            }
          }
        );

        this.cleanupStats.categories.oldActive = result.modifiedCount;
        logger.logInfo(`✅ Cancelled ${result.modifiedCount} old active signals`);
      }
    } catch (error) {
      logger.logError('Error cleaning old active signals:', error.message);
    }
  }

  /**
   * Dọn dẹp signals orphaned (không có telegram message ID)
   */
  async cleanOrphanedSignals(cutoffTime) {
    try {
      const orphanedSignals = await TradingSignal.find({
        status: 'active',
        createdAt: { $lt: new Date(Date.now() - (60 * 60 * 1000)) }, // > 1h
        $or: [
          { telegramMessageId: { $exists: false } },
          { telegramMessageId: null },
          { telegramMessageId: '' }
        ]
      });

      if (orphanedSignals.length > 0) {
        logger.logInfo(`👻 Found ${orphanedSignals.length} orphaned signals (no Telegram ID)`);

        const result = await TradingSignal.updateMany(
          {
            status: 'active',
            createdAt: { $lt: new Date(Date.now() - (60 * 60 * 1000)) },
            $or: [
              { telegramMessageId: { $exists: false } },
              { telegramMessageId: null },
              { telegramMessageId: '' }
            ]
          },
          {
            $set: {
              status: 'cancelled',
              exitTime: new Date(),
              exitPrice: null,
              pnlPercent: 0
            }
          }
        );

        this.cleanupStats.categories.orphaned = result.modifiedCount;
        logger.logInfo(`✅ Cancelled ${result.modifiedCount} orphaned signals`);
      }
    } catch (error) {
      logger.logError('Error cleaning orphaned signals:', error.message);
    }
  }

  /**
   * Dọn dẹp signals corrupted (thiếu fields bắt buộc)
   */
  async cleanCorruptedSignals() {
    try {
      const corruptedSignals = await TradingSignal.find({
        status: 'active',
        $or: [
          { symbol: { $exists: false } },
          { symbol: null },
          { symbol: '' },
          { entry: { $exists: false } },
          { entry: null },
          { takeProfit: { $exists: false } },
          { takeProfit: null },
          { stopLoss: { $exists: false } },
          { stopLoss: null },
          { type: { $exists: false } },
          { type: null }
        ]
      });

      if (corruptedSignals.length > 0) {
        logger.logInfo(`💀 Found ${corruptedSignals.length} corrupted signals (missing required fields)`);

        const result = await TradingSignal.updateMany(
          {
            status: 'active',
            $or: [
              { symbol: { $exists: false } },
              { symbol: null },
              { symbol: '' },
              { entry: { $exists: false } },
              { entry: null },
              { takeProfit: { $exists: false } },
              { takeProfit: null },
              { stopLoss: { $exists: false } },
              { stopLoss: null },
              { type: { $exists: false } },
              { type: null }
            ]
          },
          {
            $set: {
              status: 'cancelled',
              exitTime: new Date(),
              exitPrice: null,
              pnlPercent: 0
            }
          }
        );

        this.cleanupStats.categories.corrupted = result.modifiedCount;
        logger.logInfo(`✅ Cancelled ${result.modifiedCount} corrupted signals`);
      }
    } catch (error) {
      logger.logError('Error cleaning corrupted signals:', error.message);
    }
  }

  /**
   * Dọn dẹp duplicate signals (cùng symbol, timeframe, type trong 5 phút)
   */
  async cleanDuplicateSignals() {
    try {
      const fiveMinutesAgo = new Date(Date.now() - (5 * 60 * 1000));

      // Tìm duplicates
      const duplicates = await TradingSignal.aggregate([
        {
          $match: {
            status: 'active',
            createdAt: { $gte: fiveMinutesAgo }
          }
        },
        {
          $group: {
            _id: {
              symbol: '$symbol',
              timeframe: '$timeframe',
              type: '$type'
            },
            signals: { $push: '$$ROOT' },
            count: { $sum: 1 }
          }
        },
        {
          $match: {
            count: { $gt: 1 }
          }
        }
      ]);

      let duplicatesRemoved = 0;

      for (const group of duplicates) {
        // Giữ lại signal mới nhất, xóa các signal cũ hơn
        const signals = group.signals.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        const signalsToRemove = signals.slice(1); // Bỏ signal đầu tiên (mới nhất)

        for (const signal of signalsToRemove) {
          await TradingSignal.updateOne(
            { _id: signal._id },
            {
              $set: {
                status: 'cancelled',
                exitTime: new Date(),
                exitPrice: null,
                pnlPercent: 0
              }
            }
          );
          duplicatesRemoved++;
        }

        logger.logInfo(`🔄 Removed ${signalsToRemove.length} duplicate signals for ${group._id.symbol} ${group._id.type}`);
      }

      if (duplicatesRemoved > 0) {
        logger.logInfo(`✅ Removed ${duplicatesRemoved} duplicate signals`);
      }
    } catch (error) {
      logger.logError('Error cleaning duplicate signals:', error.message);
    }
  }

  /**
   * Lấy thống kê zombie signals hiện tại
   */
  async getZombieStats() {
    try {
      const cutoffTime = new Date(Date.now() - this.maxSignalAge);

      const [
        oldActiveCount,
        orphanedCount,
        corruptedCount,
        totalActiveCount
      ] = await Promise.all([
        // Old active signals
        TradingSignal.countDocuments({
          status: 'active',
          createdAt: { $lt: cutoffTime }
        }),

        // Orphaned signals
        TradingSignal.countDocuments({
          status: 'active',
          createdAt: { $lt: new Date(Date.now() - (60 * 60 * 1000)) },
          $or: [
            { telegramMessageId: { $exists: false } },
            { telegramMessageId: null },
            { telegramMessageId: '' }
          ]
        }),

        // Corrupted signals
        TradingSignal.countDocuments({
          status: 'active',
          $or: [
            { symbol: { $exists: false } },
            { symbol: null },
            { symbol: '' },
            { entry: { $exists: false } },
            { entry: null },
            { takeProfit: { $exists: false } },
            { takeProfit: null },
            { stopLoss: { $exists: false } },
            { stopLoss: null }
          ]
        }),

        // Total active signals
        TradingSignal.countDocuments({ status: 'active' })
      ]);

      return {
        totalActive: totalActiveCount,
        zombies: {
          oldActive: oldActiveCount,
          orphaned: orphanedCount,
          corrupted: corruptedCount,
          total: oldActiveCount + orphanedCount + corruptedCount
        },
        lastCleanup: this.cleanupStats.lastCleanup,
        cleanupHistory: this.cleanupStats
      };
    } catch (error) {
      logger.logError('Error getting zombie stats:', error.message);
      return null;
    }
  }

  /**
   * Kiểm tra xem có cần cleanup không
   */
  async needsCleanup() {
    const stats = await this.getZombieStats();
    return stats && stats.zombies.total > 0;
  }
}

module.exports = new ZombieCleanupService();