const cron = require('node-cron');
const statisticsService = require('./statisticsService');
const telegramBot = require('../trading/telegramBot');
const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

class ReportScheduler {
  constructor() {
    this.isRunning = false;
    this.dailyReportJob = null;
  }

  /**
   * Khởi động scheduler
   */
  start() {
    if (this.isRunning) {
      logger.warn('Report scheduler is already running');
      return;
    }

    try {
      // Chạy báo cáo hàng ngày lúc 7:00 sáng
      this.dailyReportJob = cron.schedule('0 7 * * *', async () => {
        await this.sendDailyReport();
      }, {
        scheduled: false,
        timezone: 'Asia/Ho_Chi_Minh'
      });

      this.dailyReportJob.start();
      this.isRunning = true;

      logger.logInfo('Report scheduler started - Daily reports at 7:00 AM');
    } catch (error) {
      logger.logError('Error starting report scheduler:', error.message);
    }
  }

  /**
   * Dừng scheduler
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    try {
      if (this.dailyReportJob) {
        this.dailyReportJob.stop();
        this.dailyReportJob = null;
      }

      this.isRunning = false;
      logger.logInfo('Report scheduler stopped');
    } catch (error) {
      logger.logError('Error stopping report scheduler:', error.message);
    }
  }

  /**
   * Gửi báo cáo hàng ngày
   */
  async sendDailyReport() {
    try {
      logger.logInfo('Generating daily report...');

      const report = await statisticsService.generateDailyReport();
      
      if (!report) {
        logger.warn('Failed to generate daily report');
        return;
      }

      const message = this.formatDailyReport(report);
      
      const success = await telegramBot.sendMessage(
        telegramBot.config.chatId, 
        message, 
        { parse_mode: 'HTML' }
      );

      if (success) {
        logger.logInfo('Daily report sent successfully');
      } else {
        logger.warn('Failed to send daily report');
      }

    } catch (error) {
      logger.logError('Error sending daily report:', error.message);
    }
  }

  /**
   * Format báo cáo hàng ngày
   */
  formatDailyReport(report) {
    const { overall, timeframes, topSymbols } = report;
    
    let message = `📊 <b>BÁO CÁO HÀNG NGÀY</b> 📊

⏰ <b>Thời gian:</b> ${new Date().toLocaleDateString('vi-VN')} 07:00

📈 <b>TỔNG QUAN 30 NGÀY:</b>
📊 <b>Tổng lệnh:</b> ${overall.totalTrades}
✅ <b>Win:</b> ${overall.winTrades}
❌ <b>Loss:</b> ${overall.lossTrades}
📊 <b>Win Rate:</b> ${overall.winRate.toFixed(1)}%

💰 <b>P&L:</b>
📈 <b>Tổng P&L:</b> ${overall.totalPnL > 0 ? '+' : ''}${overall.totalPnL.toFixed(2)}%
📊 <b>P&L TB:</b> ${overall.avgPnL > 0 ? '+' : ''}${overall.avgPnL.toFixed(2)}%
🎯 <b>Win TB:</b> +${overall.avgWin.toFixed(2)}%
🛑 <b>Loss TB:</b> ${overall.avgLoss.toFixed(2)}%
⚡ <b>Profit Factor:</b> ${overall.profitFactor.toFixed(2)}`;

    // Thống kê theo timeframe
    if (Object.keys(timeframes).length > 0) {
      message += `\n\n📊 <b>THEO TIMEFRAME:</b>`;
      
      const sortedTimeframes = Object.entries(timeframes)
        .sort((a, b) => b[1].winRate - a[1].winRate);

      for (const [timeframe, stats] of sortedTimeframes) {
        message += `\n🕐 <b>${timeframe}:</b> ${stats.totalTrades} lệnh, WR: ${stats.winRate.toFixed(1)}%`;
      }
    }

    // Top symbols
    if (Object.keys(topSymbols).length > 0) {
      message += `\n\n🏆 <b>TOP SYMBOLS (7 ngày):</b>`;
      
      const sortedSymbols = Object.entries(topSymbols)
        .sort((a, b) => b[1].totalPnL - a[1].totalPnL)
        .slice(0, 5);

      for (const [symbol, stats] of sortedSymbols) {
        const pnlSign = stats.totalPnL > 0 ? '+' : '';
        message += `\n💰 <b>${symbol}:</b> ${pnlSign}${stats.totalPnL.toFixed(2)}% (${stats.totalTrades} lệnh)`;
      }
    }

    message += `\n\n#ScalpWizard #DailyReport`;

    return message;
  }

  /**
   * Gửi báo cáo thủ công
   */
  async sendManualReport(days = 30) {
    try {
      logger.logInfo(`Generating manual report for ${days} days...`);

      const report = await statisticsService.generateDailyReport();
      
      if (!report) {
        return false;
      }

      const message = this.formatManualReport(report, days);
      
      const success = await telegramBot.sendMessage(
        telegramBot.config.chatId, 
        message, 
        { parse_mode: 'HTML' }
      );

      return success;
    } catch (error) {
      logger.logError('Error sending manual report:', error.message);
      return false;
    }
  }

  /**
   * Format báo cáo thủ công
   */
  formatManualReport(report, days) {
    const { overall, timeframes, topSymbols } = report;
    
    return `📊 <b>BÁO CÁO THỐNG KÊ</b> 📊

⏰ <b>Thời gian:</b> ${new Date().toLocaleString('vi-VN')}
📅 <b>Khoảng thời gian:</b> ${days} ngày

📈 <b>TỔNG QUAN:</b>
📊 <b>Tổng lệnh:</b> ${overall.totalTrades}
✅ <b>Win:</b> ${overall.winTrades} (${overall.winRate.toFixed(1)}%)
❌ <b>Loss:</b> ${overall.lossTrades}

💰 <b>HIỆU SUẤT:</b>
📈 <b>Tổng P&L:</b> ${overall.totalPnL > 0 ? '+' : ''}${overall.totalPnL.toFixed(2)}%
📊 <b>P&L Trung bình:</b> ${overall.avgPnL > 0 ? '+' : ''}${overall.avgPnL.toFixed(2)}%
🎯 <b>Win lớn nhất:</b> +${overall.maxWin.toFixed(2)}%
🛑 <b>Loss lớn nhất:</b> ${overall.maxLoss.toFixed(2)}%
⚡ <b>Profit Factor:</b> ${overall.profitFactor.toFixed(2)}

${this.formatTimeframeDetails(timeframes)}

${this.formatSymbolDetails(topSymbols)}

#ScalpWizard #ManualReport`;
  }

  /**
   * Format chi tiết timeframe
   */
  formatTimeframeDetails(timeframes) {
    if (Object.keys(timeframes).length === 0) {
      return '';
    }

    let details = `📊 <b>CHI TIẾT TIMEFRAME:</b>`;
    
    const sortedTimeframes = Object.entries(timeframes)
      .sort((a, b) => b[1].winRate - a[1].winRate);

    for (const [timeframe, stats] of sortedTimeframes) {
      details += `\n🕐 <b>${timeframe}:</b>`;
      details += `\n   📊 ${stats.totalTrades} lệnh | WR: ${stats.winRate.toFixed(1)}%`;
      details += `\n   💰 P&L: ${stats.totalPnL > 0 ? '+' : ''}${stats.totalPnL.toFixed(2)}%`;
    }

    return details;
  }

  /**
   * Format chi tiết symbol
   */
  formatSymbolDetails(topSymbols) {
    if (Object.keys(topSymbols).length === 0) {
      return '';
    }

    let details = `\n🏆 <b>TOP SYMBOLS:</b>`;
    
    const sortedSymbols = Object.entries(topSymbols)
      .sort((a, b) => b[1].totalPnL - a[1].totalPnL)
      .slice(0, 5);

    for (const [symbol, stats] of sortedSymbols) {
      details += `\n💰 <b>${symbol}:</b>`;
      details += `\n   📊 ${stats.totalTrades} lệnh | WR: ${stats.winRate.toFixed(1)}%`;
      details += `\n   💰 P&L: ${stats.totalPnL > 0 ? '+' : ''}${stats.totalPnL.toFixed(2)}%`;
    }

    return details;
  }

  /**
   * Kiểm tra trạng thái
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      nextRun: this.dailyReportJob ? this.dailyReportJob.nextDate() : null
    };
  }
}

module.exports = new ReportScheduler();
