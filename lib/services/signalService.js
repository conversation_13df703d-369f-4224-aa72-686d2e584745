const signalAnalyzer = require('../trading/signalAnalyzer');
const signalQualityAnalyzer = require('../trading/signalQualityAnalyzer');
const advancedSignalFilter = require('../trading/advancedSignalFilter');
const telegramBot = require('../trading/telegramBot');
const orderManager = require('../trading/orderManager');
const marketDataService = require('./marketDataService');

class SignalService {
  constructor() {
    this.isRunning = false;
    this.analysisQueue = [];
    this.processingQueue = false;
    this.maxQueueSize = 100;
  }

  /**
   * Khởi tạo service
   */
  async initialize() {
    try {
      logger.logInfo('Initializing Signal Service...');

      this.isRunning = true;

      // Bắt đầu xử lý queue
      this.startQueueProcessor();

      logger.logInfo('Signal Service initialized successfully');
    } catch (error) {
      logger.logError('Error initializing Signal Service:', error.message);
      throw error;
    }
  }

  /**
   * Phân tích tín hiệu cho một symbol và timeframe
   */
  async analyzeSymbol(symbol, timeframe) {
    try {
      // Thêm vào queue để xử lý
      this.addToQueue({ symbol, timeframe, timestamp: Date.now() });
    } catch (error) {
      logger.logError(`Error adding ${symbol} ${timeframe} to analysis queue:`, error.message);
    }
  }

  /**
   * Thêm vào queue phân tích
   */
  addToQueue(analysisTask) {
    if (this.analysisQueue.length >= this.maxQueueSize) {
      // Xóa task cũ nhất nếu queue đầy
      this.analysisQueue.shift();
      logger.warn('Analysis queue is full, removing oldest task');
    }

    this.analysisQueue.push(analysisTask);
  }

  /**
   * Bắt đầu xử lý queue
   */
  startQueueProcessor() {
    if (this.processingQueue) {
      return;
    }

    this.processingQueue = true;
    this.processQueue();
  }

  /**
   * Xử lý queue phân tích
   */
  async processQueue() {
    while (this.isRunning && this.processingQueue) {
      try {
        if (this.analysisQueue.length === 0) {
          await this.sleep(1000); // Đợi 1 giây nếu queue trống
          continue;
        }

        const task = this.analysisQueue.shift();
        await this.processAnalysisTask(task);

        // Đợi một chút giữa các task để tránh overload
        await this.sleep(100);

      } catch (error) {
        logger.logError('Error in queue processor:', error.message);
        await this.sleep(1000);
      }
    }
  }

  /**
   * Xử lý một task phân tích
   */
  async processAnalysisTask(task) {
    try {
      const { symbol, timeframe } = task;

      // Lấy dữ liệu nến - ưu tiên database, fallback sang API nếu không đủ
      let candles = await marketDataService.getLatestCandles(symbol, timeframe, 1000);

      // Nếu database không đủ dữ liệu, lấy trực tiếp từ API
      if (candles.length < 500) {
        logger.warn(`Database has only ${candles.length} candles for ${symbol} ${timeframe}. Fetching from API...`);

        const binanceClient = require('../trading/binanceClient');
        const apiCandles = await binanceClient.getKlines(symbol, timeframe, 1000);

        if (apiCandles.length > candles.length) {
          logger.logInfo(`Using API data: ${apiCandles.length} candles vs DB: ${candles.length} candles`);
          candles = apiCandles;

          // Async update database in background (không chờ)
          setImmediate(async () => {
            try {
              await marketDataService.loadHistoricalData(symbol, timeframe);
            } catch (error) {
              logger.logError(`Background DB update failed for ${symbol} ${timeframe}:`, error.message);
            }
          });
        }
      }

      if (candles.length < 220) {
        logger.warn(`Not enough candles for ${symbol} ${timeframe}: ${candles.length} (minimum: 220)`);
        return;
      }

      // Log warning nếu không đủ 300 nến tối ưu
      if (candles.length < 300) {
        logger.warn(`${symbol} ${timeframe}: Using ${candles.length} candles (optimal: 300+). EMA200 accuracy may be reduced.`);
      }

      // Phân tích tín hiệu
      const signal = await signalAnalyzer.analyzeSignal(symbol, timeframe, candles);

      if (signal) {
        await this.handleNewSignal(signal);
      }

    } catch (error) {
      logger.logError(`Error processing analysis task for ${task.symbol} ${task.timeframe}:`, error.message);
    }
  }

  /**
   * Xử lý tín hiệu mới
   */
  async handleNewSignal(signalData) {
    try {
      logger.logInfo(`New signal detected: ${signalData.symbol} ${signalData.timeframe} ${signalData.type}`);

      // 1. Enhanced signal quality analysis
      const qualityAnalysis = await signalQualityAnalyzer.analyzeSignalQuality(signalData);

      if (qualityAnalysis.qualityScore < 60) {
        logger.warn(`Signal quality too low: ${qualityAnalysis.qualityScore}% for ${signalData.symbol}`);
        return;
      }

      // 2. Advanced filtering
      const filterResult = await advancedSignalFilter.filterSignal(signalData);

      if (!filterResult.passed) {
        logger.warn(`Signal filtered out: ${filterResult.reason} for ${signalData.symbol}`);
        return;
      }

      // 3. Enhanced signal data with quality metrics
      const enhancedSignalData = {
        ...signalData,
        qualityScore: qualityAnalysis.qualityScore,
        qualityFactors: qualityAnalysis.factors,
        filterResults: filterResult.results,
        marketCondition: qualityAnalysis.marketCondition,
        confidence: qualityAnalysis.confidence
      };

      logger.logInfo(`Enhanced signal analysis complete: ${signalData.symbol} (Quality: ${qualityAnalysis.qualityScore}%, Confidence: ${qualityAnalysis.confidence}%)`);

      // 4. Kiểm tra conflict với lệnh đang active
      const conflictCheck = await signalAnalyzer.checkActiveSignalConflict(
        enhancedSignalData.symbol,
        enhancedSignalData.timeframe,
        enhancedSignalData.type,
        enhancedSignalData // Truyền enhanced signal data để check entry
      );

      if (conflictCheck.hasConflict) {
        // Có conflict - gửi thông báo conflict thay vì tạo signal mới
        await this.handleSignalConflict(enhancedSignalData, conflictCheck);
        return;
      }

      // Kiểm tra xem có auto-close old signal không
      if (conflictCheck.autoClosedOldSignal) {
        logger.logInfo(`Auto-closed old signal (${conflictCheck.closedReason}) - Processing new signal normally`);

        // Gửi thông báo về việc auto-close old signal
        try {
          const telegramBot = require('../trading/telegramBot');
          await telegramBot.sendSystemNotification(
            `🔄 Auto-closed old signal\n\n` +
            `📊 Symbol: ${enhancedSignalData.symbol}\n` +
            `📈 Reason: ${conflictCheck.closedReason.toUpperCase()}\n` +
            `💰 Price: ${conflictCheck.closedPrice}\n` +
            `🆕 Processing new ${enhancedSignalData.type} signal...`
          );
        } catch (error) {
          logger.logError('Error sending auto-close notification:', error.message);
        }
      }

      // Không có conflict - xử lý enhanced signal bình thường
      const savedSignal = await signalAnalyzer.saveSignal(enhancedSignalData);

      if (!savedSignal) {
        logger.warn(`Enhanced signal not saved (possibly duplicate): ${enhancedSignalData.symbol} ${enhancedSignalData.type}`);
        return;
      }

      // Gửi thông báo Telegram với enhanced information
      const messageId = await telegramBot.sendEnhancedSignalNotification(savedSignal);

      if (messageId) {
        // Cập nhật message ID
        savedSignal.telegramMessageId = messageId.toString();
        await savedSignal.save();
      }

      // Thêm vào order manager để theo dõi với enhanced data
      orderManager.addSignalToMonitoring(savedSignal);

      logger.logInfo(`Enhanced signal processed successfully: ${enhancedSignalData.symbol} ${enhancedSignalData.type} (Quality: ${qualityAnalysis.qualityScore}%)`);

    } catch (error) {
      logger.logError(`Error handling new signal:`, error.message);
    }
  }

  /**
   * Xử lý conflict signal
   */
  async handleSignalConflict(newSignalData, conflictInfo) {
    try {
      const { activeSignal, conflictType, message } = conflictInfo;

      logger.logInfo(`Signal conflict detected: ${message}`);

      // Tạo conflict notification
      const conflictNotification = {
        ...newSignalData,
        isConflictNotification: true,
        parentSignalId: activeSignal._id,
        status: 'cancelled' // Không active vì chỉ là thông báo
      };

      logger.logInfo(`Creating conflict notification for ${newSignalData.symbol}`);
      const savedConflict = await signalAnalyzer.saveSignal(conflictNotification);

      if (savedConflict) {
        logger.logInfo(`Conflict notification saved, sending Telegram notification...`);

        // Gửi thông báo conflict với reply về signal gốc
        const conflictMessageId = await telegramBot.sendConflictNotification(
          savedConflict,
          activeSignal,
          conflictType
        );

        logger.logInfo(`Telegram sendConflictNotification returned: ${conflictMessageId}`);

        if (conflictMessageId) {
          savedConflict.telegramMessageId = conflictMessageId.toString();
          await savedConflict.save();
          logger.logInfo(`✅ Conflict notification message ID saved: ${conflictMessageId}`);
        } else {
          logger.logError(`❌ No message ID returned from sendConflictNotification`);
        }
      } else {
        logger.logError(`❌ Failed to save conflict notification to database`);
      }

      logger.logInfo(`Conflict notification process completed for ${newSignalData.symbol} ${newSignalData.type}`);

    } catch (error) {
      logger.logError(`❌ Error handling signal conflict:`, error.message);
      logger.logError(`Error stack:`, error.stack);
    }
  }

  /**
   * Phân tích tất cả symbols đang track
   */
  async analyzeAllSymbols() {
    try {
      const status = marketDataService.getStatus();

      if (!status.isInitialized) {
        logger.warn('Market Data Service not initialized, skipping analysis');
        return;
      }

      for (const symbol of status.trackedSymbols) {
        for (const timeframe of status.timeframes) {
          await this.analyzeSymbol(symbol, timeframe);
        }
      }

      logger.logInfo(`Queued analysis for ${status.trackedSymbols.length} symbols, ${status.timeframes.length} timeframes`);

    } catch (error) {
      logger.logError('Error analyzing all symbols:', error.message);
    }
  }

  /**
   * Lấy thống kê signals
   */
  async getSignalStatistics(days = 30) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      const stats = await TradingSignal.getStatistics(days);
      return stats.length > 0 ? stats[0] : {
        totalTrades: 0,
        winTrades: 0,
        lossTrades: 0,
        winRate: 0,
        totalPnL: 0,
        avgPnL: 0
      };
    } catch (error) {
      logger.logError('Error getting signal statistics:', error.message);
      return null;
    }
  }

  /**
   * Gửi báo cáo thống kê
   */
  async sendStatisticsReport() {
    try {
      const statistics = await this.getSignalStatistics(30);

      if (statistics && statistics.totalTrades > 0) {
        await telegramBot.sendStatisticsNotification(statistics);
        logger.logInfo('Statistics report sent');
      }

    } catch (error) {
      logger.logError('Error sending statistics report:', error.message);
    }
  }

  /**
   * Lấy signals gần đây
   */
  async getRecentSignals(limit = 10) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      return await TradingSignal.find()
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('symbol timeframe type entry stopLoss takeProfit status createdAt exitTime pnlPercent');
    } catch (error) {
      logger.logError('Error getting recent signals:', error.message);
      return [];
    }
  }

  /**
   * Lấy signals theo symbol
   */
  async getSignalsBySymbol(symbol, limit = 20) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      return await TradingSignal.find({ symbol })
        .sort({ createdAt: -1 })
        .limit(limit);
    } catch (error) {
      logger.logError(`Error getting signals for ${symbol}:`, error.message);
      return [];
    }
  }

  /**
   * Lấy performance theo symbol
   */
  async getSymbolPerformance(days = 30) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const performance = await TradingSignal.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] }
          }
        },
        {
          $group: {
            _id: '$symbol',
            totalTrades: { $sum: 1 },
            winTrades: {
              $sum: { $cond: [{ $eq: ['$status', 'hit_tp'] }, 1, 0] }
            },
            totalPnL: { $sum: '$pnlPercent' },
            avgPnL: { $avg: '$pnlPercent' }
          }
        },
        {
          $project: {
            symbol: '$_id',
            totalTrades: 1,
            winTrades: 1,
            winRate: {
              $multiply: [
                { $divide: ['$winTrades', '$totalTrades'] },
                100
              ]
            },
            totalPnL: { $round: ['$totalPnL', 2] },
            avgPnL: { $round: ['$avgPnL', 2] }
          }
        },
        { $sort: { totalPnL: -1 } }
      ]);

      return performance;
    } catch (error) {
      logger.logError('Error getting symbol performance:', error.message);
      return [];
    }
  }

  /**
   * Lấy trạng thái service
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      processingQueue: this.processingQueue,
      queueSize: this.analysisQueue.length,
      maxQueueSize: this.maxQueueSize
    };
  }

  /**
   * Dừng service
   */
  async stop() {
    try {
      this.isRunning = false;
      this.processingQueue = false;
      this.analysisQueue = [];

      logger.logInfo('Signal Service stopped');
    } catch (error) {
      logger.logError('Error stopping Signal Service:', error.message);
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new SignalService();
