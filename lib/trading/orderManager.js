const binanceClient = require('./binanceClient');
const signalAnalyzer = require('./signalAnalyzer');
const telegramBot = require('./telegramBot');
const trailingManager = require('./trailingManager');
const TradingSignal = require('../models/tradingSignal');
const statisticsService = require('../services/statisticsService');
const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

class OrderManager {
  constructor() {
    this.activeSignals = new Map();
    this.priceMonitorInterval = 2000; // Giảm từ 5s xuống 2s
    this.highVolatilityInterval = 1000; // 1s cho market volatility cao
    this.monitoringActive = false;
    this.marketCondition = 'normal';
  }

  /**
   * Bắt đầu theo dõi các lệnh active
   */
  async startMonitoring() {
    if (this.monitoringActive) {
      return;
    }

    this.monitoringActive = true;
    logger.logInfo('Order monitoring started');

    // Load active signals từ database
    await this.loadActiveSignals();

    // Bắt đầu monitoring loop
    this.monitoringLoop();
  }

  /**
   * Dừng theo dõi
   */
  stopMonitoring() {
    this.monitoringActive = false;
    logger.logInfo('Order monitoring stopped');
  }

  /**
   * Load active signals từ database
   */
  async loadActiveSignals() {
    try {
      const signals = await signalAnalyzer.getActiveSignals();

      for (const signal of signals) {
        this.activeSignals.set(signal._id.toString(), signal);
      }

      logger.logInfo(`Loaded ${signals.length} active signals for monitoring`);
    } catch (error) {
      logger.logError('Error loading active signals:', error.message);
    }
  }

  /**
   * Thêm signal mới vào monitoring
   */
  addSignalToMonitoring(signal) {
    this.activeSignals.set(signal._id.toString(), signal);

    // Thêm vào trailing manager nếu enabled
    if (config.trading.riskManagement.dynamicTP.enabled) {
      trailingManager.addSignalToTrailing(signal);
    }

    logger.logInfo(`Added signal to monitoring: ${signal.symbol} ${signal.type}`);
  }

  /**
   * Xóa signal khỏi monitoring
   */
  removeSignalFromMonitoring(signalId) {
    this.activeSignals.delete(signalId);
    logger.logInfo(`Removed signal from monitoring: ${signalId}`);
  }

  /**
   * Dynamic monitoring interval dựa trên market condition
   */
  getDynamicInterval() {
    switch (this.marketCondition) {
      case 'high_volatility':
        return this.highVolatilityInterval;
      case 'news_event':
        return 500; // 0.5s khi có news
      default:
        return this.priceMonitorInterval;
    }
  }

  /**
   * Loop chính để monitoring với dynamic interval
   */
  async monitoringLoop() {
    while (this.monitoringActive) {
      try {
        await this.checkAllSignals();
        const interval = this.getDynamicInterval();
        await this.sleep(interval);
      } catch (error) {
        logger.logError('Error in monitoring loop:', error.message);
        await this.sleep(this.priceMonitorInterval);
      }
    }
  }

  /**
   * Kiểm tra tất cả signals
   */
  async checkAllSignals() {
    if (this.activeSignals.size === 0) {
      return;
    }

    const signalPromises = Array.from(this.activeSignals.values()).map(signal =>
      this.checkSignal(signal)
    );

    await Promise.allSettled(signalPromises);
  }

  /**
   * Kiểm tra một signal cụ thể
   */
  async checkSignal(signal) {
    try {
      // Lấy OHLC data của nến hiện tại để check chính xác SL/TP
      const candleData = await binanceClient.getCurrentCandle(signal.symbol, signal.timeframe || '1h');

      if (!candleData) {
        logger.logError(`No candle data for ${signal.symbol}`);
        return;
      }

      const currentPrice = candleData.close;
      const signalId = signal._id.toString();

      // Log candle info for debugging (chỉ khi có volatility cao)
      if (parseFloat(candleData.volatility) > 2) {
        logger.logInfo(`High volatility candle for ${signal.symbol}: ${candleData.volatility}, Range: ${candleData.high} - ${candleData.low}`);
      }

      // Kiểm tra early exit conditions trước (vẫn dùng current price)
      const earlyExit = await this.checkEarlyExitConditions(signal, currentPrice);
      if (earlyExit.shouldExit) {
        await this.executeEarlyExit(signal, currentPrice, earlyExit.reasons);
        return;
      }

      // Cập nhật trailing nếu enabled
      if (config.trading.riskManagement.dynamicTP.enabled) {
        await trailingManager.updateTrailing(signalId, currentPrice);

        // Kiểm tra trailing SL/TP (vẫn dùng current price cho trailing)
        const trailingCheck = trailingManager.checkTrailingSLTP(signalId, currentPrice);

        if (trailingCheck.hitSL) {
          // Sử dụng giá SL thực tế thay vì currentPrice để báo cáo chính xác
          const trailingData = trailingManager.getTrailingInfo(signalId);
          const actualSLPrice = trailingData ? trailingData.currentSL : currentPrice;
          await this.executeStopLoss(signal, actualSLPrice, true); // trailing = true
          return;
        }

        if (trailingCheck.hitTP) {
          // Sử dụng giá TP thực tế thay vì currentPrice để báo cáo chính xác
          const trailingData = trailingManager.getTrailingInfo(signalId);
          const actualTPPrice = trailingData ? trailingData.currentTP : currentPrice;
          await this.executeTakeProfit(signal, actualTPPrice, true); // trailing = true
          return;
        }
      } else {
        // ✅ SỬ DỤNG OHLC DATA ĐỂ CHECK SL/TP CHÍNH XÁC
        const slTpCheck = signal.checkSLTPWithCandle(candleData);

        if (slTpCheck.hitSL || slTpCheck.hitTP) {
          // Log thông tin chi tiết về việc hit SL/TP
          logger.logInfo(`${signal.symbol} hit ${slTpCheck.hitType} via candle analysis:`, {
            signal: `${signal.type} at ${signal.entry}`,
            SL: signal.stopLoss,
            TP: signal.takeProfit,
            candle: `O:${candleData.open} H:${candleData.high} L:${candleData.low} C:${candleData.close}`,
            exitPrice: slTpCheck.exitPrice,
            volatility: slTpCheck.candleInfo.volatility
          });

          if (slTpCheck.hitSL) {
            await this.executeStopLoss(signal, slTpCheck.exitPrice);
            return;
          }

          if (slTpCheck.hitTP) {
            await this.executeTakeProfit(signal, slTpCheck.exitPrice);
            return;
          }
        }
      }
    } catch (error) {
      logger.logError(`Error checking signal ${signal._id}:`, error.message);
    }
  }

  /**
   * Thực hiện Stop Loss
   */
  async executeStopLoss(signal, currentPrice, isTrailing = false) {
      try {
        logger.logInfo(`Stop Loss hit for ${signal.symbol} ${signal.type} at ${currentPrice}`);

        // Cập nhật signal trong database
        const updatedSignal = await signalAnalyzer.updateSignalStatus(
          signal._id,
          'hit_sl',
          currentPrice,
          new Date()
        );

        if (updatedSignal) {
          // Cập nhật PnL dựa trên giá thực tế
          await statisticsService.updateSignalPnL(signal._id, currentPrice);

          // Xóa khỏi monitoring và trailing
          this.removeSignalFromMonitoring(signal._id.toString());
          trailingManager.removeSignalFromTrailing(signal._id.toString());

          // Lấy thống kê
          const statistics = await this.getStatistics();

          // Gửi thông báo Telegram với reply về signal gốc
          const resultType = isTrailing ? 'Trailing SL' : 'Stop Loss';
          await telegramBot.sendResultNotification(updatedSignal, statistics, resultType);

          logger.logInfo(`${resultType} executed for ${signal.symbol}: ${signal.type} - Loss: ${updatedSignal.pnlPercent.toFixed(2)}%`);
        }
      } catch (error) {
        logger.logError(`Error executing stop loss for ${signal.symbol}:`, error.message);
      }
    }

  /**
   * Thực hiện Take Profit
   */
  async executeTakeProfit(signal, currentPrice, isTrailing = false) {
      try {
        logger.logInfo(`Take Profit hit for ${signal.symbol} ${signal.type} at ${currentPrice}`);

        // Cập nhật signal trong database
        const updatedSignal = await signalAnalyzer.updateSignalStatus(
          signal._id,
          'hit_tp',
          currentPrice,
          new Date()
        );

        if (updatedSignal) {
          // Cập nhật PnL dựa trên giá thực tế
          await statisticsService.updateSignalPnL(signal._id, currentPrice);

          // Xóa khỏi monitoring và trailing
          this.removeSignalFromMonitoring(signal._id.toString());
          trailingManager.removeSignalFromTrailing(signal._id.toString());

          // Lấy thống kê
          const statistics = await this.getStatistics();

          // Gửi thông báo Telegram với reply về signal gốc
          const resultType = isTrailing ? 'Trailing TP' : 'Take Profit';
          await telegramBot.sendResultNotification(updatedSignal, statistics, resultType);

          logger.logInfo(`${resultType} executed for ${signal.symbol}: ${signal.type} - Profit: ${updatedSignal.pnlPercent.toFixed(2)}%`);
        }
      } catch (error) {
        logger.logError(`Error executing take profit for ${signal.symbol}:`, error.message);
      }
    }

  /**
   * Lấy thống kê trading
   */
  async getStatistics() {
      try {
        // Sử dụng statisticsService để tính toán chính xác
        const stats = await statisticsService.getOverallStatistics(30);
        return stats || {
          totalTrades: 0,
          winTrades: 0,
          lossTrades: 0,
          winRate: 0,
          totalPnL: 0,
          avgPnL: 0
        };
      } catch (error) {
        logger.logError('Error getting statistics:', error.message);
        return {
          totalTrades: 0,
          winTrades: 0,
          lossTrades: 0,
          winRate: 0,
          totalPnL: 0,
          avgPnL: 0
        };
      }
    }

    /**
     * Lấy trạng thái monitoring
     */
    getMonitoringStatus() {
      return {
        isActive: this.monitoringActive,
        activeSignalsCount: this.activeSignals.size,
        activeSignals: Array.from(this.activeSignals.values()).map(signal => ({
          id: signal._id,
          symbol: signal.symbol,
          type: signal.type,
          entry: signal.entry,
          stopLoss: signal.stopLoss,
          takeProfit: signal.takeProfit,
          createdAt: signal.createdAt
        }))
      };
    }

  /**
   * Hủy signal thủ công
   */
  async cancelSignal(signalId, reason = 'Manual cancellation') {
      try {
        const signal = await TradingSignal.findById(signalId);
        if (!signal || signal.status !== 'active') {
          return false;
        }

        // Cập nhật trạng thái
        signal.status = 'cancelled';
        signal.exitTime = new Date();
        await signal.save();

        // Xóa khỏi monitoring
        this.removeSignalFromMonitoring(signalId);

        logger.logInfo(`Signal cancelled: ${signal.symbol} ${signal.type} - ${reason}`);
        return true;
      } catch (error) {
        logger.logError(`Error cancelling signal ${signalId}:`, error.message);
        return false;
      }
    }

  /**
   * Hủy tất cả signals của một symbol
   */
  async cancelSignalsBySymbol(symbol, reason = 'Symbol delisted') {
      try {
        const signals = await TradingSignal.find({
          symbol,
          status: 'active'
        });

        for (const signal of signals) {
          await this.cancelSignal(signal._id.toString(), reason);
        }

        logger.logInfo(`Cancelled ${signals.length} signals for ${symbol}`);
        return signals.length;
      } catch (error) {
        logger.logError(`Error cancelling signals for ${symbol}:`, error.message);
        return 0;
      }
    }

  /**
   * Cleanup old signals
   */
  async cleanupOldSignals(days = 30) {
      try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        const result = await TradingSignal.deleteMany({
          createdAt: { $lt: cutoffDate },
          status: { $in: ['hit_tp', 'hit_sl', 'cancelled'] }
        });

        logger.logInfo(`Cleaned up ${result.deletedCount} old signals`);
        return result.deletedCount;
      } catch (error) {
        logger.logError('Error cleaning up old signals:', error.message);
        return 0;
      }
    }

  /**
   * Kiểm tra điều kiện early exit
   */
  async checkEarlyExitConditions(signal, currentPrice) {
      try {
        // Lấy dữ liệu nến mới nhất
        const candles = await binanceClient.getKlines(signal.symbol, signal.timeframe, 100);
        if (!candles || candles.length < 50) {
          return { shouldExit: false };
        }

        const indicators = require('./indicators');
        const latestIndicators = indicators.calculateAllIndicators(candles);

        if (!latestIndicators) return { shouldExit: false };

        const earlyExitReasons = [];

        // 1. Reversal pattern xuất hiện
        if (signal.type === 'BUY' && latestIndicators.engulfing === 'bearish') {
          earlyExitReasons.push('Bearish engulfing detected');
        } else if (signal.type === 'SELL' && latestIndicators.engulfing === 'bullish') {
          earlyExitReasons.push('Bullish engulfing detected');
        }

        // 2. RSI overbought/oversold extreme
        if (signal.type === 'BUY' && latestIndicators.rsi > 85) {
          earlyExitReasons.push('RSI extremely overbought (>85)');
        } else if (signal.type === 'SELL' && latestIndicators.rsi < 15) {
          earlyExitReasons.push('RSI extremely oversold (<15)');
        }

        // 3. Volume drop significantly
        if (this.checkVolumeDropoff(candles)) {
          earlyExitReasons.push('Volume dropoff detected');
        }

        // 4. MACD divergence
        if (this.checkMACDDivergence(signal, latestIndicators)) {
          earlyExitReasons.push('MACD divergence detected');
        }

        // 5. Strong opposite momentum
        if (signal.type === 'BUY' && latestIndicators.macd?.histogram < -0.001) {
          earlyExitReasons.push('Strong bearish momentum (MACD histogram)');
        } else if (signal.type === 'SELL' && latestIndicators.macd?.histogram > 0.001) {
          earlyExitReasons.push('Strong bullish momentum (MACD histogram)');
        }

        return {
          shouldExit: earlyExitReasons.length >= 2, // Cần ít nhất 2 dấu hiệu
          reasons: earlyExitReasons
        };
      } catch (error) {
        logger.logError('Error checking early exit conditions:', error.message);
        return { shouldExit: false };
      }
    }

    /**
     * Kiểm tra volume dropoff
     */
    checkVolumeDropoff(candles) {
      if (candles.length < 10) return false;

      const recentVolumes = candles.slice(-5).map(c => c.volume);
      const previousVolumes = candles.slice(-10, -5).map(c => c.volume);

      const recentAvg = recentVolumes.reduce((a, b) => a + b) / recentVolumes.length;
      const previousAvg = previousVolumes.reduce((a, b) => a + b) / previousVolumes.length;

      // Volume giảm hơn 50%
      return recentAvg < previousAvg * 0.5;
    }

    /**
     * Kiểm tra MACD divergence
     */
    checkMACDDivergence(signal, latestIndicators) {
      if (!signal.indicators?.macd || !latestIndicators.macd) return false;

      const originalMACD = signal.indicators.macd;
      const currentMACD = latestIndicators.macd;

      // Divergence: giá tăng nhưng MACD giảm (hoặc ngược lại)
      if (signal.type === 'BUY') {
        return currentMACD.macd < originalMACD.macd &&
          latestIndicators.currentPrice > signal.entry;
      } else {
        return currentMACD.macd > originalMACD.macd &&
          latestIndicators.currentPrice < signal.entry;
      }
    }

  /**
   * Thực hiện early exit
   */
  async executeEarlyExit(signal, currentPrice, reasons) {
      try {
        logger.logInfo(`Early exit triggered for ${signal.symbol} ${signal.type} at ${currentPrice}. Reasons: ${reasons.join(', ')}`);

        // Cập nhật signal trong database
        const updatedSignal = await signalAnalyzer.updateSignalStatus(
          signal._id,
          'early_exit',
          currentPrice,
          new Date()
        );

        if (updatedSignal) {
          // Cập nhật PnL dựa trên giá thực tế
          await statisticsService.updateSignalPnL(signal._id, currentPrice);

          // Xóa khỏi monitoring và trailing
          this.removeSignalFromMonitoring(signal._id.toString());
          trailingManager.removeSignalFromTrailing(signal._id.toString());

          // Lấy thống kê
          const statistics = await this.getStatistics();

          // Gửi thông báo Telegram
          await telegramBot.sendEarlyExitNotification(updatedSignal, statistics, reasons);

          logger.logInfo(`Early exit executed for ${signal.symbol}: ${signal.type} - P&L: ${updatedSignal.pnlPercent.toFixed(2)}%`);
        }
      } catch (error) {
        logger.logError(`Error executing early exit for ${signal.symbol}:`, error.message);
      }
    }

    /**
     * Sleep utility
     */
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }

module.exports = new OrderManager();
