const logger = require('../logger');
const TrailingManager = require('./trailingManager');

/**
 * AI-Enhanced Trailing Manager
 * Extends the basic trailing manager with AI-powered trailing logic,
 * partial exits, and market condition-aware adjustments
 */
class AITrailingManager extends TrailingManager {
  constructor(config = {}) {
    super();

    this.config = {
      // AI trailing settings
      aiTrailing: {
        enabled: config.aiTrailing?.enabled || true,
        confidenceThreshold: config.aiTrailing?.confidenceThreshold || 0.7,
        adaptiveTrailingDistance: config.aiTrailing?.adaptiveTrailingDistance || true,
        marketConditionAware: config.aiTrailing?.marketConditionAware || true
      },

      // Partial exit settings
      partialExit: {
        enabled: config.partialExit?.enabled || true,
        firstExitPercent: config.partialExit?.firstExitPercent || 50, // 50% at first target
        firstExitRR: config.partialExit?.firstExitRR || 1.5, // 1.5:1 risk-reward
        secondExitPercent: config.partialExit?.secondExitPercent || 30, // 30% at second target
        secondExitRR: config.partialExit?.secondExitRR || 2.5, // 2.5:1 risk-reward
        finalExitPercent: config.partialExit?.finalExitPercent || 20 // 20% trailing
      },

      // Dynamic trailing distances based on market conditions
      trailingDistances: {
        trending: {
          initial: 0.008,  // 0.8% in trending markets
          aggressive: 0.005, // 0.5% when very profitable
          conservative: 0.012 // 1.2% in uncertain conditions
        },
        sideways: {
          initial: 0.006,  // 0.6% in sideways markets
          aggressive: 0.004, // 0.4% when very profitable
          conservative: 0.010 // 1.0% in uncertain conditions
        },
        volatile: {
          initial: 0.015,  // 1.5% in volatile markets
          aggressive: 0.010, // 1.0% when very profitable
          conservative: 0.020 // 2.0% in uncertain conditions
        }
      },

      // AI optimization settings
      aiOptimization: {
        learningEnabled: config.aiOptimization?.learningEnabled || true,
        minTradesForLearning: config.aiOptimization?.minTradesForLearning || 10,
        adaptationRate: config.aiOptimization?.adaptationRate || 0.1,
        performanceThreshold: config.aiOptimization?.performanceThreshold || 0.6
      },

      ...config
    };

    // AI trailing state
    this.aiTrailingData = new Map();
    this.performanceHistory = new Map();
    this.marketConditionCache = new Map();
  }

  /**
   * Add signal to AI trailing management
   */
  async addSignalToAITrailing(signal, aiAnalysis = {}) {
    try {
      // Call parent method first
      super.addSignalToTrailing(signal);

      // Add AI-specific data
      const aiTrailingData = {
        signalId: signal._id.toString(),
        aiAnalysis: aiAnalysis,
        confidence: aiAnalysis.confidence || 0.5,
        marketCondition: aiAnalysis.marketCondition || 'unknown',
        recommendedStrategy: aiAnalysis.trailingStrategy || 'standard',
        partialExitTargets: this.calculatePartialExitTargets(signal),
        dynamicTrailingDistance: this.calculateInitialTrailingDistance(signal, aiAnalysis),
        performanceMetrics: {
          maxFavorableExcursion: 0,
          maxAdverseExcursion: 0,
          currentExcursion: 0,
          trailingEfficiency: 0
        },
        adaptiveSettings: {
          trailingDistance: null,
          lastAdjustment: new Date(),
          adjustmentCount: 0
        }
      };

      this.aiTrailingData.set(signal._id.toString(), aiTrailingData);

      logger.info('Signal added to AI trailing management', {
        symbol: signal.symbol,
        confidence: aiTrailingData.confidence,
        strategy: aiTrailingData.recommendedStrategy
      });

      return aiTrailingData;

    } catch (error) {
      logger.error('Error adding signal to AI trailing', {
        error: error.message,
        signalId: signal._id
      });
      throw error;
    }
  }

  /**
   * Update AI trailing for a specific signal
   */
  async updateAITrailing(signalId, currentPrice, marketData = {}) {
    try {
      const trailingData = this.trailingSignals.get(signalId);
      const aiData = this.aiTrailingData.get(signalId);

      if (!trailingData || !aiData) {
        return null;
      }

      // Update performance metrics
      this.updatePerformanceMetrics(signalId, currentPrice, trailingData, aiData);

      // Check for partial exits
      const partialExitResult = await this.checkPartialExits(signalId, currentPrice, trailingData, aiData);

      if (partialExitResult.executed) {
        logger.info('Partial exit executed', {
          signalId,
          exitType: partialExitResult.type,
          percentage: partialExitResult.percentage,
          price: currentPrice
        });
      }

      // Update trailing stop with AI logic
      const trailingUpdate = await this.updateAITrailingStop(signalId, currentPrice, trailingData, aiData, marketData);

      // Adapt trailing strategy based on performance
      if (this.config.aiOptimization.learningEnabled) {
        await this.adaptTrailingStrategy(signalId, trailingData, aiData);
      }

      return {
        trailingUpdate,
        partialExitResult,
        aiData: aiData
      };

    } catch (error) {
      logger.error('Error updating AI trailing', { error: error.message, signalId });
      return null;
    }
  }

  /**
   * Calculate partial exit targets based on signal and AI analysis
   */
  calculatePartialExitTargets(signal) {
    const targets = [];
    const riskAmount = Math.abs(signal.entry - signal.stopLoss);

    // First partial exit target
    const firstTarget = signal.type === 'BUY'
      ? signal.entry + (riskAmount * this.config.partialExit.firstExitRR)
      : signal.entry - (riskAmount * this.config.partialExit.firstExitRR);

    targets.push({
      price: firstTarget,
      percentage: this.config.partialExit.firstExitPercent,
      riskReward: this.config.partialExit.firstExitRR,
      executed: false
    });

    // Second partial exit target
    const secondTarget = signal.type === 'BUY'
      ? signal.entry + (riskAmount * this.config.partialExit.secondExitRR)
      : signal.entry - (riskAmount * this.config.partialExit.secondExitRR);

    targets.push({
      price: secondTarget,
      percentage: this.config.partialExit.secondExitPercent,
      riskReward: this.config.partialExit.secondExitRR,
      executed: false
    });

    return targets;
  }

  /**
   * Calculate initial trailing distance based on signal and AI analysis
   */
  calculateInitialTrailingDistance(signal, aiAnalysis) {
    const marketCondition = aiAnalysis.marketCondition || 'sideways';
    const confidence = aiAnalysis.confidence || 0.5;

    let baseDistance = this.config.trailingDistances[marketCondition]?.initial || 0.008;

    // Adjust based on confidence
    if (confidence > 0.8) {
      baseDistance *= 0.8; // Tighter trailing for high confidence
    } else if (confidence < 0.5) {
      baseDistance *= 1.3; // Wider trailing for low confidence
    }

    return baseDistance;
  }

  /**
   * Update performance metrics for tracking
   */
  updatePerformanceMetrics(signalId, currentPrice, trailingData, aiData) {
    const entry = trailingData.entry;
    const isLong = trailingData.type === 'BUY';

    // Calculate current excursion
    const currentExcursion = isLong
      ? (currentPrice - entry) / entry
      : (entry - currentPrice) / entry;

    aiData.performanceMetrics.currentExcursion = currentExcursion;

    // Update max favorable excursion
    if (currentExcursion > aiData.performanceMetrics.maxFavorableExcursion) {
      aiData.performanceMetrics.maxFavorableExcursion = currentExcursion;
    }

    // Update max adverse excursion (if negative)
    if (currentExcursion < 0 && Math.abs(currentExcursion) > aiData.performanceMetrics.maxAdverseExcursion) {
      aiData.performanceMetrics.maxAdverseExcursion = Math.abs(currentExcursion);
    }

    // Calculate trailing efficiency
    const favorableExcursion = aiData.performanceMetrics.maxFavorableExcursion;
    if (favorableExcursion > 0) {
      aiData.performanceMetrics.trailingEfficiency = currentExcursion / favorableExcursion;
    }
  }

  /**
   * Check and execute partial exits
   */
  async checkPartialExits(signalId, currentPrice, trailingData, aiData) {
    if (!this.config.partialExit.enabled) {
      return { executed: false };
    }

    const isLong = trailingData.type === 'BUY';

    for (let i = 0; i < aiData.partialExitTargets.length; i++) {
      const target = aiData.partialExitTargets[i];

      if (target.executed) continue;

      const targetReached = isLong
        ? currentPrice >= target.price
        : currentPrice <= target.price;

      if (targetReached) {
        // Execute partial exit
        target.executed = true;
        target.executionPrice = currentPrice;
        target.executionTime = new Date();

        // Update remaining position
        const remainingPercent = 100 - target.percentage;

        return {
          executed: true,
          type: `partial_exit_${i + 1}`,
          percentage: target.percentage,
          price: currentPrice,
          remainingPercent: remainingPercent,
          riskReward: target.riskReward
        };
      }
    }

    return { executed: false };
  }

  /**
   * Update trailing stop with AI logic
   */
  async updateAITrailingStop(signalId, currentPrice, trailingData, aiData, marketData) {
    const isLong = trailingData.type === 'BUY';

    // Get current market condition
    const marketCondition = await this.getCurrentMarketCondition(trailingData.symbol, marketData);

    // Calculate adaptive trailing distance
    const adaptiveDistance = this.calculateAdaptiveTrailingDistance(
      signalId,
      currentPrice,
      trailingData,
      aiData,
      marketCondition
    );

    // Update trailing stop
    let newStopLoss = trailingData.currentSL;
    let trailingActivated = false;

    if (isLong) {
      // For long positions
      if (currentPrice > trailingData.highestPrice || !trailingData.highestPrice) {
        trailingData.highestPrice = currentPrice;

        const potentialNewSL = currentPrice * (1 - adaptiveDistance);

        if (potentialNewSL > trailingData.currentSL) {
          newStopLoss = potentialNewSL;
          trailingActivated = true;
        }
      }
    } else {
      // For short positions
      if (currentPrice < trailingData.lowestPrice || !trailingData.lowestPrice) {
        trailingData.lowestPrice = currentPrice;

        const potentialNewSL = currentPrice * (1 + adaptiveDistance);

        if (potentialNewSL < trailingData.currentSL) {
          newStopLoss = potentialNewSL;
          trailingActivated = true;
        }
      }
    }

    if (trailingActivated) {
      trailingData.currentSL = newStopLoss;
      trailingData.trailingActive = true;

      // Update adaptive settings
      aiData.adaptiveSettings.trailingDistance = adaptiveDistance;
      aiData.adaptiveSettings.lastAdjustment = new Date();
      aiData.adaptiveSettings.adjustmentCount++;

      return {
        activated: true,
        newStopLoss: newStopLoss,
        trailingDistance: adaptiveDistance,
        marketCondition: marketCondition
      };
    }

    return { activated: false };
  }

  /**
   * Calculate adaptive trailing distance based on multiple factors
   */
  calculateAdaptiveTrailingDistance(signalId, currentPrice, trailingData, aiData, marketCondition) {
    const baseDistance = this.config.trailingDistances[marketCondition]?.initial || 0.008;
    const confidence = aiData.confidence;
    const efficiency = aiData.performanceMetrics.trailingEfficiency;

    let adaptiveDistance = baseDistance;

    // Adjust based on confidence
    if (confidence > 0.8) {
      adaptiveDistance *= 0.8;
    } else if (confidence < 0.5) {
      adaptiveDistance *= 1.2;
    }

    // Adjust based on trailing efficiency
    if (efficiency > 0.8) {
      adaptiveDistance *= 0.9; // Tighter trailing when efficient
    } else if (efficiency < 0.5) {
      adaptiveDistance *= 1.1; // Wider trailing when inefficient
    }

    // Adjust based on profit level
    const profitPercent = aiData.performanceMetrics.currentExcursion;
    if (profitPercent > 0.03) { // > 3% profit
      adaptiveDistance *= 0.7; // Aggressive trailing
    } else if (profitPercent > 0.01) { // > 1% profit
      adaptiveDistance *= 0.9; // Moderate trailing
    }

    return Math.max(0.003, Math.min(0.025, adaptiveDistance)); // Clamp between 0.3% and 2.5%
  }

  /**
   * Get current market condition
   */
  async getCurrentMarketCondition(symbol, marketData) {
    // Check cache first
    const cached = this.marketConditionCache.get(symbol);
    if (cached && (Date.now() - cached.timestamp) < 5 * 60 * 1000) { // 5 minutes cache
      return cached.condition;
    }

    // Analyze market condition (simplified)
    let condition = 'sideways';

    if (marketData.volatility > 0.06) {
      condition = 'volatile';
    } else if (marketData.trend && Math.abs(marketData.trend) > 0.02) {
      condition = 'trending';
    }

    // Cache the result
    this.marketConditionCache.set(symbol, {
      condition: condition,
      timestamp: Date.now()
    });

    return condition;
  }

  /**
   * Adapt trailing strategy based on performance
   */
  async adaptTrailingStrategy(signalId, trailingData, aiData) {
    if (!this.config.aiOptimization.learningEnabled) return;

    const performance = aiData.performanceMetrics;
    const efficiency = performance.trailingEfficiency;

    // Only adapt if we have enough data
    if (aiData.adaptiveSettings.adjustmentCount < 3) return;

    // If efficiency is low, increase trailing distance
    if (efficiency < this.config.aiOptimization.performanceThreshold) {
      const currentDistance = aiData.adaptiveSettings.trailingDistance;
      const newDistance = currentDistance * (1 + this.config.aiOptimization.adaptationRate);

      aiData.adaptiveSettings.trailingDistance = Math.min(0.025, newDistance);

      logger.debug('Adapted trailing distance (increased)', {
        signalId,
        oldDistance: currentDistance,
        newDistance: aiData.adaptiveSettings.trailingDistance,
        efficiency
      });
    }
  }

  /**
   * Get AI trailing recommendations for a signal
   */
  async getAITrailingRecommendations(signalId, currentPrice, marketData = {}) {
    try {
      const trailingData = this.trailingSignals.get(signalId);
      const aiData = this.aiTrailingData.get(signalId);

      if (!trailingData || !aiData) {
        return { recommendations: [], confidence: 0 };
      }

      const recommendations = [];
      const performance = aiData.performanceMetrics;

      // Calculate current profit based on current price
      const isLong = trailingData.type === 'BUY';
      const currentProfit = isLong
        ? (currentPrice - trailingData.entry) / trailingData.entry
        : (trailingData.entry - currentPrice) / trailingData.entry;

      // Profit-taking recommendations
      if (currentProfit > 0.05) { // > 5% profit
        recommendations.push({
          type: 'profit_taking',
          action: 'Consider taking partial profits',
          confidence: 0.8,
          reason: 'Significant profit achieved'
        });
      }

      // Trailing distance recommendations
      if (performance.trailingEfficiency < 0.5) {
        recommendations.push({
          type: 'trailing_adjustment',
          action: 'Increase trailing distance',
          confidence: 0.7,
          reason: 'Low trailing efficiency detected'
        });
      }

      // Market condition recommendations
      const marketCondition = await this.getCurrentMarketCondition(trailingData.symbol, marketData);
      if (marketCondition === 'volatile') {
        recommendations.push({
          type: 'risk_management',
          action: 'Use wider trailing stops',
          confidence: 0.6,
          reason: 'High market volatility detected'
        });
      }

      // Breakeven recommendations
      if (currentProfit > 0.01 && !trailingData.breakeven) {
        recommendations.push({
          type: 'breakeven',
          action: 'Move stop loss to breakeven',
          confidence: 0.9,
          reason: 'Sufficient profit to secure breakeven'
        });
      }

      return {
        recommendations,
        confidence: aiData.confidence,
        marketCondition,
        performance: performance
      };

    } catch (error) {
      logger.error('Error getting AI trailing recommendations', { error: error.message, signalId });
      return { recommendations: [], confidence: 0 };
    }
  }

  /**
   * Update performance history after trade completion
   */
  updatePerformanceHistory(signalId, tradeResult) {
    try {
      const aiData = this.aiTrailingData.get(signalId);
      if (!aiData) return;

      const symbol = tradeResult.symbol;

      if (!this.performanceHistory.has(symbol)) {
        this.performanceHistory.set(symbol, []);
      }

      const history = this.performanceHistory.get(symbol);

      const performanceRecord = {
        signalId,
        timestamp: new Date(),
        pnl: tradeResult.pnl,
        pnlPercent: tradeResult.pnlPercent,
        exitReason: tradeResult.exitReason,
        maxFavorableExcursion: aiData.performanceMetrics.maxFavorableExcursion,
        maxAdverseExcursion: aiData.performanceMetrics.maxAdverseExcursion,
        trailingEfficiency: aiData.performanceMetrics.trailingEfficiency,
        partialExitsExecuted: aiData.partialExitTargets.filter(t => t.executed).length,
        trailingAdjustments: aiData.adaptiveSettings.adjustmentCount,
        finalTrailingDistance: aiData.adaptiveSettings.trailingDistance
      };

      history.push(performanceRecord);

      // Keep only last 100 records per symbol
      if (history.length > 100) {
        history.splice(0, history.length - 100);
      }

      this.performanceHistory.set(symbol, history);

      logger.debug('Performance history updated', {
        symbol,
        signalId,
        efficiency: performanceRecord.trailingEfficiency,
        partialExits: performanceRecord.partialExitsExecuted
      });

    } catch (error) {
      logger.error('Error updating performance history', { error: error.message, signalId });
    }
  }

  /**
   * Get performance analytics for a symbol
   */
  getPerformanceAnalytics(symbol) {
    try {
      const history = this.performanceHistory.get(symbol) || [];

      if (history.length === 0) {
        return {
          totalTrades: 0,
          avgTrailingEfficiency: 0,
          avgPartialExits: 0,
          winRate: 0,
          avgPnl: 0
        };
      }

      const totalTrades = history.length;
      const wins = history.filter(h => h.pnl > 0).length;
      const winRate = (wins / totalTrades) * 100;

      const avgTrailingEfficiency = history.reduce((sum, h) => sum + h.trailingEfficiency, 0) / totalTrades;
      const avgPartialExits = history.reduce((sum, h) => sum + h.partialExitsExecuted, 0) / totalTrades;
      const avgPnl = history.reduce((sum, h) => sum + h.pnlPercent, 0) / totalTrades;

      const avgMFE = history.reduce((sum, h) => sum + h.maxFavorableExcursion, 0) / totalTrades;
      const avgMAE = history.reduce((sum, h) => sum + h.maxAdverseExcursion, 0) / totalTrades;

      return {
        totalTrades,
        winRate,
        avgPnl,
        avgTrailingEfficiency,
        avgPartialExits,
        avgMaxFavorableExcursion: avgMFE,
        avgMaxAdverseExcursion: avgMAE,
        recentTrades: history.slice(-10) // Last 10 trades
      };

    } catch (error) {
      logger.error('Error getting performance analytics', { error: error.message, symbol });
      return { totalTrades: 0, avgTrailingEfficiency: 0 };
    }
  }

  /**
   * Remove signal from AI trailing management
   */
  removeSignalFromAITrailing(signalId) {
    try {
      const removed = this.trailingSignals.delete(signalId);
      const aiRemoved = this.aiTrailingData.delete(signalId);

      if (removed || aiRemoved) {
        logger.info('Signal removed from AI trailing management', { signalId });
      }

      return removed || aiRemoved;

    } catch (error) {
      logger.error('Error removing signal from AI trailing', { error: error.message, signalId });
      return false;
    }
  }

  /**
   * Get all active AI trailing signals
   */
  getActiveAITrailingSignals() {
    const activeSignals = [];

    for (const [signalId, trailingData] of this.trailingSignals.entries()) {
      const aiData = this.aiTrailingData.get(signalId);

      if (aiData) {
        activeSignals.push({
          signalId,
          symbol: trailingData.symbol,
          type: trailingData.type,
          entry: trailingData.entry,
          currentSL: trailingData.currentSL,
          trailingActive: trailingData.trailingActive,
          confidence: aiData.confidence,
          marketCondition: aiData.marketCondition,
          performance: aiData.performanceMetrics,
          partialExits: aiData.partialExitTargets.filter(t => t.executed).length
        });
      }
    }

    return activeSignals;
  }

  /**
   * Get AI trailing configuration
   */
  getAITrailingConfig() {
    return {
      aiTrailing: this.config.aiTrailing,
      partialExit: this.config.partialExit,
      trailingDistances: this.config.trailingDistances,
      aiOptimization: this.config.aiOptimization
    };
  }

  /**
   * Update AI trailing configuration
   */
  updateAITrailingConfig(newConfig) {
    try {
      this.config = { ...this.config, ...newConfig };
      logger.info('AI trailing configuration updated', { newConfig });
      return true;
    } catch (error) {
      logger.error('Error updating AI trailing configuration', { error: error.message });
      return false;
    }
  }
}

module.exports = AITrailingManager;
