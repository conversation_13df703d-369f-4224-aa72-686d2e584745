const logger = require('../logger');
const { calculateATR } = require('./indicators');

/**
 * Adaptive Position Sizer
 * Calculates optimal position sizes based on signal quality, symbol performance,
 * market conditions, volatility, and time-based factors
 */
class AdaptivePositionSizer {
  constructor(config = {}) {
    this.config = {
      // Base risk settings
      baseRiskPercent: config.baseRiskPercent || 1.0, // 1% risk per trade
      maxRiskPercent: config.maxRiskPercent || 3.0,   // Maximum 3% risk
      minPositionSize: config.minPositionSize || 0.001, // Minimum position size
      maxPositionSize: config.maxPositionSize || 0.1,   // Maximum 10% of portfolio

      // Quality-based multipliers
      qualityMultipliers: {
        excellent: 1.5,  // 90+ quality score
        good: 1.2,       // 80-89 quality score
        average: 1.0,    // 70-79 quality score
        weak: 0.5,       // 60-69 quality score
        poor: 0.2        // <60 quality score
      },

      // Time-based multipliers for optimal trading hours
      timeMultipliers: {
        // UTC hours - peak trading times get higher multipliers
        peak: { hours: [8, 9, 10, 13, 14, 15, 16], multiplier: 1.2 },
        good: { hours: [7, 11, 12, 17, 18], multiplier: 1.0 },
        average: { hours: [6, 19, 20, 21], multiplier: 0.8 },
        poor: { hours: [0, 1, 2, 3, 4, 5, 22, 23], multiplier: 0.5 }
      },

      // Volatility adjustment settings
      volatilityAdjustment: {
        lowThreshold: 0.02,    // 2% daily volatility
        mediumThreshold: 0.04, // 4% daily volatility
        highThreshold: 0.06,   // 6% daily volatility
        multipliers: {
          veryLow: 1.3,   // < 2% volatility
          low: 1.1,       // 2-4% volatility
          medium: 1.0,    // 4-6% volatility
          high: 0.7,      // 6-8% volatility
          veryHigh: 0.4   // > 8% volatility
        }
      },

      // Symbol performance tracking
      performanceTracking: {
        lookbackDays: 30,
        minTradesForAdjustment: 5,
        performanceMultipliers: {
          excellent: 1.4,  // > 70% win rate
          good: 1.2,       // 60-70% win rate
          average: 1.0,    // 50-60% win rate
          poor: 0.7,       // 40-50% win rate
          veryPoor: 0.4    // < 40% win rate
        }
      },

      // Safety limits
      safetyLimits: {
        maxDailyRisk: 5.0,        // Maximum 5% daily risk
        maxWeeklyRisk: 15.0,      // Maximum 15% weekly risk
        maxCorrelatedRisk: 8.0,   // Maximum 8% risk in correlated positions
        emergencyStopLoss: 20.0   // Emergency stop at 20% portfolio loss
      },

      ...config
    };

    // Performance tracking storage
    this.symbolPerformance = new Map();
    this.dailyRiskUsed = 0;
    this.weeklyRiskUsed = 0;
    this.lastResetDate = new Date();
  }

  /**
   * Calculate optimal position size for a signal
   */
  async calculateOptimalSize(signal, riskAssessment, portfolioValue, currentPortfolio = {}) {
    try {
      const calculation = {
        baseSize: 0,
        finalSize: 0,
        riskAmount: 0,
        multipliers: {},
        validationResults: {},
        recommendations: []
      };

      // Calculate base position size
      calculation.baseSize = this.calculateBaseSize(signal, portfolioValue);
      calculation.riskAmount = calculation.baseSize;

      // Apply quality multiplier
      const qualityMultiplier = this.getQualityMultiplier(signal.qualityScore || 75);
      calculation.multipliers.quality = qualityMultiplier;
      calculation.baseSize *= qualityMultiplier;

      // Apply symbol performance multiplier
      const symbolMultiplier = await this.getSymbolPerformanceMultiplier(signal.symbol);
      calculation.multipliers.symbol = symbolMultiplier;
      calculation.baseSize *= symbolMultiplier;

      // Apply time-based multiplier
      const timeMultiplier = this.getTimeBasedMultiplier();
      calculation.multipliers.time = timeMultiplier;
      calculation.baseSize *= timeMultiplier;

      // Apply volatility adjustment
      const volatilityMultiplier = await this.getVolatilityMultiplier(signal.symbol);
      calculation.multipliers.volatility = volatilityMultiplier;
      calculation.baseSize *= volatilityMultiplier;

      // Apply risk assessment multiplier
      const riskMultiplier = riskAssessment.positionSizeMultiplier || 1.0;
      calculation.multipliers.risk = riskMultiplier;
      calculation.baseSize *= riskMultiplier;

      // Calculate actual position size based on stop loss distance
      const positionSize = this.calculatePositionFromRisk(
        calculation.baseSize,
        signal.entry,
        signal.stopLoss
      );

      calculation.finalSize = positionSize;

      // Validate position size
      calculation.validationResults = await this.validatePositionSize(
        calculation.finalSize,
        calculation.riskAmount,
        signal,
        portfolioValue,
        currentPortfolio
      );

      // Apply validation adjustments
      if (!calculation.validationResults.isValid) {
        calculation.finalSize = calculation.validationResults.adjustedSize;
        calculation.recommendations.push(...calculation.validationResults.recommendations);
      }

      // Final safety check
      calculation.finalSize = Math.max(
        this.config.minPositionSize,
        Math.min(calculation.finalSize, this.config.maxPositionSize * portfolioValue)
      );

      logger.info('Position size calculated', {
        symbol: signal.symbol,
        baseRisk: calculation.riskAmount,
        finalSize: calculation.finalSize,
        multipliers: calculation.multipliers,
        valid: calculation.validationResults.isValid
      });

      return calculation;

    } catch (error) {
      logger.error('Error calculating optimal position size', {
        error: error.message,
        symbol: signal.symbol
      });

      // Return conservative position on error
      return {
        baseSize: portfolioValue * 0.005, // 0.5% risk
        finalSize: portfolioValue * 0.005,
        riskAmount: portfolioValue * 0.005,
        multipliers: { error: 0.5 },
        validationResults: { isValid: false, reason: 'calculation_error' },
        recommendations: ['Error in position calculation - using conservative size']
      };
    }
  }

  /**
   * Calculate base position size from portfolio value and risk percentage
   */
  calculateBaseSize(signal, portfolioValue) {
    const riskPercent = this.config.baseRiskPercent / 100;
    return portfolioValue * riskPercent;
  }

  /**
   * Get quality-based multiplier
   */
  getQualityMultiplier(qualityScore) {
    const multipliers = this.config.qualityMultipliers;

    if (qualityScore >= 90) return multipliers.excellent;
    if (qualityScore >= 80) return multipliers.good;
    if (qualityScore >= 70) return multipliers.average;
    if (qualityScore >= 60) return multipliers.weak;
    return multipliers.poor;
  }

  /**
   * Get symbol performance multiplier based on historical performance
   */
  async getSymbolPerformanceMultiplier(symbol) {
    try {
      const performance = this.symbolPerformance.get(symbol);

      if (!performance || performance.trades.length < this.config.performanceTracking.minTradesForAdjustment) {
        return 1.0; // Default multiplier for new symbols
      }

      const winRate = this.calculateWinRate(performance.trades);
      const multipliers = this.config.performanceTracking.performanceMultipliers;

      if (winRate > 70) return multipliers.excellent;
      if (winRate > 60) return multipliers.good;
      if (winRate > 50) return multipliers.average;
      if (winRate > 40) return multipliers.poor;
      return multipliers.veryPoor;

    } catch (error) {
      logger.error('Error getting symbol performance multiplier', { error: error.message, symbol });
      return 1.0;
    }
  }

  /**
   * Get time-based multiplier for current hour
   */
  getTimeBasedMultiplier() {
    const currentHour = new Date().getUTCHours();
    const timeConfig = this.config.timeMultipliers;

    if (timeConfig.peak.hours.includes(currentHour)) return timeConfig.peak.multiplier;
    if (timeConfig.good.hours.includes(currentHour)) return timeConfig.good.multiplier;
    if (timeConfig.average.hours.includes(currentHour)) return timeConfig.average.multiplier;
    return timeConfig.poor.multiplier;
  }

  /**
   * Get volatility-based multiplier
   */
  async getVolatilityMultiplier(symbol) {
    try {
      // This would typically fetch recent candle data to calculate volatility
      // For now, we'll use a simplified approach
      const volatility = await this.estimateVolatility(symbol);
      const config = this.config.volatilityAdjustment;

      if (volatility < config.lowThreshold) return config.multipliers.veryLow;
      if (volatility < config.mediumThreshold) return config.multipliers.low;
      if (volatility < config.highThreshold) return config.multipliers.medium;
      if (volatility < config.highThreshold * 1.33) return config.multipliers.high;
      return config.multipliers.veryHigh;

    } catch (error) {
      logger.error('Error getting volatility multiplier', { error: error.message, symbol });
      return 1.0;
    }
  }

  /**
   * Calculate position size from risk amount and stop loss distance
   */
  calculatePositionFromRisk(riskAmount, entryPrice, stopLossPrice) {
    const stopLossDistance = Math.abs(entryPrice - stopLossPrice);
    if (stopLossDistance === 0) {
      logger.warn('Stop loss distance is zero, using minimum position size');
      return this.config.minPositionSize;
    }

    return riskAmount / stopLossDistance;
  }

  /**
   * Estimate volatility for a symbol (simplified implementation)
   */
  async estimateVolatility(symbol) {
    // Simplified volatility estimation based on symbol characteristics
    const volatilityMap = {
      'BTCUSDT': 0.03,
      'ETHUSDT': 0.04,
      'ADAUSDT': 0.06,
      'DOGEUSDT': 0.08,
      'SOLUSDT': 0.07,
      'DOTUSDT': 0.06,
      'AVAXUSDT': 0.07,
      'LTCUSDT': 0.05,
      'BNBUSDT': 0.05,
      'XRPUSDT': 0.06
    };

    const baseVolatility = volatilityMap[symbol] || 0.05;

    // Add some randomness to simulate market conditions
    return baseVolatility * (0.8 + Math.random() * 0.4);
  }

  /**
   * Calculate win rate from trade history
   */
  calculateWinRate(trades) {
    if (trades.length === 0) return 50; // Default 50% for new symbols

    const wins = trades.filter(trade => trade.pnl > 0).length;
    return (wins / trades.length) * 100;
  }

  /**
   * Validate position size against safety limits
   */
  async validatePositionSize(positionSize, riskAmount, signal, portfolioValue, currentPortfolio) {
    const validation = {
      isValid: true,
      adjustedSize: positionSize,
      recommendations: [],
      violations: []
    };

    try {
      // Check minimum position size
      if (positionSize < this.config.minPositionSize) {
        validation.isValid = false;
        validation.adjustedSize = this.config.minPositionSize;
        validation.violations.push('below_minimum_size');
        validation.recommendations.push('Position size increased to minimum allowed');
      }

      // Check maximum position size
      const maxSize = this.config.maxPositionSize * portfolioValue;
      if (positionSize > maxSize) {
        validation.isValid = false;
        validation.adjustedSize = maxSize;
        validation.violations.push('above_maximum_size');
        validation.recommendations.push('Position size reduced to maximum allowed');
      }

      // Check daily risk limit
      const riskPercent = (riskAmount / portfolioValue) * 100;
      if (this.dailyRiskUsed + riskPercent > this.config.safetyLimits.maxDailyRisk) {
        const allowedRisk = this.config.safetyLimits.maxDailyRisk - this.dailyRiskUsed;
        if (allowedRisk <= 0) {
          validation.isValid = false;
          validation.adjustedSize = 0;
          validation.violations.push('daily_risk_exceeded');
          validation.recommendations.push('Daily risk limit exceeded - no new positions allowed');
        } else {
          const adjustedRiskAmount = portfolioValue * (allowedRisk / 100);
          validation.adjustedSize = this.calculatePositionFromRisk(
            adjustedRiskAmount,
            signal.entry,
            signal.stopLoss
          );
          validation.violations.push('daily_risk_adjusted');
          validation.recommendations.push(`Position size reduced due to daily risk limit`);
        }
      }

      // Check weekly risk limit
      if (this.weeklyRiskUsed + riskPercent > this.config.safetyLimits.maxWeeklyRisk) {
        const allowedRisk = this.config.safetyLimits.maxWeeklyRisk - this.weeklyRiskUsed;
        if (allowedRisk <= 0) {
          validation.isValid = false;
          validation.adjustedSize = 0;
          validation.violations.push('weekly_risk_exceeded');
          validation.recommendations.push('Weekly risk limit exceeded - no new positions allowed');
        } else {
          const adjustedRiskAmount = portfolioValue * (allowedRisk / 100);
          validation.adjustedSize = Math.min(
            validation.adjustedSize,
            this.calculatePositionFromRisk(adjustedRiskAmount, signal.entry, signal.stopLoss)
          );
          validation.violations.push('weekly_risk_adjusted');
          validation.recommendations.push(`Position size reduced due to weekly risk limit`);
        }
      }

      // Check correlation risk
      const correlationRisk = await this.calculateCorrelationRisk(signal.symbol, currentPortfolio, riskAmount);
      if (correlationRisk.totalRisk > this.config.safetyLimits.maxCorrelatedRisk) {
        const allowedRisk = this.config.safetyLimits.maxCorrelatedRisk - correlationRisk.existingRisk;
        if (allowedRisk <= 0) {
          validation.isValid = false;
          validation.adjustedSize = 0;
          validation.violations.push('correlation_risk_exceeded');
          validation.recommendations.push('Correlation risk limit exceeded - avoid this position');
        } else {
          const adjustedRiskAmount = portfolioValue * (allowedRisk / 100);
          validation.adjustedSize = Math.min(
            validation.adjustedSize,
            this.calculatePositionFromRisk(adjustedRiskAmount, signal.entry, signal.stopLoss)
          );
          validation.violations.push('correlation_risk_adjusted');
          validation.recommendations.push(`Position size reduced due to correlation risk`);
        }
      }

      return validation;

    } catch (error) {
      logger.error('Error validating position size', { error: error.message, symbol: signal.symbol });
      return {
        isValid: false,
        adjustedSize: this.config.minPositionSize,
        recommendations: ['Error in validation - using minimum position size'],
        violations: ['validation_error']
      };
    }
  }

  /**
   * Calculate correlation risk with existing portfolio
   */
  async calculateCorrelationRisk(symbol, currentPortfolio, newRiskAmount) {
    // Simplified correlation risk calculation
    // In production, this would use the CorrelationRiskCalculator
    const correlationMap = {
      'BTCUSDT': { 'ETHUSDT': 0.8, 'ADAUSDT': 0.6, 'SOLUSDT': 0.7 },
      'ETHUSDT': { 'BTCUSDT': 0.8, 'ADAUSDT': 0.7, 'SOLUSDT': 0.8 },
      'ADAUSDT': { 'BTCUSDT': 0.6, 'ETHUSDT': 0.7, 'SOLUSDT': 0.6 },
      'SOLUSDT': { 'BTCUSDT': 0.7, 'ETHUSDT': 0.8, 'ADAUSDT': 0.6 }
    };

    let existingRisk = 0;
    const correlations = correlationMap[symbol] || {};

    for (const [portfolioSymbol, position] of Object.entries(currentPortfolio)) {
      if (position.riskAmount && correlations[portfolioSymbol]) {
        const correlation = correlations[portfolioSymbol];
        existingRisk += position.riskAmount * correlation;
      }
    }

    return {
      existingRisk: existingRisk,
      newRisk: newRiskAmount,
      totalRisk: existingRisk + newRiskAmount
    };
  }

  /**
   * Update symbol performance after trade completion
   */
  updateSymbolPerformance(symbol, tradeResult) {
    try {
      if (!this.symbolPerformance.has(symbol)) {
        this.symbolPerformance.set(symbol, {
          trades: [],
          totalPnl: 0,
          winRate: 0,
          lastUpdated: new Date()
        });
      }

      const performance = this.symbolPerformance.get(symbol);

      // Add new trade
      performance.trades.push({
        pnl: tradeResult.pnl,
        pnlPercent: tradeResult.pnlPercent,
        timestamp: new Date(),
        exitReason: tradeResult.exitReason
      });

      // Keep only recent trades (last 30 days)
      const cutoffDate = new Date(Date.now() - this.config.performanceTracking.lookbackDays * 24 * 60 * 60 * 1000);
      performance.trades = performance.trades.filter(trade => trade.timestamp > cutoffDate);

      // Update metrics
      performance.totalPnl = performance.trades.reduce((sum, trade) => sum + trade.pnl, 0);
      performance.winRate = this.calculateWinRate(performance.trades);
      performance.lastUpdated = new Date();

      this.symbolPerformance.set(symbol, performance);

      logger.debug('Symbol performance updated', {
        symbol,
        trades: performance.trades.length,
        winRate: performance.winRate,
        totalPnl: performance.totalPnl
      });

    } catch (error) {
      logger.error('Error updating symbol performance', { error: error.message, symbol });
    }
  }

  /**
   * Update daily and weekly risk tracking
   */
  updateRiskTracking(riskAmount, portfolioValue) {
    try {
      const now = new Date();
      const riskPercent = (riskAmount / portfolioValue) * 100;

      // Reset daily risk if new day
      if (now.toDateString() !== this.lastResetDate.toDateString()) {
        this.dailyRiskUsed = 0;
        this.lastResetDate = now;
      }

      // Reset weekly risk if new week
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay());
      const lastResetWeekStart = new Date(this.lastResetDate);
      lastResetWeekStart.setDate(this.lastResetDate.getDate() - this.lastResetDate.getDay());

      if (weekStart.getTime() !== lastResetWeekStart.getTime()) {
        this.weeklyRiskUsed = 0;
      }

      // Add current risk
      this.dailyRiskUsed += riskPercent;
      this.weeklyRiskUsed += riskPercent;

      logger.debug('Risk tracking updated', {
        dailyRiskUsed: this.dailyRiskUsed,
        weeklyRiskUsed: this.weeklyRiskUsed,
        newRisk: riskPercent
      });

    } catch (error) {
      logger.error('Error updating risk tracking', { error: error.message });
    }
  }

  /**
   * Get current risk utilization
   */
  getRiskUtilization() {
    return {
      dailyRiskUsed: this.dailyRiskUsed,
      weeklyRiskUsed: this.weeklyRiskUsed,
      dailyRiskRemaining: Math.max(0, this.config.safetyLimits.maxDailyRisk - this.dailyRiskUsed),
      weeklyRiskRemaining: Math.max(0, this.config.safetyLimits.maxWeeklyRisk - this.weeklyRiskUsed),
      lastResetDate: this.lastResetDate
    };
  }

  /**
   * Get symbol performance summary
   */
  getSymbolPerformanceSummary() {
    const summary = {};

    for (const [symbol, performance] of this.symbolPerformance.entries()) {
      summary[symbol] = {
        trades: performance.trades.length,
        winRate: performance.winRate,
        totalPnl: performance.totalPnl,
        lastUpdated: performance.lastUpdated,
        multiplier: this.getSymbolPerformanceMultiplier(symbol)
      };
    }

    return summary;
  }

  /**
   * Get position sizing recommendations for a symbol
   */
  async getPositionSizingRecommendations(symbol, signal, portfolioValue) {
    try {
      const recommendations = [];

      // Quality-based recommendation
      const qualityMultiplier = this.getQualityMultiplier(signal.qualityScore || 75);
      if (qualityMultiplier > 1.2) {
        recommendations.push({
          type: 'quality',
          message: 'High quality signal - consider larger position',
          multiplier: qualityMultiplier
        });
      } else if (qualityMultiplier < 0.8) {
        recommendations.push({
          type: 'quality',
          message: 'Low quality signal - consider smaller position',
          multiplier: qualityMultiplier
        });
      }

      // Time-based recommendation
      const timeMultiplier = this.getTimeBasedMultiplier();
      if (timeMultiplier < 0.8) {
        recommendations.push({
          type: 'time',
          message: 'Non-optimal trading hours - consider reduced position',
          multiplier: timeMultiplier
        });
      }

      // Volatility-based recommendation
      const volatilityMultiplier = await this.getVolatilityMultiplier(symbol);
      if (volatilityMultiplier < 0.8) {
        recommendations.push({
          type: 'volatility',
          message: 'High volatility detected - consider smaller position',
          multiplier: volatilityMultiplier
        });
      }

      // Symbol performance recommendation
      const symbolMultiplier = await this.getSymbolPerformanceMultiplier(symbol);
      if (symbolMultiplier < 0.8) {
        recommendations.push({
          type: 'performance',
          message: 'Poor recent performance on this symbol - consider smaller position',
          multiplier: symbolMultiplier
        });
      } else if (symbolMultiplier > 1.2) {
        recommendations.push({
          type: 'performance',
          message: 'Good recent performance on this symbol - consider larger position',
          multiplier: symbolMultiplier
        });
      }

      // Risk utilization recommendation
      const riskUtil = this.getRiskUtilization();
      if (riskUtil.dailyRiskUsed > this.config.safetyLimits.maxDailyRisk * 0.8) {
        recommendations.push({
          type: 'risk_limit',
          message: 'Approaching daily risk limit - consider smaller position',
          multiplier: 0.5
        });
      }

      return recommendations;

    } catch (error) {
      logger.error('Error getting position sizing recommendations', { error: error.message, symbol });
      return [];
    }
  }

  /**
   * Reset risk tracking (for testing or manual reset)
   */
  resetRiskTracking() {
    this.dailyRiskUsed = 0;
    this.weeklyRiskUsed = 0;
    this.lastResetDate = new Date();
    logger.info('Risk tracking reset');
  }

  /**
   * Reset symbol performance (for testing or manual reset)
   */
  resetSymbolPerformance(symbol = null) {
    if (symbol) {
      this.symbolPerformance.delete(symbol);
      logger.info('Symbol performance reset', { symbol });
    } else {
      this.symbolPerformance.clear();
      logger.info('All symbol performance reset');
    }
  }

  /**
   * Get configuration summary
   */
  getConfigSummary() {
    return {
      baseRiskPercent: this.config.baseRiskPercent,
      maxRiskPercent: this.config.maxRiskPercent,
      minPositionSize: this.config.minPositionSize,
      maxPositionSize: this.config.maxPositionSize,
      safetyLimits: this.config.safetyLimits,
      qualityMultipliers: this.config.qualityMultipliers,
      timeMultipliers: this.config.timeMultipliers,
      volatilityAdjustment: this.config.volatilityAdjustment,
      performanceTracking: this.config.performanceTracking
    };
  }
}

module.exports = AdaptivePositionSizer;
