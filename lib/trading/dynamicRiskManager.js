const logger = require('../logger');
const { calculateATR } = require('./indicators');

/**
 * Dynamic Risk Manager
 * Tracks consecutive losses, drawdown, market volatility, and manages trading risk
 */
class DynamicRiskManager {
  constructor(config = {}) {
    this.config = {
      maxConsecutiveLosses: config.maxConsecutiveLosses || 5,
      maxDrawdownPercent: config.maxDrawdownPercent || 15,
      volatilityThreshold: config.volatilityThreshold || 0.05,
      correlationThreshold: config.correlationThreshold || 0.8,
      pauseDurationHours: config.pauseDurationHours || 24,
      riskMultipliers: {
        consecutive3Losses: 0.5,
        consecutive5Losses: 0.0,
        highVolatility: 0.7,
        highCorrelation: 0.6,
        highDrawdown: 0.3
      },
      ...config
    };

    // Risk state tracking
    this.consecutiveLosses = 0;
    this.currentDrawdown = 0;
    this.riskMultiplier = 1.0;
    this.tradingPaused = false;
    this.pauseEndTime = null;
    this.marketVolatility = 'normal';
    this.recentTrades = [];
    this.portfolioPositions = new Map();

    // Performance tracking
    this.initialBalance = 0;
    this.currentBalance = 0;
    this.peakBalance = 0;

    logger.info('DynamicRiskManager initialized', { config: this.config });
  }

  /**
   * Initialize risk manager with starting balance
   */
  initialize(initialBalance) {
    this.initialBalance = initialBalance;
    this.currentBalance = initialBalance;
    this.peakBalance = initialBalance;
    logger.info('Risk manager initialized', { initialBalance });
  }

  /**
   * Main risk assessment method
   */
  async assessRisk(signal, currentPortfolio = {}) {
    const riskAssessment = {
      allowTrade: true,
      positionSizeMultiplier: 1.0,
      reasonsToReject: [],
      riskLevel: 'normal',
      riskFactors: {}
    };

    try {
      // Check if trading is paused
      if (this.isTradingPaused()) {
        riskAssessment.allowTrade = false;
        riskAssessment.reasonsToReject.push(`Trading paused until ${this.pauseEndTime}`);
        return riskAssessment;
      }

      // Check consecutive losses
      const consecutiveLossRisk = this.assessConsecutiveLossRisk();
      riskAssessment.riskFactors.consecutiveLosses = consecutiveLossRisk;

      if (consecutiveLossRisk.shouldPause) {
        riskAssessment.allowTrade = false;
        riskAssessment.reasonsToReject.push(`${this.consecutiveLosses} consecutive losses - trading paused`);
        return riskAssessment;
      }

      riskAssessment.positionSizeMultiplier *= consecutiveLossRisk.multiplier;

      // Check drawdown risk
      const drawdownRisk = this.assessDrawdownRisk();
      riskAssessment.riskFactors.drawdown = drawdownRisk;

      if (drawdownRisk.critical) {
        riskAssessment.riskLevel = 'critical';
        riskAssessment.positionSizeMultiplier *= this.config.riskMultipliers.highDrawdown;
      }

      // Check market volatility
      const volatilityRisk = await this.assessVolatilityRisk(signal.symbol);
      riskAssessment.riskFactors.volatility = volatilityRisk;

      if (volatilityRisk.high) {
        riskAssessment.positionSizeMultiplier *= this.config.riskMultipliers.highVolatility;
        riskAssessment.reasonsToReject.push('High market volatility detected');
      }

      // Check news events (placeholder for now)
      const newsRisk = await this.assessNewsRisk(signal.symbol);
      riskAssessment.riskFactors.news = newsRisk;

      if (newsRisk.highImpact) {
        riskAssessment.allowTrade = false;
        riskAssessment.reasonsToReject.push('High-impact news event detected');
        return riskAssessment;
      }

      // Check correlation risk
      const correlationRisk = await this.assessCorrelationRisk(signal.symbol, currentPortfolio);
      riskAssessment.riskFactors.correlation = correlationRisk;

      if (correlationRisk.high) {
        riskAssessment.positionSizeMultiplier *= this.config.riskMultipliers.highCorrelation;
        riskAssessment.reasonsToReject.push('High correlation with existing positions');
      }

      // Set overall risk level
      if (riskAssessment.positionSizeMultiplier <= 0.5) {
        riskAssessment.riskLevel = 'high';
      } else if (riskAssessment.positionSizeMultiplier <= 0.7) {
        riskAssessment.riskLevel = 'medium';
      }

      logger.info('Risk assessment completed', {
        symbol: signal.symbol,
        riskLevel: riskAssessment.riskLevel,
        positionSizeMultiplier: riskAssessment.positionSizeMultiplier,
        reasonsToReject: riskAssessment.reasonsToReject
      });

      return riskAssessment;

    } catch (error) {
      logger.error('Error in risk assessment', { error: error.message, symbol: signal.symbol });

      // Conservative approach on error
      return {
        allowTrade: false,
        positionSizeMultiplier: 0,
        reasonsToReject: ['Risk assessment error - trading paused for safety'],
        riskLevel: 'critical',
        riskFactors: {}
      };
    }
  }
/**
   * Assess consecutive loss risk
   */
  assessConsecutiveLossRisk() {
    const risk = {
      count: this.consecutiveLosses,
      multiplier: 1.0,
      shouldPause: false,
      level: 'normal'
    };

    if (this.consecutiveLosses >= this.config.maxConsecutiveLosses) {
      risk.shouldPause = true;
      risk.multiplier = 0;
      risk.level = 'critical';
    } else if (this.consecutiveLosses >= 3) {
      risk.multiplier = this.config.riskMultipliers.consecutive3Losses;
      risk.level = 'high';
    }

    return risk;
  }

  /**
   * Assess drawdown risk
   */
  assessDrawdownRisk() {
    const drawdownPercent = (this.peakBalance - this.currentBalance) / this.peakBalance * 100;
    this.currentDrawdown = Math.max(0, drawdownPercent);

    const risk = {
      currentDrawdown: this.currentDrawdown,
      maxAllowed: this.config.maxDrawdownPercent,
      critical: this.currentDrawdown >= this.config.maxDrawdownPercent,
      level: 'normal'
    };

    if (this.currentDrawdown >= this.config.maxDrawdownPercent) {
      risk.level = 'critical';
    } else if (this.currentDrawdown >= this.config.maxDrawdownPercent * 0.7) {
      risk.level = 'high';
    } else if (this.currentDrawdown >= this.config.maxDrawdownPercent * 0.5) {
      risk.level = 'medium';
    }

    return risk;
  }

  /**
   * Calculate market volatility using ATR and price movements
   */
  async calculateMarketVolatility(symbol, candles) {
    try {
      if (!candles || candles.length < 20) {
        return { volatility: 0, level: 'unknown' };
      }

      // Calculate ATR for volatility measure
      const atr = calculateATR(candles, 14);
      const currentPrice = candles[candles.length - 1].close;
      const atrPercent = (atr / currentPrice) * 100;

      // Calculate price movement volatility
      const priceChanges = [];
      for (let i = 1; i < Math.min(candles.length, 20); i++) {
        const change = Math.abs((candles[i].close - candles[i-1].close) / candles[i-1].close);
        priceChanges.push(change);
      }

      const avgPriceChange = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
      const volatilityScore = Math.max(atrPercent / 100, avgPriceChange);

      let level = 'low';
      if (volatilityScore > this.config.volatilityThreshold * 1.5) {
        level = 'very_high';
      } else if (volatilityScore > this.config.volatilityThreshold) {
        level = 'high';
      } else if (volatilityScore > this.config.volatilityThreshold * 0.5) {
        level = 'medium';
      }

      return {
        volatility: volatilityScore,
        atrPercent,
        avgPriceChange,
        level,
        threshold: this.config.volatilityThreshold
      };

    } catch (error) {
      logger.error('Error calculating market volatility', { error: error.message, symbol });
      return { volatility: 0, level: 'unknown' };
    }
  }

  /**
   * Assess volatility risk for a symbol
   */
  async assessVolatilityRisk(symbol) {
    // This would typically fetch recent candle data
    // For now, we'll use a simplified approach
    const risk = {
      high: false,
      level: 'normal',
      volatility: 0
    };

    try {
      // In a real implementation, this would fetch candle data
      // For now, we'll simulate based on symbol characteristics
      const volatilityMap = {
        'BTCUSDT': 0.03,
        'ETHUSDT': 0.04,
        'ADAUSDT': 0.06,
        'DOGEUSDT': 0.08,
        'SOLUSDT': 0.07
      };

      const baseVolatility = volatilityMap[symbol] || 0.05;

      // Add some randomness to simulate market conditions
      const currentVolatility = baseVolatility * (0.8 + Math.random() * 0.4);

      risk.volatility = currentVolatility;
      risk.high = currentVolatility > this.config.volatilityThreshold;

      if (currentVolatility > this.config.volatilityThreshold * 1.5) {
        risk.level = 'very_high';
      } else if (currentVolatility > this.config.volatilityThreshold) {
        risk.level = 'high';
      } else if (currentVolatility > this.config.volatilityThreshold * 0.5) {
        risk.level = 'medium';
      } else {
        risk.level = 'low';
      }

    } catch (error) {
      logger.error('Error assessing volatility risk', { error: error.message, symbol });
    }

    return risk;
  }

  /**
   * Assess news event risk (placeholder implementation)
   */
  async assessNewsRisk(symbol) {
    // This would integrate with news APIs in a real implementation
    // For now, we'll return a basic structure
    return {
      highImpact: false,
      events: [],
      level: 'normal',
      nextEventTime: null
    };
  }

  /**
   * Calculate correlation risk with existing portfolio positions
   */
  async assessCorrelationRisk(symbol, currentPortfolio) {
    const risk = {
      high: false,
      maxCorrelation: 0,
      correlatedSymbols: [],
      level: 'normal'
    };

    try {
      if (!currentPortfolio || Object.keys(currentPortfolio).length === 0) {
        return risk;
      }

      // Simplified correlation calculation based on symbol categories
      const correlationMatrix = {
        'BTCUSDT': { 'ETHUSDT': 0.8, 'ADAUSDT': 0.6, 'SOLUSDT': 0.7 },
        'ETHUSDT': { 'BTCUSDT': 0.8, 'ADAUSDT': 0.7, 'SOLUSDT': 0.8 },
        'ADAUSDT': { 'BTCUSDT': 0.6, 'ETHUSDT': 0.7, 'SOLUSDT': 0.6 },
        'SOLUSDT': { 'BTCUSDT': 0.7, 'ETHUSDT': 0.8, 'ADAUSDT': 0.6 },
        'DOGEUSDT': { 'BTCUSDT': 0.5, 'ETHUSDT': 0.4 }
      };

      const symbolCorrelations = correlationMatrix[symbol] || {};

      for (const [portfolioSymbol, position] of Object.entries(currentPortfolio)) {
        if (position.size > 0) {
          const correlation = symbolCorrelations[portfolioSymbol] || 0;

          if (correlation > risk.maxCorrelation) {
            risk.maxCorrelation = correlation;
          }

          if (correlation > this.config.correlationThreshold) {
            risk.correlatedSymbols.push({
              symbol: portfolioSymbol,
              correlation,
              position: position.size
            });
          }
        }
      }

      risk.high = risk.maxCorrelation > this.config.correlationThreshold;

      if (risk.maxCorrelation > 0.9) {
        risk.level = 'very_high';
      } else if (risk.maxCorrelation > this.config.correlationThreshold) {
        risk.level = 'high';
      } else if (risk.maxCorrelation > 0.5) {
        risk.level = 'medium';
      }

    } catch (error) {
      logger.error('Error assessing correlation risk', { error: error.message, symbol });
    }

    return risk;
  }  /**

  * Update risk state after a trade result
   */
  async updateRiskState(tradeResult) {
    try {
      const { pnlPercent, pnlAmount, symbol, exitReason } = tradeResult;

      // Update balance tracking
      this.currentBalance += pnlAmount || 0;
      if (this.currentBalance > this.peakBalance) {
        this.peakBalance = this.currentBalance;
      }

      // Update consecutive losses
      if (pnlPercent > 0) {
        this.consecutiveLosses = 0;
        this.riskMultiplier = Math.min(1.0, this.riskMultiplier + 0.1);
      } else {
        this.consecutiveLosses++;
        this.riskMultiplier = Math.max(0.3, this.riskMultiplier - 0.1);
      }

      // Add to recent trades for analysis
      this.recentTrades.push({
        ...tradeResult,
        timestamp: new Date()
      });

      // Keep only last 100 trades
      if (this.recentTrades.length > 100) {
        this.recentTrades = this.recentTrades.slice(-100);
      }

      // Auto-pause trading if needed
      if (this.consecutiveLosses >= this.config.maxConsecutiveLosses) {
        this.pauseTrading('consecutive_losses');
      }

      // Check drawdown-based pause
      const currentDrawdown = (this.peakBalance - this.currentBalance) / this.peakBalance * 100;
      if (currentDrawdown >= this.config.maxDrawdownPercent) {
        this.pauseTrading('max_drawdown');
      }

      logger.info('Risk state updated', {
        symbol,
        pnlPercent,
        consecutiveLosses: this.consecutiveLosses,
        currentDrawdown: currentDrawdown.toFixed(2) + '%',
        riskMultiplier: this.riskMultiplier,
        tradingPaused: this.tradingPaused
      });

    } catch (error) {
      logger.error('Error updating risk state', { error: error.message, tradeResult });
    }
  }

  /**
   * Pause trading for specified reason
   */
  pauseTrading(reason) {
    this.tradingPaused = true;
    this.pauseEndTime = new Date(Date.now() + this.config.pauseDurationHours * 60 * 60 * 1000);

    logger.warn('Trading paused', {
      reason,
      pauseEndTime: this.pauseEndTime,
      consecutiveLosses: this.consecutiveLosses,
      currentDrawdown: this.currentDrawdown
    });

    // Auto-resume trading after pause duration
    setTimeout(() => {
      this.resumeTrading();
    }, this.config.pauseDurationHours * 60 * 60 * 1000);
  }

  /**
   * Resume trading
   */
  resumeTrading() {
    this.tradingPaused = false;
    this.pauseEndTime = null;
    this.consecutiveLosses = Math.max(0, this.consecutiveLosses - 2); // Reduce consecutive losses on resume

    logger.info('Trading resumed', {
      consecutiveLosses: this.consecutiveLosses,
      riskMultiplier: this.riskMultiplier
    });
  }

  /**
   * Check if trading is currently paused
   */
  isTradingPaused() {
    if (this.tradingPaused && this.pauseEndTime && new Date() > this.pauseEndTime) {
      this.resumeTrading();
    }
    return this.tradingPaused;
  }

  /**
   * Get current risk metrics
   */
  getRiskMetrics() {
    const currentDrawdown = this.peakBalance > 0 ?
      (this.peakBalance - this.currentBalance) / this.peakBalance * 100 : 0;

    return {
      consecutiveLosses: this.consecutiveLosses,
      currentDrawdown: currentDrawdown,
      riskMultiplier: this.riskMultiplier,
      tradingPaused: this.tradingPaused,
      pauseEndTime: this.pauseEndTime,
      currentBalance: this.currentBalance,
      peakBalance: this.peakBalance,
      totalReturn: this.initialBalance > 0 ?
        (this.currentBalance - this.initialBalance) / this.initialBalance * 100 : 0,
      recentTradesCount: this.recentTrades.length
    };
  }

  /**
   * Get risk summary for reporting
   */
  getRiskSummary() {
    const metrics = this.getRiskMetrics();
    const recentWinRate = this.calculateRecentWinRate();

    return {
      ...metrics,
      recentWinRate,
      riskLevel: this.getCurrentRiskLevel(),
      recommendations: this.generateRiskRecommendations()
    };
  }

  /**
   * Calculate recent win rate
   */
  calculateRecentWinRate(lookback = 20) {
    if (this.recentTrades.length === 0) return 0;

    const recentTrades = this.recentTrades.slice(-lookback);
    const wins = recentTrades.filter(trade => trade.pnlPercent > 0).length;

    return (wins / recentTrades.length) * 100;
  }

  /**
   * Get current overall risk level
   */
  getCurrentRiskLevel() {
    if (this.tradingPaused) return 'critical';
    if (this.consecutiveLosses >= 3 || this.currentDrawdown >= this.config.maxDrawdownPercent * 0.7) {
      return 'high';
    }
    if (this.consecutiveLosses >= 2 || this.currentDrawdown >= this.config.maxDrawdownPercent * 0.5) {
      return 'medium';
    }
    return 'normal';
  }

  /**
   * Generate risk management recommendations
   */
  generateRiskRecommendations() {
    const recommendations = [];

    if (this.consecutiveLosses >= 2) {
      recommendations.push({
        type: 'position_sizing',
        message: 'Consider reducing position sizes due to consecutive losses',
        priority: 'high'
      });
    }

    if (this.currentDrawdown >= this.config.maxDrawdownPercent * 0.5) {
      recommendations.push({
        type: 'drawdown_management',
        message: 'Approaching maximum drawdown limit - consider conservative approach',
        priority: 'high'
      });
    }

    const recentWinRate = this.calculateRecentWinRate();
    if (recentWinRate < 40) {
      recommendations.push({
        type: 'strategy_review',
        message: 'Low recent win rate - consider reviewing strategy parameters',
        priority: 'medium'
      });
    }

    return recommendations;
  }

  /**
   * Reset risk state (for testing or manual reset)
   */
  reset() {
    this.consecutiveLosses = 0;
    this.currentDrawdown = 0;
    this.riskMultiplier = 1.0;
    this.tradingPaused = false;
    this.pauseEndTime = null;
    this.recentTrades = [];
    this.currentBalance = this.initialBalance;
    this.peakBalance = this.initialBalance;

    logger.info('Risk manager reset');
  }
}

module.exports = DynamicRiskManager;