const logger = require('../logger');

/**
 * Seasonal Pattern Detector
 * Detects seasonal patterns in trading performance for market timing optimization
 */
class SeasonalPatternDetector {
  constructor(config = {}) {
    this.config = {
      // Minimum data requirements
      minYearsOfData: config.minYearsOfData || 1,
      minTradesPerPeriod: config.minTradesPerPeriod || 5,
      
      // Pattern detection thresholds
      significanceThreshold: config.significanceThreshold || 0.15, // 15% difference
      confidenceThreshold: config.confidenceThreshold || 0.7,
      
      // Seasonal periods to analyze
      seasonalPeriods: {
        monthly: true,
        quarterly: true,
        dayOfWeek: true,
        hourOfDay: true,
        ...config.seasonalPeriods
      },

      // Known market patterns (for validation)
      knownPatterns: {
        crypto: {
          // Crypto markets are 24/7, different patterns than traditional markets
          strongMonths: [10, 11, 12, 1], // Q4 and January often strong for crypto
          weakMonths: [6, 7, 8], // Summer months often weaker
          strongDays: [1, 2], // Monday/Tuesday often strong
          weakDays: [6, 0], // Saturday/Sunday often weaker
          strongHours: [8, 9, 10, 13, 14, 15, 16], // Peak trading hours UTC
          weakHours: [2, 3, 4, 5] // Low activity hours UTC
        },
        ...config.knownPatterns
      },

      ...config
    };

    this.patternCache = new Map();
    this.lastAnalysisTime = new Map();
  }

  /**
   * Detect all seasonal patterns in trading data
   */
  async detectAllPatterns(trades, symbols = []) {
    try {
      const patterns = {
        monthly: {},
        quarterly: {},
        dayOfWeek: {},
        hourOfDay: {},
        summary: {},
        recommendations: [],
        detectionDate: new Date()
      };

      // Group trades by symbol if symbols specified, otherwise analyze all together
      const tradeGroups = symbols.length > 0 
        ? this.groupTradesBySymbol(trades, symbols)
        : { 'ALL': trades };

      for (const [groupName, groupTrades] of Object.entries(tradeGroups)) {
        if (groupTrades.length < this.config.minTradesPerPeriod * 12) { // Need enough data
          continue;
        }

        // Detect monthly patterns
        if (this.config.seasonalPeriods.monthly) {
          patterns.monthly[groupName] = this.detectMonthlyPatterns(groupTrades);
        }

        // Detect quarterly patterns
        if (this.config.seasonalPeriods.quarterly) {
          patterns.quarterly[groupName] = this.detectQuarterlyPatterns(groupTrades);
        }

        // Detect day of week patterns
        if (this.config.seasonalPeriods.dayOfWeek) {
          patterns.dayOfWeek[groupName] = this.detectDayOfWeekPatterns(groupTrades);
        }

        // Detect hour of day patterns
        if (this.config.seasonalPeriods.hourOfDay) {
          patterns.hourOfDay[groupName] = this.detectHourOfDayPatterns(groupTrades);
        }
      }

      // Generate summary and recommendations
      patterns.summary = this.generatePatternSummary(patterns);
      patterns.recommendations = this.generateSeasonalRecommendations(patterns);

      return patterns;

    } catch (error) {
      logger.error('Error detecting seasonal patterns', { error: error.message });
      return {
        monthly: {},
        quarterly: {},
        dayOfWeek: {},
        hourOfDay: {},
        summary: { error: 'Pattern detection failed' },
        recommendations: [],
        detectionDate: new Date()
      };
    }
  }

  /**
   * Detect monthly seasonal patterns
   */
  detectMonthlyPatterns(trades) {
    const monthlyData = this.groupTradesByMonth(trades);
    const monthlyStats = {};
    const patterns = [];

    // Calculate statistics for each month
    for (let month = 1; month <= 12; month++) {
      const monthTrades = monthlyData[month] || [];
      if (monthTrades.length >= this.config.minTradesPerPeriod) {
        monthlyStats[month] = this.calculatePeriodStats(monthTrades);
      }
    }

    // Identify significant patterns
    const avgWinRate = this.calculateAverageWinRate(Object.values(monthlyStats));
    const avgPnL = this.calculateAveragePnL(Object.values(monthlyStats));

    for (const [month, stats] of Object.entries(monthlyStats)) {
      const winRateDiff = (stats.winRate - avgWinRate) / avgWinRate;
      const pnlDiff = (stats.avgPnL - avgPnL) / Math.abs(avgPnL);

      if (Math.abs(winRateDiff) > this.config.significanceThreshold ||
          Math.abs(pnlDiff) > this.config.significanceThreshold) {
        
        patterns.push({
          month: parseInt(month),
          monthName: this.getMonthName(parseInt(month)),
          type: winRateDiff > 0 || pnlDiff > 0 ? 'strong' : 'weak',
          winRate: stats.winRate,
          avgPnL: stats.avgPnL,
          trades: stats.totalTrades,
          winRateDiff: winRateDiff,
          pnlDiff: pnlDiff,
          significance: Math.max(Math.abs(winRateDiff), Math.abs(pnlDiff)),
          confidence: this.calculatePatternConfidence(stats.totalTrades, Math.abs(winRateDiff))
        });
      }
    }

    return {
      monthlyStats,
      patterns: patterns.sort((a, b) => b.significance - a.significance),
      strongMonths: patterns.filter(p => p.type === 'strong').map(p => p.month),
      weakMonths: patterns.filter(p => p.type === 'weak').map(p => p.month),
      avgWinRate,
      avgPnL
    };
  }

  /**
   * Detect quarterly seasonal patterns
   */
  detectQuarterlyPatterns(trades) {
    const quarterlyData = this.groupTradesByQuarter(trades);
    const quarterlyStats = {};
    const patterns = [];

    // Calculate statistics for each quarter
    for (let quarter = 1; quarter <= 4; quarter++) {
      const quarterTrades = quarterlyData[quarter] || [];
      if (quarterTrades.length >= this.config.minTradesPerPeriod * 3) { // More trades needed for quarters
        quarterlyStats[quarter] = this.calculatePeriodStats(quarterTrades);
      }
    }

    // Identify significant patterns
    const avgWinRate = this.calculateAverageWinRate(Object.values(quarterlyStats));
    const avgPnL = this.calculateAveragePnL(Object.values(quarterlyStats));

    for (const [quarter, stats] of Object.entries(quarterlyStats)) {
      const winRateDiff = (stats.winRate - avgWinRate) / avgWinRate;
      const pnlDiff = (stats.avgPnL - avgPnL) / Math.abs(avgPnL);

      if (Math.abs(winRateDiff) > this.config.significenceThreshold ||
          Math.abs(pnlDiff) > this.config.significanceThreshold) {
        
        patterns.push({
          quarter: parseInt(quarter),
          quarterName: `Q${quarter}`,
          type: winRateDiff > 0 || pnlDiff > 0 ? 'strong' : 'weak',
          winRate: stats.winRate,
          avgPnL: stats.avgPnL,
          trades: stats.totalTrades,
          winRateDiff: winRateDiff,
          pnlDiff: pnlDiff,
          significance: Math.max(Math.abs(winRateDiff), Math.abs(pnlDiff)),
          confidence: this.calculatePatternConfidence(stats.totalTrades, Math.abs(winRateDiff))
        });
      }
    }

    return {
      quarterlyStats,
      patterns: patterns.sort((a, b) => b.significance - a.significance),
      strongQuarters: patterns.filter(p => p.type === 'strong').map(p => p.quarter),
      weakQuarters: patterns.filter(p => p.type === 'weak').map(p => p.quarter),
      avgWinRate,
      avgPnL
    };
  }

  /**
   * Detect day of week patterns
   */
  detectDayOfWeekPatterns(trades) {
    const dailyData = this.groupTradesByDayOfWeek(trades);
    const dailyStats = {};
    const patterns = [];

    // Calculate statistics for each day
    for (let day = 0; day <= 6; day++) { // 0 = Sunday, 6 = Saturday
      const dayTrades = dailyData[day] || [];
      if (dayTrades.length >= this.config.minTradesPerPeriod) {
        dailyStats[day] = this.calculatePeriodStats(dayTrades);
      }
    }

    // Identify significant patterns
    const avgWinRate = this.calculateAverageWinRate(Object.values(dailyStats));
    const avgPnL = this.calculateAveragePnL(Object.values(dailyStats));

    for (const [day, stats] of Object.entries(dailyStats)) {
      const winRateDiff = (stats.winRate - avgWinRate) / avgWinRate;
      const pnlDiff = (stats.avgPnL - avgPnL) / Math.abs(avgPnL);

      if (Math.abs(winRateDiff) > this.config.significanceThreshold ||
          Math.abs(pnlDiff) > this.config.significanceThreshold) {
        
        patterns.push({
          day: parseInt(day),
          dayName: this.getDayName(parseInt(day)),
          type: winRateDiff > 0 || pnlDiff > 0 ? 'strong' : 'weak',
          winRate: stats.winRate,
          avgPnL: stats.avgPnL,
          trades: stats.totalTrades,
          winRateDiff: winRateDiff,
          pnlDiff: pnlDiff,
          significance: Math.max(Math.abs(winRateDiff), Math.abs(pnlDiff)),
          confidence: this.calculatePatternConfidence(stats.totalTrades, Math.abs(winRateDiff))
        });
      }
    }

    return {
      dailyStats,
      patterns: patterns.sort((a, b) => b.significance - a.significance),
      strongDays: patterns.filter(p => p.type === 'strong').map(p => p.day),
      weakDays: patterns.filter(p => p.type === 'weak').map(p => p.day),
      avgWinRate,
      avgPnL
    };
  }

  /**
   * Detect hour of day patterns
   */
  detectHourOfDayPatterns(trades) {
    const hourlyData = this.groupTradesByHour(trades);
    const hourlyStats = {};
    const patterns = [];

    // Calculate statistics for each hour
    for (let hour = 0; hour <= 23; hour++) {
      const hourTrades = hourlyData[hour] || [];
      if (hourTrades.length >= this.config.minTradesPerPeriod) {
        hourlyStats[hour] = this.calculatePeriodStats(hourTrades);
      }
    }

    // Identify significant patterns
    const avgWinRate = this.calculateAverageWinRate(Object.values(hourlyStats));
    const avgPnL = this.calculateAveragePnL(Object.values(hourlyStats));

    for (const [hour, stats] of Object.entries(hourlyStats)) {
      const winRateDiff = (stats.winRate - avgWinRate) / avgWinRate;
      const pnlDiff = (stats.avgPnL - avgPnL) / Math.abs(avgPnL);

      if (Math.abs(winRateDiff) > this.config.significanceThreshold ||
          Math.abs(pnlDiff) > this.config.significanceThreshold) {
        
        patterns.push({
          hour: parseInt(hour),
          hourRange: `${hour}:00-${hour}:59`,
          type: winRateDiff > 0 || pnlDiff > 0 ? 'strong' : 'weak',
          winRate: stats.winRate,
          avgPnL: stats.avgPnL,
          trades: stats.totalTrades,
          winRateDiff: winRateDiff,
          pnlDiff: pnlDiff,
          significance: Math.max(Math.abs(winRateDiff), Math.abs(pnlDiff)),
          confidence: this.calculatePatternConfidence(stats.totalTrades, Math.abs(winRateDiff))
        });
      }
    }

    return {
      hourlyStats,
      patterns: patterns.sort((a, b) => b.significance - a.significance),
      strongHours: patterns.filter(p => p.type === 'strong').map(p => p.hour),
      weakHours: patterns.filter(p => p.type === 'weak').map(p => p.hour),
      avgWinRate,
      avgPnL
    };
  }

  /**
   * Group trades by symbol
   */
  groupTradesBySymbol(trades, symbols) {
    const groups = {};
    symbols.forEach(symbol => groups[symbol] = []);
    
    trades.forEach(trade => {
      if (symbols.includes(trade.symbol)) {
        groups[trade.symbol].push(trade);
      }
    });

    return groups;
  }

  /**
   * Group trades by month
   */
  groupTradesByMonth(trades) {
    const groups = {};
    for (let i = 1; i <= 12; i++) groups[i] = [];

    trades.forEach(trade => {
      const date = new Date(trade.createdAt || trade.timestamp);
      const month = date.getMonth() + 1; // getMonth() returns 0-11
      groups[month].push(trade);
    });

    return groups;
  }

  /**
   * Group trades by quarter
   */
  groupTradesByQuarter(trades) {
    const groups = { 1: [], 2: [], 3: [], 4: [] };

    trades.forEach(trade => {
      const date = new Date(trade.createdAt || trade.timestamp);
      const month = date.getMonth() + 1;
      const quarter = Math.ceil(month / 3);
      groups[quarter].push(trade);
    });

    return groups;
  }

  /**
   * Group trades by day of week
   */
  groupTradesByDayOfWeek(trades) {
    const groups = {};
    for (let i = 0; i <= 6; i++) groups[i] = [];

    trades.forEach(trade => {
      const date = new Date(trade.createdAt || trade.timestamp);
      const day = date.getDay(); // 0 = Sunday, 6 = Saturday
      groups[day].push(trade);
    });

    return groups;
  }

  /**
   * Group trades by hour of day
   */
  groupTradesByHour(trades) {
    const groups = {};
    for (let i = 0; i <= 23; i++) groups[i] = [];

    trades.forEach(trade => {
      const date = new Date(trade.createdAt || trade.timestamp);
      const hour = date.getUTCHours(); // Use UTC for consistency
      groups[hour].push(trade);
    });

    return groups;
  }

  /**
   * Calculate statistics for a period
   */
  calculatePeriodStats(trades) {
    if (trades.length === 0) {
      return { totalTrades: 0, winRate: 0, avgPnL: 0, totalPnL: 0 };
    }

    const winningTrades = trades.filter(t => (t.pnlPercent || 0) > 0);
    const winRate = (winningTrades.length / trades.length) * 100;
    const totalPnL = trades.reduce((sum, t) => sum + (t.pnlPercent || 0), 0);
    const avgPnL = totalPnL / trades.length;

    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: trades.length - winningTrades.length,
      winRate: Math.round(winRate * 100) / 100,
      avgPnL: Math.round(avgPnL * 100) / 100,
      totalPnL: Math.round(totalPnL * 100) / 100
    };
  }

  /**
   * Calculate average win rate from stats array
   */
  calculateAverageWinRate(statsArray) {
    if (statsArray.length === 0) return 0;
    const totalWinRate = statsArray.reduce((sum, stats) => sum + stats.winRate, 0);
    return totalWinRate / statsArray.length;
  }

  /**
   * Calculate average PnL from stats array
   */
  calculateAveragePnL(statsArray) {
    if (statsArray.length === 0) return 0;
    const totalPnL = statsArray.reduce((sum, stats) => sum + stats.avgPnL, 0);
    return totalPnL / statsArray.length;
  }

  /**
   * Calculate pattern confidence based on sample size and effect size
   */
  calculatePatternConfidence(sampleSize, effectSize) {
    // Simple confidence calculation based on sample size and effect size
    const sampleConfidence = Math.min(sampleSize / 50, 1); // Max confidence at 50+ trades
    const effectConfidence = Math.min(effectSize / 0.3, 1); // Max confidence at 30% effect
    return Math.round((sampleConfidence * effectConfidence) * 100) / 100;
  }

  /**
   * Get month name
   */
  getMonthName(month) {
    const months = [
      '', 'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month];
  }

  /**
   * Get day name
   */
  getDayName(day) {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[day];
  }

  /**
   * Generate pattern summary
   */
  generatePatternSummary(patterns) {
    const summary = {
      totalPatternsDetected: 0,
      highConfidencePatterns: 0,
      strongestPattern: null,
      patternTypes: {
        monthly: 0,
        quarterly: 0,
        dayOfWeek: 0,
        hourOfDay: 0
      }
    };

    // Count patterns across all categories
    Object.entries(patterns).forEach(([category, categoryData]) => {
      if (typeof categoryData === 'object' && categoryData !== null) {
        Object.values(categoryData).forEach(groupData => {
          if (groupData.patterns) {
            summary.totalPatternsDetected += groupData.patterns.length;
            summary.patternTypes[category] += groupData.patterns.length;
            
            const highConfidence = groupData.patterns.filter(p => p.confidence >= this.config.confidenceThreshold);
            summary.highConfidencePatterns += highConfidence.length;

            // Find strongest pattern
            const strongest = groupData.patterns[0]; // Already sorted by significance
            if (strongest && (!summary.strongestPattern || strongest.significance > summary.strongestPattern.significance)) {
              summary.strongestPattern = { ...strongest, category };
            }
          }
        });
      }
    });

    return summary;
  }

  /**
   * Generate seasonal recommendations
   */
  generateSeasonalRecommendations(patterns) {
    const recommendations = [];

    // Monthly recommendations
    Object.entries(patterns.monthly).forEach(([group, data]) => {
      if (data.strongMonths.length > 0) {
        recommendations.push({
          type: 'monthly_allocation',
          group: group,
          message: `Increase allocation during strong months: ${data.strongMonths.map(m => this.getMonthName(m)).join(', ')}`,
          priority: 'medium',
          months: data.strongMonths,
          expectedImprovement: 'Higher win rate and returns during these periods'
        });
      }

      if (data.weakMonths.length > 0) {
        recommendations.push({
          type: 'monthly_reduction',
          group: group,
          message: `Reduce allocation during weak months: ${data.weakMonths.map(m => this.getMonthName(m)).join(', ')}`,
          priority: 'medium',
          months: data.weakMonths,
          expectedImprovement: 'Avoid periods of poor performance'
        });
      }
    });

    // Day of week recommendations
    Object.entries(patterns.dayOfWeek).forEach(([group, data]) => {
      if (data.strongDays.length > 0) {
        recommendations.push({
          type: 'daily_timing',
          group: group,
          message: `Focus trading on strong days: ${data.strongDays.map(d => this.getDayName(d)).join(', ')}`,
          priority: 'high',
          days: data.strongDays,
          expectedImprovement: 'Better performance on these days of the week'
        });
      }
    });

    // Hour of day recommendations
    Object.entries(patterns.hourOfDay).forEach(([group, data]) => {
      if (data.strongHours.length > 0) {
        recommendations.push({
          type: 'hourly_timing',
          group: group,
          message: `Optimize trading during peak hours: ${data.strongHours.join(', ')} UTC`,
          priority: 'high',
          hours: data.strongHours,
          expectedImprovement: 'Higher success rate during optimal trading hours'
        });
      }
    });

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.patternCache.clear();
    this.lastAnalysisTime.clear();
    logger.info('Seasonal pattern detector cache cleared');
  }
}

module.exports = SeasonalPatternDetector;
