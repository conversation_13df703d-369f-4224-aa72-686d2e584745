const logger = require('../logger');
const PerformanceMetricsCalculator = require('./performanceMetricsCalculator');

/**
 * Timeframe Performance Analyzer
 * Analyzes performance across different timeframes and provides optimal timeframe recommendations
 */
class TimeframePerformanceAnalyzer {
  constructor(config = {}) {
    this.config = {
      // Supported timeframes
      timeframes: config.timeframes || ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
      
      // Analysis periods
      analysisPeriods: config.analysisPeriods || [7, 30, 90],
      
      // Performance weights for scoring
      performanceWeights: {
        winRate: 0.25,
        profitFactor: 0.25,
        sharpeRatio: 0.20,
        maxDrawdown: 0.15,
        consistency: 0.15,
        ...config.performanceWeights
      },

      // Timeframe characteristics
      timeframeCharacteristics: {
        '1m': { volatility: 'very_high', signals_per_day: 100, risk_level: 'high' },
        '5m': { volatility: 'high', signals_per_day: 50, risk_level: 'high' },
        '15m': { volatility: 'medium_high', signals_per_day: 20, risk_level: 'medium' },
        '30m': { volatility: 'medium', signals_per_day: 10, risk_level: 'medium' },
        '1h': { volatility: 'medium', signals_per_day: 5, risk_level: 'medium' },
        '4h': { volatility: 'low', signals_per_day: 2, risk_level: 'low' },
        '1d': { volatility: 'very_low', signals_per_day: 1, risk_level: 'low' },
        ...config.timeframeCharacteristics
      },

      // Minimum trades required for analysis
      minTradesForAnalysis: config.minTradesForAnalysis || 20,

      ...config
    };

    this.metricsCalculator = new PerformanceMetricsCalculator();
    this.analysisCache = new Map();
    this.lastAnalysisTime = new Map();
  }

  /**
   * Analyze performance across all timeframes
   */
  async analyzeAllTimeframes(trades) {
    try {
      const timeframeTrades = this.groupTradesByTimeframe(trades);
      const analyses = new Map();

      for (const [timeframe, timeframeTradeList] of timeframeTrades.entries()) {
        if (timeframeTradeList.length >= this.config.minTradesForAnalysis) {
          const analysis = await this.analyzeTimeframe(timeframe, timeframeTradeList);
          analyses.set(timeframe, analysis);
        }
      }

      // Generate timeframe recommendations
      const recommendations = this.generateTimeframeRecommendations(analyses);

      // Calculate optimal timeframe mix
      const optimalMix = this.calculateOptimalTimeframeMix(analyses);

      return {
        timeframeAnalyses: Object.fromEntries(analyses),
        recommendations,
        optimalMix,
        summary: this.generateAnalysisSummary(analyses),
        analysisDate: new Date()
      };

    } catch (error) {
      logger.error('Error analyzing all timeframes', { error: error.message });
      return {
        timeframeAnalyses: {},
        recommendations: [],
        optimalMix: {},
        summary: {},
        analysisDate: new Date()
      };
    }
  }

  /**
   * Analyze performance for a specific timeframe
   */
  async analyzeTimeframe(timeframe, trades) {
    try {
      // Check cache
      const cacheKey = `${timeframe}_${trades.length}`;
      const cached = this.analysisCache.get(cacheKey);
      const lastAnalysis = this.lastAnalysisTime.get(timeframe);
      
      if (cached && lastAnalysis && (Date.now() - lastAnalysis) < 30 * 60 * 1000) { // 30 min cache
        return cached;
      }

      const analysis = {
        timeframe,
        totalTrades: trades.length,
        characteristics: this.config.timeframeCharacteristics[timeframe] || {},
        periodAnalyses: {},
        overallScore: 0,
        ranking: 0,
        strengths: [],
        weaknesses: [],
        recommendations: []
      };

      // Analyze different periods
      for (const days of this.config.analysisPeriods) {
        const periodTrades = this.getTradesInPeriod(trades, days);
        if (periodTrades.length >= Math.min(this.config.minTradesForAnalysis, 10)) {
          const metrics = this.metricsCalculator.calculateComprehensiveMetrics(periodTrades);
          const score = this.calculateTimeframeScore(metrics, timeframe);
          
          analysis.periodAnalyses[`${days}d`] = {
            trades: periodTrades.length,
            metrics,
            score,
            tradesPerDay: periodTrades.length / days
          };
        }
      }

      // Calculate overall score
      analysis.overallScore = this.calculateOverallScore(analysis.periodAnalyses);

      // Identify strengths and weaknesses
      analysis.strengths = this.identifyTimeframeStrengths(analysis);
      analysis.weaknesses = this.identifyTimeframeWeaknesses(analysis);

      // Generate specific recommendations
      analysis.recommendations = this.generateTimeframeSpecificRecommendations(analysis);

      // Cache the result
      this.analysisCache.set(cacheKey, analysis);
      this.lastAnalysisTime.set(timeframe, Date.now());

      return analysis;

    } catch (error) {
      logger.error('Error analyzing timeframe', { error: error.message, timeframe });
      return {
        timeframe,
        totalTrades: trades.length,
        characteristics: {},
        periodAnalyses: {},
        overallScore: 0,
        ranking: 0,
        strengths: [],
        weaknesses: ['Analysis error'],
        recommendations: ['Manual review required due to analysis error']
      };
    }
  }

  /**
   * Group trades by timeframe
   */
  groupTradesByTimeframe(trades) {
    const timeframeTrades = new Map();
    
    for (const trade of trades) {
      const timeframe = trade.timeframe || '1h'; // Default to 1h if not specified
      if (!timeframeTrades.has(timeframe)) {
        timeframeTrades.set(timeframe, []);
      }
      timeframeTrades.get(timeframe).push(trade);
    }

    return timeframeTrades;
  }

  /**
   * Get trades within a specific period
   */
  getTradesInPeriod(trades, days) {
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return trades.filter(trade => {
      const tradeDate = trade.createdAt || trade.timestamp || new Date();
      return new Date(tradeDate) >= cutoffDate;
    });
  }

  /**
   * Calculate timeframe-specific score
   */
  calculateTimeframeScore(metrics, timeframe) {
    const weights = this.config.performanceWeights;
    const characteristics = this.config.timeframeCharacteristics[timeframe] || {};
    
    let score = 0;

    // Base performance score
    score += (metrics.winRate / 100) * weights.winRate * 100;
    score += Math.min(metrics.profitFactor / 3, 1) * weights.profitFactor * 100;
    score += Math.min(Math.max(metrics.sharpeRatio / 2, 0), 1) * weights.sharpeRatio * 100;
    score += (1 - Math.min(metrics.maxDrawdown / 25, 1)) * weights.maxDrawdown * 100;
    score += (metrics.consistencyRatio / 100) * weights.consistency * 100;

    // Adjust for timeframe characteristics
    if (characteristics.risk_level === 'high' && metrics.maxDrawdown > 15) {
      score *= 0.9; // Penalty for high-risk timeframes with high drawdown
    }
    
    if (characteristics.risk_level === 'low' && metrics.sharpeRatio > 1.0) {
      score *= 1.1; // Bonus for low-risk timeframes with good risk-adjusted returns
    }

    return Math.round(score * 100) / 100;
  }

  /**
   * Calculate overall score from period analyses
   */
  calculateOverallScore(periodAnalyses) {
    const periods = Object.values(periodAnalyses);
    if (periods.length === 0) return 0;

    // Weight recent periods more heavily
    let weightedScore = 0;
    let totalWeight = 0;
    
    periods.forEach((period, index) => {
      const weight = Math.pow(1.5, index); // More recent periods get higher weight
      weightedScore += period.score * weight;
      totalWeight += weight;
    });

    return Math.round((weightedScore / totalWeight) * 100) / 100;
  }

  /**
   * Generate timeframe recommendations
   */
  generateTimeframeRecommendations(analyses) {
    const recommendations = [];
    const sortedTimeframes = Array.from(analyses.entries())
      .sort(([,a], [,b]) => b.overallScore - a.overallScore);

    // Update rankings
    sortedTimeframes.forEach(([timeframe, analysis], index) => {
      analysis.ranking = index + 1;
    });

    // Top performer recommendations
    if (sortedTimeframes.length > 0) {
      const [topTimeframe, topAnalysis] = sortedTimeframes[0];
      recommendations.push({
        type: 'top_performer',
        timeframe: topTimeframe,
        message: `${topTimeframe} is the top performing timeframe with score ${topAnalysis.overallScore}`,
        priority: 'high',
        action: 'increase_allocation'
      });
    }

    // Poor performer recommendations
    const poorPerformers = sortedTimeframes.filter(([,analysis]) => analysis.overallScore < 40);
    poorPerformers.forEach(([timeframe, analysis]) => {
      recommendations.push({
        type: 'poor_performer',
        timeframe: timeframe,
        message: `${timeframe} showing poor performance (score: ${analysis.overallScore})`,
        priority: 'medium',
        action: 'reduce_allocation_or_review'
      });
    });

    // Risk-based recommendations
    analyses.forEach((analysis, timeframe) => {
      const characteristics = analysis.characteristics;
      const latestPeriod = Object.values(analysis.periodAnalyses)[0];
      
      if (latestPeriod && characteristics.risk_level === 'high' && latestPeriod.metrics.maxDrawdown > 20) {
        recommendations.push({
          type: 'risk_warning',
          timeframe: timeframe,
          message: `High risk timeframe ${timeframe} showing excessive drawdown (${latestPeriod.metrics.maxDrawdown.toFixed(1)}%)`,
          priority: 'high',
          action: 'implement_stricter_risk_management'
        });
      }
    });

    // Diversification recommendations
    if (sortedTimeframes.length > 1) {
      const topScores = sortedTimeframes.slice(0, 3).map(([,analysis]) => analysis.overallScore);
      const scoreRange = Math.max(...topScores) - Math.min(...topScores);
      
      if (scoreRange < 20) {
        recommendations.push({
          type: 'diversification',
          message: 'Multiple timeframes showing similar performance - consider diversified approach',
          priority: 'medium',
          action: 'diversify_timeframe_allocation'
        });
      }
    }

    return recommendations;
  }

  /**
   * Calculate optimal timeframe mix
   */
  calculateOptimalTimeframeMix(analyses) {
    const timeframes = Array.from(analyses.entries())
      .filter(([,analysis]) => analysis.overallScore > 30) // Only include decent performers
      .sort(([,a], [,b]) => b.overallScore - a.overallScore);

    if (timeframes.length === 0) {
      return { message: 'No timeframes meet minimum performance criteria' };
    }

    const totalScore = timeframes.reduce((sum, [,analysis]) => sum + analysis.overallScore, 0);
    const mix = {};
    let allocatedPercentage = 0;

    timeframes.forEach(([timeframe, analysis], index) => {
      let allocation = 0;
      
      if (index === 0) {
        // Top performer gets 40-60% based on score dominance
        const scoreDominance = analysis.overallScore / totalScore;
        allocation = Math.min(60, Math.max(40, scoreDominance * 100));
      } else if (index === 1 && analysis.overallScore > 50) {
        // Second best gets 20-30%
        allocation = Math.min(30, Math.max(20, (analysis.overallScore / totalScore) * 100));
      } else if (analysis.overallScore > 40) {
        // Others get smaller allocations
        allocation = Math.min(15, Math.max(5, (analysis.overallScore / totalScore) * 100));
      }

      // Ensure we don't over-allocate
      if (allocatedPercentage + allocation > 100) {
        allocation = Math.max(0, 100 - allocatedPercentage);
      }

      if (allocation > 0) {
        mix[timeframe] = {
          allocation: Math.round(allocation),
          score: analysis.overallScore,
          ranking: analysis.ranking,
          rationale: this.getTimeframeMixRationale(analysis, allocation)
        };
        allocatedPercentage += allocation;
      }
    });

    return mix;
  }

  /**
   * Get rationale for timeframe mix allocation
   */
  getTimeframeMixRationale(analysis, allocation) {
    const score = analysis.overallScore;
    const characteristics = analysis.characteristics;
    
    if (allocation >= 40) {
      return `Primary timeframe due to excellent performance (score: ${score}) and ${characteristics.risk_level} risk profile`;
    } else if (allocation >= 20) {
      return `Secondary timeframe with strong performance (score: ${score}) for diversification`;
    } else {
      return `Supporting timeframe with acceptable performance (score: ${score}) for portfolio balance`;
    }
  }

  /**
   * Identify timeframe strengths
   */
  identifyTimeframeStrengths(analysis) {
    const strengths = [];
    const periods = Object.values(analysis.periodAnalyses);
    
    if (periods.length === 0) return strengths;

    const latestMetrics = periods[0].metrics; // Most recent period
    const characteristics = analysis.characteristics;

    if (latestMetrics.winRate >= 60) strengths.push('High win rate');
    if (latestMetrics.profitFactor >= 1.8) strengths.push('Strong profit factor');
    if (latestMetrics.sharpeRatio >= 1.0) strengths.push('Good risk-adjusted returns');
    if (latestMetrics.maxDrawdown <= 10) strengths.push('Low drawdown');
    if (latestMetrics.consistencyRatio >= 55) strengths.push('Consistent performance');
    
    if (characteristics.risk_level === 'low') strengths.push('Low risk profile');
    if (periods[0].tradesPerDay >= 2) strengths.push('Good signal frequency');

    return strengths;
  }

  /**
   * Identify timeframe weaknesses
   */
  identifyTimeframeWeaknesses(analysis) {
    const weaknesses = [];
    const periods = Object.values(analysis.periodAnalyses);
    
    if (periods.length === 0) return ['Insufficient data'];

    const latestMetrics = periods[0].metrics;
    const characteristics = analysis.characteristics;

    if (latestMetrics.winRate < 45) weaknesses.push('Low win rate');
    if (latestMetrics.profitFactor < 1.2) weaknesses.push('Poor profit factor');
    if (latestMetrics.sharpeRatio < 0.3) weaknesses.push('Poor risk-adjusted returns');
    if (latestMetrics.maxDrawdown > 20) weaknesses.push('High drawdown');
    if (latestMetrics.consistencyRatio < 40) weaknesses.push('Inconsistent performance');
    
    if (characteristics.risk_level === 'high') weaknesses.push('High risk profile');
    if (periods[0].tradesPerDay < 0.5) weaknesses.push('Low signal frequency');

    return weaknesses;
  }

  /**
   * Generate timeframe-specific recommendations
   */
  generateTimeframeSpecificRecommendations(analysis) {
    const recommendations = [];
    const periods = Object.values(analysis.periodAnalyses);
    
    if (periods.length === 0) {
      return ['Insufficient data for specific recommendations'];
    }

    const latestMetrics = periods[0].metrics;
    const characteristics = analysis.characteristics;

    // Performance-based recommendations
    if (latestMetrics.winRate < 50 && characteristics.signals_per_day > 10) {
      recommendations.push('Consider tightening entry criteria for high-frequency timeframe');
    }

    if (latestMetrics.maxDrawdown > 15 && characteristics.risk_level === 'high') {
      recommendations.push('Implement position sizing adjustments for high-risk timeframe');
    }

    if (latestMetrics.profitFactor < 1.3) {
      recommendations.push('Review exit strategy to improve risk-reward ratio');
    }

    // Timeframe-specific recommendations
    if (analysis.timeframe.includes('m') && latestMetrics.sharpeRatio < 0.5) {
      recommendations.push('Consider reducing noise in short timeframe signals');
    }

    if (analysis.timeframe.includes('h') || analysis.timeframe.includes('d')) {
      if (periods[0].tradesPerDay < 1) {
        recommendations.push('Consider optimizing signal generation for longer timeframes');
      }
    }

    return recommendations.length > 0 ? recommendations : ['Performance within acceptable parameters'];
  }

  /**
   * Generate analysis summary
   */
  generateAnalysisSummary(analyses) {
    const timeframes = Array.from(analyses.values());
    const totalTimeframes = timeframes.length;
    
    if (totalTimeframes === 0) {
      return { totalTimeframes: 0, message: 'No timeframes analyzed' };
    }

    const avgScore = timeframes.reduce((sum, tf) => sum + tf.overallScore, 0) / totalTimeframes;
    const topPerformer = timeframes.reduce((best, current) => 
      current.overallScore > best.overallScore ? current : best
    );
    
    const scoreDistribution = {
      excellent: timeframes.filter(tf => tf.overallScore >= 80).length,
      good: timeframes.filter(tf => tf.overallScore >= 60 && tf.overallScore < 80).length,
      average: timeframes.filter(tf => tf.overallScore >= 40 && tf.overallScore < 60).length,
      poor: timeframes.filter(tf => tf.overallScore < 40).length
    };

    return {
      totalTimeframes,
      averageScore: Math.round(avgScore * 100) / 100,
      topPerformer: {
        timeframe: topPerformer.timeframe,
        score: topPerformer.overallScore
      },
      scoreDistribution,
      excellentPercentage: Math.round((scoreDistribution.excellent / totalTimeframes) * 100),
      needsImprovementPercentage: Math.round((scoreDistribution.poor / totalTimeframes) * 100)
    };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.analysisCache.clear();
    this.lastAnalysisTime.clear();
    logger.info('Timeframe performance analyzer cache cleared');
  }
}

module.exports = TimeframePerformanceAnalyzer;
