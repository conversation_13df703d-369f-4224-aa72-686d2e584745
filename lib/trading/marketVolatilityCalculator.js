const { calculateATR, calculateSMA } = require('./indicators');
const logger = require('../logger');

/**
 * Market Volatility Calculator
 * Uses ATR and price movements to assess market volatility
 */
class MarketVolatilityCalculator {
  constructor(config = {}) {
    this.config = {
      atrPeriod: config.atrPeriod || 14,
      priceChangePeriod: config.priceChangePeriod || 20,
      volatilityThresholds: {
        low: 0.02,      // 2%
        medium: 0.035,  // 3.5%
        high: 0.05,     // 5%
        veryHigh: 0.075 // 7.5%
      },
      ...config
    };
  }

  /**
   * Calculate comprehensive volatility metrics
   */
  calculateVolatility(candles) {
    try {
      if (!candles || candles.length < Math.max(this.config.atrPeriod, this.config.priceChangePeriod)) {
        return {
          atrVolatility: 0,
          priceChangeVolatility: 0,
          combinedVolatility: 0,
          level: 'unknown',
          confidence: 0
        };
      }

      // Calculate ATR-based volatility
      const atrVolatility = this.calculateATRVolatility(candles);

      // Calculate price change volatility
      const priceChangeVolatility = this.calculatePriceChangeVolatility(candles);

      // Calculate range volatility
      const rangeVolatility = this.calculateRangeVolatility(candles);

      // Calculate gap volatility
      const gapVolatility = this.calculateGapVolatility(candles);

      // Combine volatilities with weights
      const combinedVolatility = this.combineVolatilities({
        atr: atrVolatility,
        priceChange: priceChangeVolatility,
        range: rangeVolatility,
        gap: gapVolatility
      });

      // Determine volatility level
      const level = this.determineVolatilityLevel(combinedVolatility.value);

      return {
        atrVolatility: atrVolatility.value,
        priceChangeVolatility: priceChangeVolatility.value,
        rangeVolatility: rangeVolatility.value,
        gapVolatility: gapVolatility.value,
        combinedVolatility: combinedVolatility.value,
        level,
        confidence: combinedVolatility.confidence,
        components: {
          atr: atrVolatility,
          priceChange: priceChangeVolatility,
          range: rangeVolatility,
          gap: gapVolatility
        }
      };

    } catch (error) {
      logger.error('Error calculating volatility', { error: error.message });
      return {
        atrVolatility: 0,
        priceChangeVolatility: 0,
        combinedVolatility: 0,
        level: 'unknown',
        confidence: 0
      };
    }
  }

  /**
   * Calculate ATR-based volatility
   */
  calculateATRVolatility(candles) {
    try {
      const atr = calculateATR(candles, this.config.atrPeriod);
      const currentPrice = candles[candles.length - 1].close;
      const atrPercent = (atr / currentPrice) * 100;

      return {
        value: atrPercent / 100, // Convert to decimal
        raw: atr,
        percent: atrPercent,
        confidence: 0.8 // ATR is generally reliable
      };
    } catch (error) {
      logger.error('Error calculating ATR volatility', { error: error.message });
      return { value: 0, raw: 0, percent: 0, confidence: 0 };
    }
  }

  /**
   * Calculate price change volatility (standard deviation of returns)
   */
  calculatePriceChangeVolatility(candles) {
    try {
      const returns = [];
      const period = Math.min(this.config.priceChangePeriod, candles.length - 1);

      for (let i = candles.length - period; i < candles.length; i++) {
        const currentPrice = candles[i].close;
        const previousPrice = candles[i - 1].close;
        const return_ = (currentPrice - previousPrice) / previousPrice;
        returns.push(return_);
      }

      // Calculate standard deviation of returns
      const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
      const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
      const stdDev = Math.sqrt(variance);

      // Annualize the volatility (assuming daily data)
      const annualizedVolatility = stdDev * Math.sqrt(365);

      return {
        value: stdDev,
        annualized: annualizedVolatility,
        mean: mean,
        confidence: 0.7
      };
    } catch (error) {
      logger.error('Error calculating price change volatility', { error: error.message });
      return { value: 0, annualized: 0, mean: 0, confidence: 0 };
    }
  }

  /**
   * Calculate range volatility (high-low range as percentage of close)
   */
  calculateRangeVolatility(candles) {
    try {
      const ranges = [];
      const period = Math.min(this.config.priceChangePeriod, candles.length);

      for (let i = candles.length - period; i < candles.length; i++) {
        const candle = candles[i];
        const range = (candle.high - candle.low) / candle.close;
        ranges.push(range);
      }

      const avgRange = ranges.reduce((sum, range) => sum + range, 0) / ranges.length;

      // Calculate standard deviation of ranges
      const variance = ranges.reduce((sum, range) => sum + Math.pow(range - avgRange, 2), 0) / ranges.length;
      const stdDev = Math.sqrt(variance);

      return {
        value: avgRange,
        stdDev: stdDev,
        confidence: 0.6
      };
    } catch (error) {
      logger.error('Error calculating range volatility', { error: error.message });
      return { value: 0, stdDev: 0, confidence: 0 };
    }
  }

  /**
   * Calculate gap volatility (open vs previous close)
   */
  calculateGapVolatility(candles) {
    try {
      const gaps = [];
      const period = Math.min(this.config.priceChangePeriod, candles.length - 1);

      for (let i = candles.length - period; i < candles.length; i++) {
        const currentOpen = candles[i].open;
        const previousClose = candles[i - 1].close;
        const gap = Math.abs(currentOpen - previousClose) / previousClose;
        gaps.push(gap);
      }

      const avgGap = gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length;

      return {
        value: avgGap,
        maxGap: Math.max(...gaps),
        confidence: 0.5 // Gaps can be less predictive
      };
    } catch (error) {
      logger.error('Error calculating gap volatility', { error: error.message });
      return { value: 0, maxGap: 0, confidence: 0 };
    }
  }

  /**
   * Combine different volatility measures with weights
   */
  combineVolatilities(volatilities) {
    const weights = {
      atr: 0.4,        // ATR is most reliable
      priceChange: 0.3, // Price change volatility is important
      range: 0.2,       // Range volatility adds context
      gap: 0.1          // Gap volatility is least reliable
    };

    let weightedSum = 0;
    let totalWeight = 0;
    let totalConfidence = 0;

    for (const [type, volatility] of Object.entries(volatilities)) {
      if (volatility.confidence > 0) {
        const weight = weights[type] * volatility.confidence;
        weightedSum += volatility.value * weight;
        totalWeight += weight;
        totalConfidence += volatility.confidence * weights[type];
      }
    }

    const combinedValue = totalWeight > 0 ? weightedSum / totalWeight : 0;
    const confidence = totalConfidence;

    return {
      value: combinedValue,
      confidence: Math.min(1.0, confidence)
    };
  }

  /**
   * Determine volatility level based on combined volatility
   */
  determineVolatilityLevel(volatility) {
    const thresholds = this.config.volatilityThresholds;

    if (volatility >= thresholds.veryHigh) {
      return 'very_high';
    } else if (volatility >= thresholds.high) {
      return 'high';
    } else if (volatility >= thresholds.medium) {
      return 'medium';
    } else if (volatility >= thresholds.low) {
      return 'low';
    } else {
      return 'very_low';
    }
  }

  /**
   * Get volatility trend (increasing/decreasing)
   */
  getVolatilityTrend(candles, periods = [5, 10, 20]) {
    try {
      const trends = {};

      for (const period of periods) {
        if (candles.length < period * 2) continue;

        const recentCandles = candles.slice(-period);
        const olderCandles = candles.slice(-period * 2, -period);

        const recentVolatility = this.calculateVolatility(recentCandles);
        const olderVolatility = this.calculateVolatility(olderCandles);

        const change = recentVolatility.combinedVolatility - olderVolatility.combinedVolatility;
        const changePercent = olderVolatility.combinedVolatility > 0 ?
          (change / olderVolatility.combinedVolatility) * 100 : 0;

        trends[`${period}period`] = {
          change: change,
          changePercent: changePercent,
          direction: change > 0.001 ? 'increasing' : change < -0.001 ? 'decreasing' : 'stable'
        };
      }

      return trends;
    } catch (error) {
      logger.error('Error calculating volatility trend', { error: error.message });
      return {};
    }
  }

  /**
   * Check if volatility is abnormally high
   */
  isAbnormalVolatility(candles, lookbackPeriods = 50) {
    try {
      if (candles.length < lookbackPeriods + this.config.priceChangePeriod) {
        return { abnormal: false, reason: 'insufficient_data' };
      }

      const currentVolatility = this.calculateVolatility(candles.slice(-this.config.priceChangePeriod));

      // Calculate historical volatility distribution
      const historicalVolatilities = [];
      for (let i = lookbackPeriods; i >= this.config.priceChangePeriod; i -= 5) {
        const historicalCandles = candles.slice(-i, -i + this.config.priceChangePeriod);
        const vol = this.calculateVolatility(historicalCandles);
        historicalVolatilities.push(vol.combinedVolatility);
      }

      if (historicalVolatilities.length === 0) {
        return { abnormal: false, reason: 'no_historical_data' };
      }

      // Calculate percentile
      historicalVolatilities.sort((a, b) => a - b);
      const percentile95 = historicalVolatilities[Math.floor(historicalVolatilities.length * 0.95)];
      const percentile99 = historicalVolatilities[Math.floor(historicalVolatilities.length * 0.99)];

      const isAbnormal = currentVolatility.combinedVolatility > percentile95;
      const isExtreme = currentVolatility.combinedVolatility > percentile99;

      return {
        abnormal: isAbnormal,
        extreme: isExtreme,
        currentVolatility: currentVolatility.combinedVolatility,
        percentile95: percentile95,
        percentile99: percentile99,
        percentileRank: this.calculatePercentileRank(currentVolatility.combinedVolatility, historicalVolatilities)
      };

    } catch (error) {
      logger.error('Error checking abnormal volatility', { error: error.message });
      return { abnormal: false, reason: 'calculation_error' };
    }
  }

  /**
   * Calculate percentile rank of current volatility
   */
  calculatePercentileRank(value, sortedArray) {
    let rank = 0;
    for (const item of sortedArray) {
      if (item <= value) rank++;
      else break;
    }
    return (rank / sortedArray.length) * 100;
  }
}

module.exports = MarketVolatilityCalculator;