const axios = require('axios');
const WebSocket = require('ws');

class BinanceClient {
  constructor() {
    this.baseURL = config.trading.binance.baseURL;
    this.wsBaseURL = config.trading.binance.wsBaseURL;
    this.wsConnections = new Map();
    this.candleCallbacks = new Map();
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;

    // Rate limiting
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.requestsPerSecond = 10; // Binance limit: ~1200/min = 20/sec, we use 10 for safety
    this.requestInterval = 1000 / this.requestsPerSecond; // 100ms between requests
    this.lastRequestTime = 0;

    // Price cache to reduce API calls
    this.priceCache = new Map();
    this.priceCacheTimeout = 2000; // 2 seconds cache
  }

  /**
   * L<PERSON>y danh sách top coins theo volume 24h với rate limiting
   */
  async getTop24hVolumeCoins(limit = 50) {
    try {
      const response = await this.makeRateLimitedRequest(async () => {
        return await this.makeRequestWithRetry(
          () => axios.get(`${this.baseURL}/fapi/v1/ticker/24hr`, { timeout: 10000 })
        );
      });

      // Lọc và sắp xếp theo volume
      const sortedCoins = response.data
        .filter(coin => coin.symbol.endsWith('USDT'))
        .sort((a, b) => parseFloat(b.quoteVolume) - parseFloat(a.quoteVolume))
        .slice(0, limit)
        .map(coin => coin.symbol);

      logger.logInfo(`Top ${limit} volume coins:`, sortedCoins);
      return sortedCoins;
    } catch (error) {
      logger.logError('Error getting top volume coins:', error.message);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu klines (candlestick) lịch sử với rate limiting
   */
  async getKlines(symbol, interval, limit = 200) {
    try {
      const response = await this.makeRateLimitedRequest(async () => {
        return await this.makeRequestWithRetry(
          () => axios.get(`${this.baseURL}/fapi/v1/klines`, {
            params: { symbol, interval, limit },
            timeout: 10000
          })
        );
      });

      return response.data.map(kline => ({
        openTime: new Date(kline[0]),
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5]),
        closeTime: new Date(kline[6]),
        quoteVolume: parseFloat(kline[7]),
        trades: parseInt(kline[8])
      }));
    } catch (error) {
      logger.logError(`Error getting klines for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Rate limited request wrapper
   */
  async makeRateLimitedRequest(requestFn) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ requestFn, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Process request queue with rate limiting
   */
  async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;

      if (timeSinceLastRequest < this.requestInterval) {
        await this.sleep(this.requestInterval - timeSinceLastRequest);
      }

      const { requestFn, resolve, reject } = this.requestQueue.shift();
      this.lastRequestTime = Date.now();

      try {
        const result = await requestFn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Lấy giá hiện tại của symbol với rate limiting và cache
   */
  async getCurrentPrice(symbol) {
    // Check cache first
    const cacheKey = `price_${symbol}`;
    const cached = this.priceCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.priceCacheTimeout) {
      return cached.price;
    }

    try {
      const price = await this.makeRateLimitedRequest(async () => {
        const response = await this.makeRequestWithRetry(
          () => axios.get(`${this.baseURL}/fapi/v1/ticker/price`, {
            params: { symbol },
            timeout: 5000
          })
        );
        return parseFloat(response.data.price);
      });

      // Cache the price
      this.priceCache.set(cacheKey, {
        price,
        timestamp: Date.now()
      });

      return price;
    } catch (error) {
      logger.logError(`Error getting current price for ${symbol}:`, error.message);

      // Return cached price if available during error
      if (cached) {
        logger.warn(`Using cached price for ${symbol} due to API error`);
        return cached.price;
      }

      throw error;
    }
  }

  /**
   * Lấy OHLC data của nến hiện tại để check SL/TP chính xác
   * Bao gồm cả high/low để bắt được price spikes
   */
  async getCurrentCandle(symbol, timeframe = '1m') {
    const cacheKey = `candle_${symbol}_${timeframe}`;
    const cached = this.priceCache.get(cacheKey);

    // Cache candle data for shorter time (30 seconds) since it changes more frequently
    const candleCacheTimeout = 30000;
    if (cached && Date.now() - cached.timestamp < candleCacheTimeout) {
      return cached.candle;
    }

    try {
      const candle = await this.makeRateLimitedRequest(async () => {
        const response = await this.makeRequestWithRetry(
          () => axios.get(`${this.baseURL}/fapi/v1/klines`, {
            params: {
              symbol,
              interval: timeframe,
              limit: 1 // Chỉ lấy nến hiện tại
            },
            timeout: 5000
          })
        );

        const klineData = response.data[0];
        if (!klineData) {
          throw new Error(`No kline data for ${symbol}`);
        }

        return {
          symbol,
          timeframe,
          openTime: parseInt(klineData[0]),
          closeTime: parseInt(klineData[6]),
          open: parseFloat(klineData[1]),
          high: parseFloat(klineData[2]),
          low: parseFloat(klineData[3]),
          close: parseFloat(klineData[4]),
          volume: parseFloat(klineData[5]),
          timestamp: Date.now()
        };
      });

      // Cache the candle data
      this.priceCache.set(cacheKey, {
        candle,
        timestamp: Date.now()
      });

      return candle;
    } catch (error) {
      logger.logError(`Error getting current candle for ${symbol}:`, error.message);

      // Return cached candle if available during error
      if (cached) {
        logger.warn(`Using cached candle for ${symbol} due to API error`);
        return cached.candle;
      }

      throw error;
    }
  }

  /**
   * Make request with retry logic for rate limiting errors
   */
  async makeRequestWithRetry(requestFn, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        const isRateLimitError = error.response?.status === 418 ||
          error.response?.status === 429 ||
          error.code === 'ECONNRESET';

        if (isRateLimitError && attempt < maxRetries) {
          const backoffDelay = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s
          logger.warn(`Rate limit hit for attempt ${attempt}, retrying in ${backoffDelay}ms...`);
          await this.sleep(backoffDelay);
          continue;
        }

        // Log specific error details
        if (error.response?.status === 418) {
          logger.logError(`Binance 418 error - Rate limit exceeded. Backing off...`);
        } else if (error.response?.status === 429) {
          logger.logError(`Binance 429 error - Too many requests. Backing off...`);
        }

        throw error;
      }
    }
  }

  /**
   * Đăng ký callback cho dữ liệu nến real-time
   */
  subscribeCandlestick(symbol, interval, callback) {
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`;
    const key = `${symbol}_${interval}`;

    this.candleCallbacks.set(key, callback);
    this.connectWebSocket(streamName, key);
  }

  /**
   * Hủy đăng ký callback
   */
  unsubscribeCandlestick(symbol, interval) {
    const key = `${symbol}_${interval}`;

    if (this.wsConnections.has(key)) {
      this.wsConnections.get(key).close();
      this.wsConnections.delete(key);
    }

    this.candleCallbacks.delete(key);
    this.reconnectAttempts.delete(key);
  }

  /**
   * Kết nối WebSocket
   */
  connectWebSocket(streamName, key) {
    const wsUrl = `${this.wsBaseURL}${streamName}`;

    try {
      const ws = new WebSocket(wsUrl);

      ws.on('open', () => {
        logger.logInfo(`WebSocket connected: ${streamName}`);
        this.reconnectAttempts.set(key, 0);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.k && message.k.x) { // Nến đã đóng
            const kline = message.k;
            const candleData = {
              symbol: kline.s,
              openTime: new Date(kline.t),
              closeTime: new Date(kline.T),
              open: parseFloat(kline.o),
              high: parseFloat(kline.h),
              low: parseFloat(kline.l),
              close: parseFloat(kline.c),
              volume: parseFloat(kline.v),
              quoteVolume: parseFloat(kline.q),
              trades: parseInt(kline.n),
              interval: kline.i
            };

            const callback = this.candleCallbacks.get(key);
            if (callback) {
              callback(candleData);
            }
          }
        } catch (error) {
          logger.logError(`Error parsing WebSocket message for ${key}:`, error.message);
        }
      });

      ws.on('close', () => {
        logger.warn(`WebSocket closed: ${streamName}`);
        this.handleReconnect(streamName, key);
      });

      ws.on('error', (error) => {
        logger.logError(`WebSocket error for ${streamName}:`, error.message);
        this.handleReconnect(streamName, key);
      });

      this.wsConnections.set(key, ws);

    } catch (error) {
      logger.logError(`Error creating WebSocket for ${streamName}:`, error.message);
      this.handleReconnect(streamName, key);
    }
  }

  /**
   * Xử lý reconnect WebSocket
   */
  handleReconnect(streamName, key) {
    const attempts = this.reconnectAttempts.get(key) || 0;

    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(key, attempts + 1);

      setTimeout(() => {
        logger.logInfo(`Reconnecting WebSocket (${attempts + 1}/${this.maxReconnectAttempts}): ${streamName}`);
        this.connectWebSocket(streamName, key);
      }, this.reconnectDelay * (attempts + 1));
    } else {
      logger.logError(`Max reconnect attempts reached for ${streamName}`);
      this.candleCallbacks.delete(key);
      this.reconnectAttempts.delete(key);
    }
  }

  /**
   * Đóng tất cả kết nối WebSocket
   */
  closeAllConnections() {
    for (const [, ws] of this.wsConnections) {
      ws.close();
    }
    this.wsConnections.clear();
    this.candleCallbacks.clear();
    this.reconnectAttempts.clear();
  }

  /**
   * Test connectivity to Binance API
   */
  async testConnectivity() {
    try {
      await this.makeRateLimitedRequest(async () => {
        return await this.makeRequestWithRetry(
          () => axios.get(`${this.baseURL}/fapi/v1/ping`, { timeout: 5000 })
        );
      });
      return true;
    } catch (error) {
      logger.logError('Binance API connectivity test failed:', error.message);
      return false;
    }
  }

  /**
   * Kiểm tra trạng thái kết nối
   */
  getConnectionStatus() {
    const status = {};
    for (const [key, ws] of this.wsConnections) {
      status[key] = ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected';
    }
    return status;
  }

  /**
   * Clear price cache
   */
  clearPriceCache() {
    this.priceCache.clear();
    logger.logInfo('Price cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      priceCache: {
        size: this.priceCache.size,
        timeout: this.priceCacheTimeout
      },
      requestQueue: {
        pending: this.requestQueue.length,
        processing: this.isProcessingQueue,
        rateLimit: this.requestsPerSecond
      }
    };
  }
}

module.exports = new BinanceClient();
