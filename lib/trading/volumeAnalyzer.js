const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

/**
 * Volume Analyzer - Analyzes volume patterns for signal confirmation
 * Implements requirement 3.1: Volume confirmation checks
 */
class VolumeAnalyzer {
  constructor() {
    this.volumeThresholds = {
      minimal: 1.2,    // 20% above average
      moderate: 1.5,   // 50% above average
      strong: 2.0,     // 100% above average
      exceptional: 3.0 // 200% above average
    };
  }

  /**
   * Analyze volume confirmation for trading signals
   * Requirement 3.1: Volume >1.5x average for confirmation
   */
  async analyzeVolumeConfirmation(candles, signalType) {
    try {
      if (!candles || candles.length < 20) {
        return {
          isAboveAverage: false,
          volumeRatio: 0,
          strength: 'insufficient_data',
          reason: 'Not enough candle data for volume analysis'
        };
      }

      // Calculate volume metrics
      const volumeMetrics = this.calculateVolumeMetrics(candles);

      // Analyze volume pattern
      const volumePattern = this.analyzeVolumePattern(candles, signalType);

      // Determine volume confirmation
      const isAboveAverage = volumeMetrics.currentRatio >= this.volumeThresholds.moderate;

      return {
        isAboveAverage,
        volumeRatio: volumeMetrics.currentRatio,
        strength: this.getVolumeStrength(volumeMetrics.currentRatio),
        averageVolume: volumeMetrics.averageVolume,
        currentVolume: volumeMetrics.currentVolume,
        pattern: volumePattern,
        reason: this.getVolumeReason(volumeMetrics.currentRatio, isAboveAverage)
      };
    } catch (error) {
      logger.logError('Error analyzing volume confirmation:', error.message);
      return {
        isAboveAverage: false,
        volumeRatio: 0,
        strength: 'error',
        reason: 'Error analyzing volume'
      };
    }
  }

  /**
   * Calculate basic volume metrics
   */
  calculateVolumeMetrics(candles) {
    // Use last 20 candles for average calculation
    const volumeWindow = Math.min(20, candles.length - 1);
    const recentVolumes = candles.slice(-volumeWindow - 1, -1).map(c => c.volume);

    const averageVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
    const currentVolume = candles[candles.length - 1].volume;
    const currentRatio = currentVolume / averageVolume;

    // Calculate volume trend (last 5 candles)
    const trendWindow = Math.min(5, recentVolumes.length);
    const recentTrend = recentVolumes.slice(-trendWindow);
    const trendSlope = this.calculateVolumeSlope(recentTrend);

    return {
      averageVolume,
      currentVolume,
      currentRatio,
      trendSlope,
      volumeWindow
    };
  }

  /**
   * Analyze volume pattern in relation to price action
   */
  analyzeVolumePattern(candles, signalType) {
    if (candles.length < 3) {
      return { pattern: 'insufficient_data', confidence: 0 };
    }

    const current = candles[candles.length - 1];
    const previous = candles[candles.length - 2];

    // Volume vs Price relationship
    const priceChange = (current.close - previous.close) / previous.close;
    const volumeChange = (current.volume - previous.volume) / previous.volume;

    // Determine pattern
    let pattern = 'neutral';
    let confidence = 0.5;

    if (signalType === 'BUY') {
      if (priceChange > 0 && volumeChange > 0) {
        pattern = 'bullish_confirmation'; // Price up, volume up
        confidence = 0.8;
      } else if (priceChange > 0 && volumeChange < -0.2) {
        pattern = 'bullish_divergence'; // Price up, volume down (weak)
        confidence = 0.3;
      } else if (priceChange < 0 && volumeChange > 0.5) {
        pattern = 'accumulation'; // Price down, high volume (potential accumulation)
        confidence = 0.6;
      }
    } else if (signalType === 'SELL') {
      if (priceChange < 0 && volumeChange > 0) {
        pattern = 'bearish_confirmation'; // Price down, volume up
        confidence = 0.8;
      } else if (priceChange < 0 && volumeChange < -0.2) {
        pattern = 'bearish_divergence'; // Price down, volume down (weak)
        confidence = 0.3;
      } else if (priceChange > 0 && volumeChange > 0.5) {
        pattern = 'distribution'; // Price up, high volume (potential distribution)
        confidence = 0.6;
      }
    }

    return {
      pattern,
      confidence,
      priceChange: priceChange * 100, // Convert to percentage
      volumeChange: volumeChange * 100,
      analysis: this.getPatternAnalysis(pattern)
    };
  }

  /**
   * Calculate volume trend slope
   */
  calculateVolumeSlope(volumes) {
    if (volumes.length < 2) return 0;

    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    const n = volumes.length;

    for (let i = 0; i < n; i++) {
      sumX += i;
      sumY += volumes[i];
      sumXY += i * volumes[i];
      sumXX += i * i;
    }

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return slope;
  }

  /**
   * Get volume strength classification
   */
  getVolumeStrength(ratio) {
    if (ratio >= this.volumeThresholds.exceptional) {
      return 'exceptional';
    } else if (ratio >= this.volumeThresholds.strong) {
      return 'strong';
    } else if (ratio >= this.volumeThresholds.moderate) {
      return 'moderate';
    } else if (ratio >= this.volumeThresholds.minimal) {
      return 'minimal';
    } else {
      return 'weak';
    }
  }

  /**
   * Get volume confirmation reason
   */
  getVolumeReason(ratio, isAboveAverage) {
    if (isAboveAverage) {
      return `Volume ${ratio.toFixed(2)}x average - Strong confirmation`;
    } else {
      return `Volume ${ratio.toFixed(2)}x average - Below confirmation threshold (${this.volumeThresholds.moderate}x)`;
    }
  }

  /**
   * Get pattern analysis description
   */
  getPatternAnalysis(pattern) {
    const analyses = {
      'bullish_confirmation': 'Strong bullish signal - Price and volume both increasing',
      'bearish_confirmation': 'Strong bearish signal - Price declining with high volume',
      'bullish_divergence': 'Weak bullish signal - Price up but volume declining',
      'bearish_divergence': 'Weak bearish signal - Price down but volume declining',
      'accumulation': 'Potential accumulation - Price down but high volume suggests buying interest',
      'distribution': 'Potential distribution - Price up but high volume suggests selling pressure',
      'neutral': 'Neutral volume pattern - No clear directional bias',
      'insufficient_data': 'Not enough data for pattern analysis'
    };

    return analyses[pattern] || 'Unknown pattern';
  }

  /**
   * Check if volume supports breakout
   */
  checkBreakoutVolumeSupport(candles, breakoutPrice, direction) {
    try {
      if (candles.length < 10) return false;

      const volumeMetrics = this.calculateVolumeMetrics(candles);

      // Breakout should have at least 2x average volume
      const breakoutVolumeThreshold = 2.0;

      // Check if current volume supports breakout
      const hasVolumeSupport = volumeMetrics.currentRatio >= breakoutVolumeThreshold;

      // Check volume trend leading to breakout
      const recentCandles = candles.slice(-5);
      const volumeTrend = this.calculateVolumeSlope(recentCandles.map(c => c.volume));
      const hasIncreasingVolume = volumeTrend > 0;

      return {
        hasSupport: hasVolumeSupport && hasIncreasingVolume,
        volumeRatio: volumeMetrics.currentRatio,
        volumeTrend: volumeTrend > 0 ? 'increasing' : 'decreasing',
        threshold: breakoutVolumeThreshold
      };
    } catch (error) {
      logger.logError('Error checking breakout volume support:', error.message);
      return { hasSupport: false, error: error.message };
    }
  }

  /**
   * Analyze volume profile for support/resistance levels
   */
  analyzeVolumeProfile(candles, priceLevels) {
    try {
      if (candles.length < 20) return {};

      const volumeProfile = {};

      // Group candles by price levels and sum volume
      for (const candle of candles) {
        const priceLevel = Math.round(candle.close * 100) / 100; // Round to 2 decimals

        if (!volumeProfile[priceLevel]) {
          volumeProfile[priceLevel] = {
            totalVolume: 0,
            candleCount: 0,
            avgVolume: 0
          };
        }

        volumeProfile[priceLevel].totalVolume += candle.volume;
        volumeProfile[priceLevel].candleCount += 1;
        volumeProfile[priceLevel].avgVolume = volumeProfile[priceLevel].totalVolume / volumeProfile[priceLevel].candleCount;
      }

      // Find high volume nodes (potential support/resistance)
      const sortedLevels = Object.entries(volumeProfile)
        .sort(([,a], [,b]) => b.totalVolume - a.totalVolume)
        .slice(0, 5); // Top 5 volume levels

      return {
        highVolumeNodes: sortedLevels.map(([price, data]) => ({
          price: parseFloat(price),
          volume: data.totalVolume,
          strength: data.candleCount,
          avgVolume: data.avgVolume
        })),
        totalLevels: Object.keys(volumeProfile).length
      };
    } catch (error) {
      logger.logError('Error analyzing volume profile:', error.message);
      return {};
    }
  }
}

module.exports = VolumeAnalyzer;