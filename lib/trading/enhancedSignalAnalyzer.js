const SignalAnalyzer = require('./signalAnalyzer');
const VolumeAnalyzer = require('./volumeAnalyzer');
const MarketRegimeDetector = require('./marketRegimeDetector');
const SignalStrengthClassifier = require('./signalStrengthClassifier');
const config = require('config');
const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

/**
 * Enhanced Signal Analyzer - Extends existing SignalAnalyzer with advanced filtering
 * Implements requirements 3.1, 3.2, 3.3 for enhanced signal analysis
 */
class EnhancedSignalAnalyzer extends SignalAnalyzer {
  constructor() {
    try {
      super();
    } catch (error) {
      // Handle case where parent constructor fails (e.g., in testing)
      this.timeframes = config?.trading?.timeframes || ['1h'];
    }
    this.volumeAnalyzer = new VolumeAnalyzer();
    this.marketRegimeDetector = new MarketRegimeDetector();
    this.strengthClassifier = new SignalStrengthClassifier();
  }

  /**
   * Analyze signal with enhanced filtering and quality scoring
   * Requirement 3.1: Volume confirmation checks
   * Requirement 3.2: Market regime classification
   * Requirement 3.3: Signal strength rating
   */
  async analyzeSignalWithEnhancements(symbol, timeframe, candles) {
    try {
      // Call base signal analysis first
      const baseSignal = await super.analyzeSignal(symbol, timeframe, candles);
      if (!baseSignal || !baseSignal.entry) {
        return null;
      }

      // Apply enhanced filters
      const enhancements = await this.applyEnhancedFilters(symbol, timeframe, candles, baseSignal);

      // Calculate signal quality score (0-100)
      const qualityScore = this.calculateQualityScore(baseSignal, enhancements);

      // Only accept signals with quality >= 70 (Requirement 3.3)
      const isValid = qualityScore >= 70;

      if (!isValid) {
        logger.logInfo(`Signal rejected for ${symbol} ${timeframe}: Quality score ${qualityScore} < 70`);
        return null;
      }

      // Calculate dynamic Risk/Reward based on signal strength
      const recommendedRR = this.calculateDynamicRR(enhancements.signalStrength, enhancements.marketRegime);

      return {
        ...baseSignal,
        enhancements,
        qualityScore,
        isValid,
        recommendedRR,
        enhancedAnalysis: {
          volumeConfirmation: enhancements.volumeConfirmation,
          marketRegime: enhancements.marketRegime,
          signalStrength: enhancements.signalStrength,
          timeFilter: enhancements.timeFilter,
          supportResistanceFilter: enhancements.supportResistanceFilter
        }
      };
    } catch (error) {
      logger.logError(`Error in enhanced signal analysis for ${symbol} ${timeframe}:`, error.message);
      return null;
    }
  }

  /**
   * Apply enhanced filters to signal
   */
  async applyEnhancedFilters(symbol, timeframe, candles, baseSignal) {
    // Volume confirmation analysis (Requirement 3.1)
    const volumeConfirmation = await this.volumeAnalyzer.analyzeVolumeConfirmation(candles, baseSignal.type);

    // Market regime detection (Requirement 3.2)
    const marketRegime = await this.marketRegimeDetector.detectMarketRegime(candles);

    // Signal strength classification (Requirement 3.3)
    const signalStrength = await this.strengthClassifier.classifySignalStrength(baseSignal, candles);

    // Time-based filtering
    const timeFilter = this.checkOptimalTradingTime();

    // Support/Resistance proximity check
    const supportResistanceFilter = this.checkSRProximity(baseSignal.entry, candles);

    return {
      volumeConfirmation,
      marketRegime,
      signalStrength,
      timeFilter,
      supportResistanceFilter
    };
  }

  /**
   * Calculate signal quality score (0-100)
   * Combines multiple factors to rate signal quality
   */
  calculateQualityScore(signal, enhancements) {
    let score = 50; // Base score

    // Volume confirmation (+20 points) - Requirement 3.1
    if (enhancements.volumeConfirmation.isAboveAverage) {
      score += 20;
    }
    if (enhancements.volumeConfirmation.volumeRatio >= 2.0) {
      score += 5; // Bonus for very high volume
    }

    // Market regime (+15 points for trending, -10 for sideways) - Requirement 3.2
    if (enhancements.marketRegime.regime === 'trending') {
      score += 15;
    } else if (enhancements.marketRegime.regime === 'sideways') {
      score -= 10;
    } else if (enhancements.marketRegime.regime === 'volatile') {
      score -= 5;
    }

    // Signal strength (+25 points for strong, +10 for medium) - Requirement 3.3
    if (enhancements.signalStrength.strength === 'strong') {
      score += 25;
    } else if (enhancements.signalStrength.strength === 'medium') {
      score += 10;
    } else if (enhancements.signalStrength.strength === 'weak') {
      score -= 5;
    }

    // Time filter (+10 points for optimal time)
    if (enhancements.timeFilter.isOptimal) {
      score += 10;
    }

    // Support/Resistance proximity (-15 points if too close)
    if (enhancements.supportResistanceFilter.tooClose) {
      score -= 15;
    }

    // Market regime confidence bonus
    if (enhancements.marketRegime.confidence >= 0.8) {
      score += 5;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Calculate dynamic Risk/Reward based on signal strength and market regime
   */
  calculateDynamicRR(signalStrength, marketRegime) {
    let baseRR = 1.5;

    // Adjust based on signal strength
    switch (signalStrength.strength) {
      case 'strong':
        baseRR = 2.5;
        break;
      case 'medium':
        baseRR = 2.0;
        break;
      case 'weak':
        baseRR = 1.2;
        break;
    }

    // Adjust based on market regime
    switch (marketRegime.regime) {
      case 'trending':
        baseRR *= 1.2; // Increase RR in trending markets
        break;
      case 'sideways':
        baseRR *= 0.8; // Decrease RR in sideways markets
        break;
      case 'volatile':
        baseRR *= 0.9; // Slightly decrease RR in volatile markets
        break;
    }

    // Apply confidence multiplier
    const confidenceMultiplier = 0.8 + (marketRegime.confidence * 0.4);
    baseRR *= confidenceMultiplier;

    return Math.max(1.2, Math.min(3.0, baseRR));
  }

  /**
   * Check optimal trading time
   * Avoid low liquidity periods (22:00-02:00 UTC)
   */
  checkOptimalTradingTime() {
    const now = new Date();
    const utcHour = now.getUTCHours();

    // Low liquidity period: 22:00-02:00 UTC
    const isLowLiquidity = utcHour >= 22 || utcHour <= 2;

    return {
      isOptimal: !isLowLiquidity,
      currentHour: utcHour,
      reason: isLowLiquidity ? 'Low liquidity period (22:00-02:00 UTC)' : 'Optimal trading time'
    };
  }

  /**
   * Check Support/Resistance proximity
   * Reject signals too close to major S/R levels
   */
  checkSRProximity(entryPrice, candles) {
    try {
      const indicators = require('./indicators');
      const { supports, resistances } = indicators.findAdvancedSupportResistance(candles, 50);

      const tolerance = entryPrice * 0.01; // 1% tolerance

      // Check proximity to resistance levels
      for (const resistance of resistances.slice(0, 3)) { // Check top 3 resistance levels
        if (Math.abs(entryPrice - resistance.price) <= tolerance) {
          return {
            tooClose: true,
            level: resistance.price,
            type: 'resistance',
            strength: resistance.strength,
            distance: Math.abs(entryPrice - resistance.price) / entryPrice * 100
          };
        }
      }

      // Check proximity to support levels
      for (const support of supports.slice(0, 3)) { // Check top 3 support levels
        if (Math.abs(entryPrice - support.price) <= tolerance) {
          return {
            tooClose: true,
            level: support.price,
            type: 'support',
            strength: support.strength,
            distance: Math.abs(entryPrice - support.price) / entryPrice * 100
          };
        }
      }

      return {
        tooClose: false,
        reason: 'Entry not near major S/R levels'
      };
    } catch (error) {
      logger.logError('Error checking S/R proximity:', error.message);
      return { tooClose: false, reason: 'Error checking S/R levels' };
    }
  }

  /**
   * Override base analyzeSignal to use enhanced version
   */
  async analyzeSignal(symbol, timeframe, candles) {
    return await this.analyzeSignalWithEnhancements(symbol, timeframe, candles);
  }
}

module.exports = EnhancedSignalAnalyzer;