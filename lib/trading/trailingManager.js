const TradingSignal = require('../models/tradingSignal');

class TrailingManager {
  constructor() {
    this.trailingSignals = new Map(); // signalId -> trailing data
    this.config = config.trading.riskManagement;
  }

  /**
   * Thêm signal vào trailing management
   */
  addSignalToTrailing(signal) {
    const trailingData = {
      signalId: signal._id.toString(),
      symbol: signal.symbol,
      type: signal.type,
      entry: signal.entry,
      originalSL: signal.stopLoss,
      originalTP: signal.takeProfit,
      currentSL: signal.stopLoss,
      currentTP: signal.takeProfit,
      highestPrice: signal.type === 'BUY' ? signal.entry : null,
      lowestPrice: signal.type === 'SELL' ? signal.entry : null,
      trailingActive: false,
      breakeven: false,
      partialTaken: false,
      breakevenNotified: false,
      trailingNotified: false,
      partialNotified: false,
      createdAt: new Date()
    };

    this.trailingSignals.set(signal._id.toString(), trailingData);
    logger.logInfo(`Added signal to trailing: ${signal.symbol} ${signal.type}`);
  }

  /**
   * Cập nhật trailing cho tất cả signals
   */
  async updateAllTrailing(priceData) {
    for (const [signalId, trailingData] of this.trailingSignals) {
      const currentPrice = priceData[trailingData.symbol];
      if (currentPrice) {
        await this.updateTrailing(signalId, currentPrice);
      }
    }
  }

  /**
   * Cập nhật trailing cho một signal
   */
  async updateTrailing(signalId, currentPrice) {
    const trailingData = this.trailingSignals.get(signalId);
    if (!trailingData) return;

    try {
      let updated = false;

      if (trailingData.type === 'BUY') {
        updated = await this.updateBuyTrailing(trailingData, currentPrice);
      } else {
        updated = await this.updateSellTrailing(trailingData, currentPrice);
      }

      if (updated) {
        // Cập nhật database
        await this.updateSignalInDB(signalId, trailingData);

        // Gửi thông báo nếu có thay đổi quan trọng
        await this.notifyTrailingUpdate(trailingData, currentPrice);
      }

    } catch (error) {
      logger.logError(`Error updating trailing for ${signalId}:`, error.message);
    }
  }

  /**
   * Cập nhật trailing cho BUY signal
   */
  async updateBuyTrailing(trailingData, currentPrice) {
    let updated = false;
    const riskAmount = trailingData.entry - trailingData.originalSL;

    // Cập nhật highest price
    if (currentPrice > (trailingData.highestPrice || 0)) {
      trailingData.highestPrice = currentPrice;
      updated = true;
    }

    // 1. Move to Breakeven khi đạt 1R
    if (!trailingData.breakeven && currentPrice >= trailingData.entry + riskAmount) {
      trailingData.currentSL = trailingData.entry + (riskAmount * 0.1); // Entry + 10% risk
      trailingData.breakeven = true;
      updated = true;
      logger.logInfo(`Moved to breakeven: ${trailingData.symbol} SL: ${trailingData.currentSL}`);
    }

    // 2. Activate trailing khi đạt 1.5R
    if (!trailingData.trailingActive && currentPrice >= trailingData.entry + (riskAmount * 1.5)) {
      trailingData.trailingActive = true;
      updated = true;
      logger.logInfo(`Trailing activated: ${trailingData.symbol}`);
    }

    // 3. Trailing stop logic
    if (trailingData.trailingActive && trailingData.highestPrice) {
      const trailingDistance = riskAmount * 0.5; // Trail 50% of original risk
      const newSL = trailingData.highestPrice - trailingDistance;

      if (newSL > trailingData.currentSL) {
        trailingData.currentSL = newSL;
        updated = true;
        logger.logInfo(`Trailing SL updated: ${trailingData.symbol} SL: ${newSL}`);
      }
    }

    // 4. Partial profit taking tại TP1
    if (!trailingData.partialTaken && currentPrice >= trailingData.originalTP) {
      trailingData.partialTaken = true;
      // Extend TP cho phần còn lại
      const extendedTP = trailingData.originalTP + (riskAmount * 1.0);
      trailingData.currentTP = extendedTP;
      updated = true;
      logger.logInfo(`Partial TP hit, extended TP: ${trailingData.symbol} TP: ${extendedTP}`);
    }

    return updated;
  }

  /**
   * Cập nhật trailing cho SELL signal
   */
  async updateSellTrailing(trailingData, currentPrice) {
    let updated = false;
    const riskAmount = trailingData.originalSL - trailingData.entry; // Positive value
    const profitAmount = trailingData.entry - trailingData.originalTP; // Positive value

    // Cập nhật lowest price
    if (currentPrice < (trailingData.lowestPrice || Infinity)) {
      trailingData.lowestPrice = currentPrice;
      updated = true;
    }

    // 1. Move to Breakeven khi đạt 1R (giá giảm đủ để có lời 1R)
    if (!trailingData.breakeven && currentPrice <= trailingData.entry - profitAmount) {
      // Cho SELL: SL breakeven = Entry - 10% risk (THẤP HƠN entry)
      trailingData.currentSL = trailingData.entry - (riskAmount * 0.1);

      trailingData.breakeven = true;
      updated = true;
      logger.logInfo(`SELL Moved to breakeven: ${trailingData.symbol} Entry: ${trailingData.entry} New SL: ${trailingData.currentSL} (Entry - 10% risk)`);
    }

    // 2. Activate trailing khi đạt 1.5R (giá giảm đủ để có lời 1.5R)
    if (!trailingData.trailingActive && currentPrice <= trailingData.entry - (profitAmount * 1.5)) {
      trailingData.trailingActive = true;
      updated = true;
      logger.logInfo(`SELL Trailing activated: ${trailingData.symbol}`);
    }

    // 3. Trailing stop logic
    if (trailingData.trailingActive && trailingData.lowestPrice) {
      const trailingDistance = profitAmount * 0.5; // Trail 50% of original profit target
      const newSL = trailingData.lowestPrice + trailingDistance;

      // Cho SELL: SL mới phải THẤP HƠN SL hiện tại (tốt hơn)
      if (newSL < trailingData.currentSL) {
        trailingData.currentSL = newSL;
        updated = true;
        logger.logInfo(`SELL Trailing SL updated: ${trailingData.symbol} SL: ${newSL}`);
      }
    }

    // 4. Partial profit taking tại TP1
    if (!trailingData.partialTaken && currentPrice <= trailingData.originalTP) {
      trailingData.partialTaken = true;
      // Extend TP cho phần còn lại (TP thấp hơn cho SELL)
      const extendedTP = trailingData.originalTP - (profitAmount * 1.0);
      trailingData.currentTP = extendedTP;
      updated = true;
      logger.logInfo(`SELL Partial TP hit, extended TP: ${trailingData.symbol} TP: ${extendedTP}`);
    }

    return updated;
  }

  /**
   * Cập nhật signal trong database
   */
  async updateSignalInDB(signalId, trailingData) {
    try {
      await TradingSignal.findByIdAndUpdate(signalId, {
        stopLoss: trailingData.currentSL,
        takeProfit: trailingData.currentTP,
        updatedAt: new Date()
      });
    } catch (error) {
      logger.logError(`Error updating signal in DB:`, error.message);
    }
  }

  /**
   * Gửi thông báo cập nhật trailing
   */
  async notifyTrailingUpdate(trailingData, currentPrice) {
    try {
      const telegramBot = require('./telegramBot');

      let message = '';

      // Chỉ gửi thông báo breakeven 1 lần
      if (trailingData.breakeven && !trailingData.trailingActive && !trailingData.breakevenNotified) {
        message = `🔒 <b>BREAKEVEN</b> 🔒\n\n📊 <b>Cặp:</b> ${trailingData.symbol}\n📈 <b>Loại:</b> ${trailingData.type}\n💰 <b>Giá hiện tại:</b> ${currentPrice.toFixed(6)}\n🛑 <b>SL mới:</b> ${trailingData.currentSL.toFixed(6)}\n\n✅ Lệnh đã về breakeven!`;
        trailingData.breakevenNotified = true;
      }
      // Chỉ gửi thông báo trailing active 1 lần
      else if (trailingData.trailingActive && !trailingData.trailingNotified) {
        message = `📈 <b>TRAILING ACTIVE</b> 📈\n\n📊 <b>Cặp:</b> ${trailingData.symbol}\n📈 <b>Loại:</b> ${trailingData.type}\n💰 <b>Giá hiện tại:</b> ${currentPrice.toFixed(6)}\n🛑 <b>SL trailing:</b> ${trailingData.currentSL.toFixed(6)}\n\n🚀 Trailing stop đang hoạt động!`;
        trailingData.trailingNotified = true;
      }
      // Chỉ gửi thông báo partial TP 1 lần
      else if (trailingData.partialTaken && !trailingData.partialNotified) {
        message = `🎯 <b>PARTIAL TP</b> 🎯\n\n📊 <b>Cặp:</b> ${trailingData.symbol}\n📈 <b>Loại:</b> ${trailingData.type}\n💰 <b>Giá hiện tại:</b> ${currentPrice.toFixed(6)}\n🎯 <b>TP mở rộng:</b> ${trailingData.currentTP.toFixed(6)}\n\n💰 Đã chốt lời một phần!`;
        trailingData.partialNotified = true;
      }

      if (message) {
        await telegramBot.bot.sendMessage(telegramBot.config.chatId, message, {
          parse_mode: 'HTML'
        });
      }

    } catch (error) {
      logger.logError('Error sending trailing notification:', error.message);
    }
  }

  /**
   * Kiểm tra trailing SL/TP
   */
  checkTrailingSLTP(signalId, currentPrice) {
    const trailingData = this.trailingSignals.get(signalId);
    if (!trailingData) return { hitSL: false, hitTP: false };

    let hitSL = false;
    let hitTP = false;

    if (trailingData.type === 'BUY') {
      hitSL = currentPrice <= trailingData.currentSL;
      hitTP = currentPrice >= trailingData.currentTP;
    } else {
      hitSL = currentPrice >= trailingData.currentSL;
      hitTP = currentPrice <= trailingData.currentTP;
    }

    return { hitSL, hitTP };
  }

  /**
   * Xóa signal khỏi trailing
   */
  removeSignalFromTrailing(signalId) {
    this.trailingSignals.delete(signalId);
    logger.logInfo(`Removed signal from trailing: ${signalId}`);
  }

  /**
   * Lấy thông tin trailing
   */
  getTrailingInfo(signalId) {
    return this.trailingSignals.get(signalId);
  }

  /**
   * Lấy tất cả trailing signals
   */
  getAllTrailingSignals() {
    return Array.from(this.trailingSignals.values());
  }

  /**
   * Cleanup trailing data cũ
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [signalId, trailingData] of this.trailingSignals) {
      if (now - trailingData.createdAt.getTime() > maxAge) {
        this.trailingSignals.delete(signalId);
        logger.logInfo(`Cleaned up old trailing data: ${signalId}`);
      }
    }
  }
}

module.exports = new TrailingManager();
