const logger = require('../logger');

/**
 * Correlation Risk Calculator
 * Calculates correlation between symbols to manage portfolio concentration risk
 */
class CorrelationRiskCalculator {
  constructor(config = {}) {
    this.config = {
      correlationThreshold: config.correlationThreshold || 0.8,
      lookbackPeriod: config.lookbackPeriod || 30, // days
      maxCorrelatedPositions: config.maxCorrelatedPositions || 3,
      correlationDecayFactor: config.correlationDecayFactor || 0.95,

      // Predefined correlation matrix for major crypto pairs
      // In production, this would be calculated from historical data
      staticCorrelations: {
        'BTCUSDT': {
          'ETHUSDT': 0.85,
          'ADAUSDT': 0.65,
          'SOLUSDT': 0.75,
          'DOGEUSDT': 0.55,
          'DOTUSDT': 0.70,
          'AVAXUSDT': 0.72,
          'LTCUSDT': 0.78,
          'BNBUSDT': 0.68,
          'XRPUSDT': 0.60
        },
        'ETHUSDT': {
          'BTCUSDT': 0.85,
          'ADAUSDT': 0.75,
          'SOLUSDT': 0.82,
          'DOGEUSDT': 0.45,
          'DOTUSDT': 0.78,
          'AVAXUSDT': 0.80,
          'LTCUSDT': 0.70,
          'BNBUSDT': 0.72,
          'XRPUSDT': 0.58
        },
        'ADAUSDT': {
          'BTCUSDT': 0.65,
          'ETHUSDT': 0.75,
          'SOLUSDT': 0.68,
          'DOGEUSDT': 0.40,
          'DOTUSDT': 0.72,
          'AVAXUSDT': 0.70,
          'LTCUSDT': 0.62,
          'BNBUSDT': 0.65,
          'XRPUSDT': 0.55
        },
        'SOLUSDT': {
          'BTCUSDT': 0.75,
          'ETHUSDT': 0.82,
          'ADAUSDT': 0.68,
          'DOGEUSDT': 0.42,
          'DOTUSDT': 0.74,
          'AVAXUSDT': 0.78,
          'LTCUSDT': 0.68,
          'BNBUSDT': 0.70,
          'XRPUSDT': 0.56
        }
      },

      ...config
    };

    // Cache for calculated correlations
    this.correlationCache = new Map();
    this.lastCacheUpdate = new Map();
  }

  /**
   * Calculate correlation risk for a new position
   */
  async calculateCorrelationRisk(symbol, currentPortfolio) {
    try {
      const risk = {
        maxCorrelation: 0,
        correlatedPositions: [],
        totalCorrelationExposure: 0,
        riskLevel: 'low',
        positionSizeMultiplier: 1.0,
        recommendations: []
      };

      if (!currentPortfolio || Object.keys(currentPortfolio).length === 0) {
        return risk;
      }

      // Calculate correlations with existing positions
      for (const [portfolioSymbol, position] of Object.entries(currentPortfolio)) {
        if (position.size > 0 && portfolioSymbol !== symbol) {
          const correlation = await this.getCorrelation(symbol, portfolioSymbol);

          if (correlation > 0) {
            const correlatedPosition = {
              symbol: portfolioSymbol,
              correlation: correlation,
              positionSize: position.size,
              positionValue: position.value || 0,
              weight: position.weight || 0
            };

            risk.correlatedPositions.push(correlatedPosition);

            if (correlation > risk.maxCorrelation) {
              risk.maxCorrelation = correlation;
            }

            // Calculate weighted correlation exposure
            risk.totalCorrelationExposure += correlation * (position.weight || 0);
          }
        }
      }

      // Sort by correlation (highest first)
      risk.correlatedPositions.sort((a, b) => b.correlation - a.correlation);

      // Assess risk level and calculate multiplier
      const riskAssessment = this.assessCorrelationRisk(risk);
      risk.riskLevel = riskAssessment.level;
      risk.positionSizeMultiplier = riskAssessment.multiplier;
      risk.recommendations = riskAssessment.recommendations;

      logger.debug('Correlation risk calculated', {
        symbol,
        maxCorrelation: risk.maxCorrelation,
        correlatedPositions: risk.correlatedPositions.length,
        riskLevel: risk.riskLevel,
        multiplier: risk.positionSizeMultiplier
      });

      return risk;

    } catch (error) {
      logger.error('Error calculating correlation risk', { error: error.message, symbol });
      return {
        maxCorrelation: 0,
        correlatedPositions: [],
        totalCorrelationExposure: 0,
        riskLevel: 'unknown',
        positionSizeMultiplier: 0.5, // Conservative on error
        recommendations: ['Error in correlation calculation - using conservative sizing']
      };
    }
  }

  /**
   * Get correlation between two symbols
   */
  async getCorrelation(symbol1, symbol2) {
    try {
      if (symbol1 === symbol2) return 1.0;

      const cacheKey = this.getCacheKey(symbol1, symbol2);
      const cached = this.correlationCache.get(cacheKey);
      const lastUpdate = this.lastCacheUpdate.get(cacheKey);

      // Use cached value if recent (within 1 hour)
      if (cached && lastUpdate && (Date.now() - lastUpdate) < 60 * 60 * 1000) {
        return cached;
      }

      // Try to get from static correlations first
      let correlation = this.getStaticCorrelation(symbol1, symbol2);

      if (correlation === null) {
        // Calculate dynamic correlation (placeholder for now)
        correlation = await this.calculateDynamicCorrelation(symbol1, symbol2);
      }

      // Cache the result
      this.correlationCache.set(cacheKey, correlation);
      this.lastCacheUpdate.set(cacheKey, Date.now());

      return correlation;

    } catch (error) {
      logger.error('Error getting correlation', { error: error.message, symbol1, symbol2 });
      return 0; // Return 0 correlation on error
    }
  }

  /**
   * Get static correlation from predefined matrix
   */
  getStaticCorrelation(symbol1, symbol2) {
    const correlations1 = this.config.staticCorrelations[symbol1];
    if (correlations1 && correlations1[symbol2] !== undefined) {
      return correlations1[symbol2];
    }

    const correlations2 = this.config.staticCorrelations[symbol2];
    if (correlations2 && correlations2[symbol1] !== undefined) {
      return correlations2[symbol1];
    }

    return null; // No static correlation found
  }

  /**
   * Calculate dynamic correlation (placeholder implementation)
   * In production, this would use historical price data
   */
  async calculateDynamicCorrelation(symbol1, symbol2) {
    try {
      // This is a placeholder implementation
      // In production, you would:
      // 1. Fetch historical price data for both symbols
      // 2. Calculate returns for each symbol
      // 3. Calculate Pearson correlation coefficient

      // For now, return a default correlation based on symbol categories
      const category1 = this.getSymbolCategory(symbol1);
      const category2 = this.getSymbolCategory(symbol2);

      if (category1 === category2) {
        return 0.7; // Same category symbols are moderately correlated
      } else if (category1 === 'major' || category2 === 'major') {
        return 0.5; // Major cryptos have some correlation with others
      } else {
        return 0.3; // Different categories have low correlation
      }

    } catch (error) {
      logger.error('Error calculating dynamic correlation', { error: error.message, symbol1, symbol2 });
      return 0.3; // Default low correlation
    }
  }

  /**
   * Get symbol category for correlation estimation
   */
  getSymbolCategory(symbol) {
    const majorCryptos = ['BTCUSDT', 'ETHUSDT'];
    const altcoins = ['ADAUSDT', 'SOLUSDT', 'DOTUSDT', 'AVAXUSDT'];
    const memeCoins = ['DOGEUSDT'];
    const utilities = ['BNBUSDT'];

    if (majorCryptos.includes(symbol)) return 'major';
    if (altcoins.includes(symbol)) return 'altcoin';
    if (memeCoins.includes(symbol)) return 'meme';
    if (utilities.includes(symbol)) return 'utility';

    return 'other';
  }

  /**
   * Assess correlation risk level and calculate position size multiplier
   */
  assessCorrelationRisk(risk) {
    const assessment = {
      level: 'low',
      multiplier: 1.0,
      recommendations: []
    };

    // Check maximum correlation
    if (risk.maxCorrelation >= 0.9) {
      assessment.level = 'very_high';
      assessment.multiplier = 0.2;
      assessment.recommendations.push('Very high correlation detected - consider avoiding this position');
    } else if (risk.maxCorrelation >= this.config.correlationThreshold) {
      assessment.level = 'high';
      assessment.multiplier = 0.4;
      assessment.recommendations.push('High correlation with existing positions - reduce position size');
    } else if (risk.maxCorrelation >= 0.6) {
      assessment.level = 'medium';
      assessment.multiplier = 0.7;
      assessment.recommendations.push('Moderate correlation detected - consider smaller position');
    }

    // Check number of correlated positions
    const highlyCorrelated = risk.correlatedPositions.filter(p => p.correlation >= 0.7).length;
    if (highlyCorrelated >= this.config.maxCorrelatedPositions) {
      assessment.level = 'high';
      assessment.multiplier = Math.min(assessment.multiplier, 0.3);
      assessment.recommendations.push('Too many highly correlated positions - diversify portfolio');
    }

    // Check total correlation exposure
    if (risk.totalCorrelationExposure >= 0.8) {
      assessment.level = 'high';
      assessment.multiplier = Math.min(assessment.multiplier, 0.4);
      assessment.recommendations.push('High total correlation exposure - reduce correlated positions');
    }

    return assessment;
  }

  /**
   * Generate cache key for correlation pair
   */
  getCacheKey(symbol1, symbol2) {
    return [symbol1, symbol2].sort().join('_');
  }

  /**
   * Calculate portfolio correlation matrix
   */
  async calculatePortfolioCorrelationMatrix(portfolio) {
    try {
      const symbols = Object.keys(portfolio).filter(symbol => portfolio[symbol].size > 0);
      const matrix = {};

      for (let i = 0; i < symbols.length; i++) {
        matrix[symbols[i]] = {};
        for (let j = 0; j < symbols.length; j++) {
          if (i === j) {
            matrix[symbols[i]][symbols[j]] = 1.0;
          } else {
            matrix[symbols[i]][symbols[j]] = await this.getCorrelation(symbols[i], symbols[j]);
          }
        }
      }

      return matrix;

    } catch (error) {
      logger.error('Error calculating portfolio correlation matrix', { error: error.message });
      return {};
    }
  }

  /**
   * Get portfolio diversification score
   */
  async getPortfolioDiversificationScore(portfolio) {
    try {
      const correlationMatrix = await this.calculatePortfolioCorrelationMatrix(portfolio);
      const symbols = Object.keys(correlationMatrix);

      if (symbols.length <= 1) {
        return { score: 100, level: 'excellent' }; // Single position is perfectly diversified
      }

      let totalCorrelation = 0;
      let pairCount = 0;

      for (let i = 0; i < symbols.length; i++) {
        for (let j = i + 1; j < symbols.length; j++) {
          totalCorrelation += correlationMatrix[symbols[i]][symbols[j]];
          pairCount++;
        }
      }

      const avgCorrelation = pairCount > 0 ? totalCorrelation / pairCount : 0;

      // Convert correlation to diversification score (inverse relationship)
      const score = Math.max(0, (1 - avgCorrelation) * 100);

      let level = 'poor';
      if (score >= 80) level = 'excellent';
      else if (score >= 60) level = 'good';
      else if (score >= 40) level = 'fair';

      return {
        score: Math.round(score),
        level,
        avgCorrelation,
        symbolCount: symbols.length,
        pairCount
      };

    } catch (error) {
      logger.error('Error calculating diversification score', { error: error.message });
      return { score: 0, level: 'unknown' };
    }
  }

  /**
   * Get correlation-based position recommendations
   */
  async getPositionRecommendations(portfolio) {
    try {
      const recommendations = [];
      const correlationMatrix = await this.calculatePortfolioCorrelationMatrix(portfolio);

      // Find highly correlated pairs
      const symbols = Object.keys(correlationMatrix);
      for (let i = 0; i < symbols.length; i++) {
        for (let j = i + 1; j < symbols.length; j++) {
          const correlation = correlationMatrix[symbols[i]][symbols[j]];

          if (correlation >= 0.8) {
            recommendations.push({
              type: 'reduce_correlation',
              symbols: [symbols[i], symbols[j]],
              correlation: correlation,
              action: 'Consider reducing position in one of these highly correlated assets',
              priority: 'high'
            });
          }
        }
      }

      // Check for concentration in similar categories
      const categoryExposure = this.calculateCategoryExposure(portfolio);
      for (const [category, exposure] of Object.entries(categoryExposure)) {
        if (exposure.weight > 0.6) {
          recommendations.push({
            type: 'diversify_category',
            category: category,
            exposure: exposure.weight,
            symbols: exposure.symbols,
            action: `High exposure to ${category} category - consider diversifying`,
            priority: 'medium'
          });
        }
      }

      return recommendations;

    } catch (error) {
      logger.error('Error getting position recommendations', { error: error.message });
      return [];
    }
  }

  /**
   * Calculate exposure by symbol category
   */
  calculateCategoryExposure(portfolio) {
    const categoryExposure = {};
    let totalValue = 0;

    // Calculate total portfolio value
    for (const position of Object.values(portfolio)) {
      totalValue += position.value || 0;
    }

    // Calculate category exposures
    for (const [symbol, position] of Object.entries(portfolio)) {
      if (position.size > 0) {
        const category = this.getSymbolCategory(symbol);
        const weight = totalValue > 0 ? (position.value || 0) / totalValue : 0;

        if (!categoryExposure[category]) {
          categoryExposure[category] = { weight: 0, symbols: [], value: 0 };
        }

        categoryExposure[category].weight += weight;
        categoryExposure[category].symbols.push(symbol);
        categoryExposure[category].value += position.value || 0;
      }
    }

    return categoryExposure;
  }

  /**
   * Clear correlation cache
   */
  clearCache() {
    this.correlationCache.clear();
    this.lastCacheUpdate.clear();
    logger.info('Correlation cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      cacheSize: this.correlationCache.size,
      lastUpdates: Array.from(this.lastCacheUpdate.entries()).map(([key, time]) => ({
        pair: key,
        lastUpdate: new Date(time),
        age: Date.now() - time
      }))
    };
  }
}

module.exports = CorrelationRiskCalculator;