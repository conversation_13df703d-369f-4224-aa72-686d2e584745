const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

/**
 * Market Regime Detector - Classifies market conditions
 * Implements requirement 3.2: Market regime classification (trending/sideways/volatile)
 */
class MarketRegimeDetector {
  constructor() {
    this.regimeThresholds = {
      trending: {
        minTrendStrength: 0.6,
        minDirectionalMovement: 0.7,
        maxVolatility: 0.04
      },
      sideways: {
        maxTrendStrength: 0.3,
        maxDirectionalMovement: 0.4,
        maxVolatility: 0.03
      },
      volatile: {
        minVolatility: 0.05,
        maxTrendStrength: 0.5
      }
    };
  }

  /**
   * Detect current market regime
   * Requirement 3.2: Classify market as trending, sideways, or volatile
   */
  async detectMarketRegime(candles) {
    try {
      if (!candles || candles.length < 50) {
        return {
          regime: 'unknown',
          confidence: 0,
          reason: 'Insufficient data for regime detection',
          metrics: {}
        };
      }

      // Calculate regime metrics
      const metrics = this.calculateRegimeMetrics(candles);

      // Classify regime based on metrics
      const classification = this.classifyRegime(metrics);

      // Calculate confidence score
      const confidence = this.calculateConfidence(metrics, classification.regime);

      return {
        regime: classification.regime,
        confidence,
        reason: classification.reason,
        metrics,
        details: {
          trendStrength: metrics.trendStrength,
          volatility: metrics.volatility,
          directionalMovement: metrics.directionalMovement,
          priceRange: metrics.priceRange
        }
      };
    } catch (error) {
      logger.logError('Error detecting market regime:', error.message);
      return {
        regime: 'error',
        confidence: 0,
        reason: 'Error in regime detection',
        metrics: {}
      };
    }
  }

  /**
   * Calculate metrics for regime classification
   */
  calculateRegimeMetrics(candles) {
    const lookback = Math.min(50, candles.length);
    const recentCandles = candles.slice(-lookback);

    // Calculate trend strength using EMA alignment
    const trendStrength = this.calculateTrendStrength(recentCandles);

    // Calculate volatility (standard deviation of returns)
    const volatility = this.calculateVolatility(recentCandles);

    // Calculate directional movement
    const directionalMovement = this.calculateDirectionalMovement(recentCandles);

    // Calculate price range efficiency
    const priceRange = this.calculatePriceRangeEfficiency(recentCandles);

    // Calculate momentum consistency
    const momentum = this.calculateMomentumConsistency(recentCandles);

    // Calculate volume trend
    const volumeTrend = this.calculateVolumeTrend(recentCandles);

    return {
      trendStrength,
      volatility,
      directionalMovement,
      priceRange,
      momentum,
      volumeTrend,
      candleCount: lookback
    };
  }

  /**
   * Calculate trend strength using EMA alignment and slope
   */
  calculateTrendStrength(candles) {
    const closes = candles.map(c => c.close);

    // Calculate EMAs
    const ema20 = this.calculateEMA(closes, 20);
    const ema50 = this.calculateEMA(closes, 50);

    if (!ema20 || !ema50) return 0;

    // EMA alignment score
    const alignmentScore = Math.abs(ema20 - ema50) / ema50;

    // EMA slope consistency
    const ema20Slope = this.calculateSlope(closes.slice(-20));
    const ema50Slope = this.calculateSlope(closes.slice(-50));

    const slopeConsistency = Math.abs(ema20Slope) > 0 && Math.abs(ema50Slope) > 0 &&
                            (ema20Slope * ema50Slope > 0) ? 1 : 0;

    // Price position relative to EMAs
    const currentPrice = closes[closes.length - 1];
    const pricePosition = currentPrice > ema20 && ema20 > ema50 ? 1 :
                         currentPrice < ema20 && ema20 < ema50 ? 1 : 0;

    return Math.min(1, (alignmentScore * 10 + slopeConsistency + pricePosition) / 3);
  }

  /**
   * Calculate market volatility
   */
  calculateVolatility(candles) {
    if (candles.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < candles.length; i++) {
      const returnRate = (candles[i].close - candles[i-1].close) / candles[i-1].close;
      returns.push(returnRate);
    }

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  /**
   * Calculate directional movement efficiency
   */
  calculateDirectionalMovement(candles) {
    if (candles.length < 2) return 0;

    let upMoves = 0;
    let downMoves = 0;
    let totalMoves = 0;

    for (let i = 1; i < candles.length; i++) {
      const move = candles[i].close - candles[i-1].close;
      if (move > 0) upMoves++;
      else if (move < 0) downMoves++;
      totalMoves++;
    }

    // Directional bias (how much one direction dominates)
    const directionalBias = Math.abs(upMoves - downMoves) / totalMoves;

    return directionalBias;
  }

  /**
   * Calculate price range efficiency (net movement vs total movement)
   */
  calculatePriceRangeEfficiency(candles) {
    if (candles.length < 2) return 0;

    const startPrice = candles[0].close;
    const endPrice = candles[candles.length - 1].close;
    const netMovement = Math.abs(endPrice - startPrice);

    // Calculate total movement (sum of all price changes)
    let totalMovement = 0;
    for (let i = 1; i < candles.length; i++) {
      totalMovement += Math.abs(candles[i].close - candles[i-1].close);
    }

    return totalMovement > 0 ? netMovement / totalMovement : 0;
  }

  /**
   * Calculate momentum consistency
   */
  calculateMomentumConsistency(candles) {
    if (candles.length < 10) return 0;

    const closes = candles.map(c => c.close);
    const shortMA = this.calculateSMA(closes.slice(-10), 5);
    const longMA = this.calculateSMA(closes.slice(-20), 10);

    if (!shortMA || !longMA) return 0;

    // Check momentum direction consistency
    let consistentPeriods = 0;
    const totalPeriods = Math.min(10, candles.length - 1);

    for (let i = candles.length - totalPeriods; i < candles.length - 1; i++) {
      const currentMomentum = closes[i+1] - closes[i];
      const maTrend = shortMA - longMA;

      if ((currentMomentum > 0 && maTrend > 0) || (currentMomentum < 0 && maTrend < 0)) {
        consistentPeriods++;
      }
    }

    return consistentPeriods / totalPeriods;
  }

  /**
   * Calculate volume trend
   */
  calculateVolumeTrend(candles) {
    if (candles.length < 10) return 0;

    const volumes = candles.slice(-10).map(c => c.volume);
    return this.calculateSlope(volumes);
  }

  /**
   * Classify regime based on calculated metrics
   */
  classifyRegime(metrics) {
    const { trendStrength, volatility, directionalMovement, priceRange } = metrics;

    // Check for volatile regime first (highest priority)
    if (volatility >= this.regimeThresholds.volatile.minVolatility) {
      return {
        regime: 'volatile',
        reason: `High volatility detected (${(volatility * 100).toFixed(2)}%)`
      };
    }

    // Check for trending regime
    if (trendStrength >= this.regimeThresholds.trending.minTrendStrength &&
        directionalMovement >= this.regimeThresholds.trending.minDirectionalMovement &&
        volatility <= this.regimeThresholds.trending.maxVolatility) {
      return {
        regime: 'trending',
        reason: `Strong trend detected (strength: ${trendStrength.toFixed(2)}, direction: ${directionalMovement.toFixed(2)})`
      };
    }

    // Check for sideways regime
    if (trendStrength <= this.regimeThresholds.sideways.maxTrendStrength &&
        directionalMovement <= this.regimeThresholds.sideways.maxDirectionalMovement &&
        volatility <= this.regimeThresholds.sideways.maxVolatility) {
      return {
        regime: 'sideways',
        reason: `Sideways market detected (low trend strength: ${trendStrength.toFixed(2)})`
      };
    }

    // Default to mixed regime
    return {
      regime: 'mixed',
      reason: 'Market shows mixed characteristics'
    };
  }

  /**
   * Calculate confidence score for regime classification
   */
  calculateConfidence(metrics, regime) {
    let confidence = 0.5; // Base confidence

    switch (regime) {
      case 'trending':
        confidence = Math.min(1,
          (metrics.trendStrength * 0.4) +
          (metrics.directionalMovement * 0.3) +
          (metrics.priceRange * 0.2) +
          ((1 - metrics.volatility * 10) * 0.1)
        );
        break;

      case 'sideways':
        confidence = Math.min(1,
          ((1 - metrics.trendStrength) * 0.4) +
          ((1 - metrics.directionalMovement) * 0.3) +
          ((1 - metrics.volatility * 20) * 0.3)
        );
        break;

      case 'volatile':
        confidence = Math.min(1, metrics.volatility * 20);
        break;

      case 'mixed':
        confidence = 0.3; // Lower confidence for mixed regime
        break;

      default:
        confidence = 0.1;
    }

    return Math.max(0.1, Math.min(1, confidence));
  }

  /**
   * Helper: Calculate EMA
   */
  calculateEMA(prices, period) {
    if (prices.length < period) return null;

    const multiplier = 2 / (period + 1);
    let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  /**
   * Helper: Calculate SMA
   */
  calculateSMA(prices, period) {
    if (prices.length < period) return null;

    const sum = prices.slice(-period).reduce((sum, price) => sum + price, 0);
    return sum / period;
  }

  /**
   * Helper: Calculate slope of a data series
   */
  calculateSlope(data) {
    if (data.length < 2) return 0;

    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    const n = data.length;

    for (let i = 0; i < n; i++) {
      sumX += i;
      sumY += data[i];
      sumXY += i * data[i];
      sumXX += i * i;
    }

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return isNaN(slope) ? 0 : slope;
  }

  /**
   * Get regime trading recommendations
   */
  getRegimeRecommendations(regime, confidence) {
    const recommendations = {
      trending: {
        strategy: 'trend_following',
        riskMultiplier: 1.2,
        filters: ['momentum_confirmation', 'volume_confirmation'],
        advice: 'Favor trend-following strategies, increase position size for strong trends'
      },
      sideways: {
        strategy: 'mean_reversion',
        riskMultiplier: 0.6,
        filters: ['range_boundaries', 'oscillator_signals'],
        advice: 'Use mean reversion strategies, reduce position size, focus on range boundaries'
      },
      volatile: {
        strategy: 'avoid_trading',
        riskMultiplier: 0.3,
        filters: ['high_confidence_only', 'tight_stops'],
        advice: 'Avoid trading or use very tight risk management'
      },
      mixed: {
        strategy: 'adaptive',
        riskMultiplier: 0.8,
        filters: ['multiple_confirmations'],
        advice: 'Use adaptive strategies with multiple confirmations'
      }
    };

    const baseRec = recommendations[regime] || recommendations.mixed;

    // Adjust recommendations based on confidence
    const confidenceMultiplier = confidence > 0.7 ? 1.2 : confidence < 0.4 ? 0.8 : 1.0;

    return {
      ...baseRec,
      riskMultiplier: baseRec.riskMultiplier * confidenceMultiplier,
      confidence
    };
  }
}

module.exports = MarketRegimeDetector;