const logger = require('../logger');

/**
 * Performance Metrics Calculator
 * Calculates advanced performance metrics including Sharpe ratio, Sortino ratio,
 * maximum drawdown, Calmar ratio, and other trading performance indicators
 */
class PerformanceMetricsCalculator {
  constructor(config = {}) {
    this.config = {
      riskFreeRate: config.riskFreeRate || 0.02, // 2% annual risk-free rate
      tradingDaysPerYear: config.tradingDaysPerYear || 365,
      benchmarkReturn: config.benchmarkReturn || 0.08, // 8% annual benchmark
      ...config
    };
  }

  /**
   * Calculate comprehensive performance metrics for a set of trades
   */
  calculateComprehensiveMetrics(trades, portfolioHistory = []) {
    try {
      if (!trades || trades.length === 0) {
        return this.getEmptyMetrics();
      }

      const basicMetrics = this.calculateBasicMetrics(trades);
      const riskMetrics = this.calculateRiskMetrics(trades, portfolioHistory);
      const advancedMetrics = this.calculateAdvancedMetrics(trades);
      const drawdownMetrics = this.calculateDrawdownMetrics(trades, portfolioHistory);
      const consistencyMetrics = this.calculateConsistencyMetrics(trades);

      return {
        ...basicMetrics,
        ...riskMetrics,
        ...advancedMetrics,
        ...drawdownMetrics,
        ...consistencyMetrics,
        calculationDate: new Date(),
        totalTrades: trades.length
      };

    } catch (error) {
      logger.error('Error calculating comprehensive metrics', { error: error.message });
      return this.getEmptyMetrics();
    }
  }

  /**
   * Calculate basic performance metrics
   */
  calculateBasicMetrics(trades) {
    const winningTrades = trades.filter(t => (t.pnlPercent || 0) > 0);
    const losingTrades = trades.filter(t => (t.pnlPercent || 0) < 0);
    
    const totalTrades = trades.length;
    const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
    
    const totalPnL = trades.reduce((sum, t) => sum + (t.pnlPercent || 0), 0);
    const avgWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / winningTrades.length 
      : 0;
    const avgLoss = losingTrades.length > 0 
      ? Math.abs(losingTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / losingTrades.length)
      : 0;
    
    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0;
    const expectancy = (winRate / 100) * avgWin - ((100 - winRate) / 100) * avgLoss;

    return {
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: Math.round(winRate * 100) / 100,
      totalPnL: Math.round(totalPnL * 100) / 100,
      avgWin: Math.round(avgWin * 100) / 100,
      avgLoss: Math.round(avgLoss * 100) / 100,
      profitFactor: Math.round(profitFactor * 100) / 100,
      expectancy: Math.round(expectancy * 100) / 100,
      largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnlPercent)) : 0,
      largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnlPercent)) : 0
    };
  }

  /**
   * Calculate risk-adjusted metrics
   */
  calculateRiskMetrics(trades, portfolioHistory = []) {
    const returns = trades.map(t => (t.pnlPercent || 0) / 100);
    
    if (returns.length === 0) {
      return { sharpeRatio: 0, sortinoRatio: 0, calmarRatio: 0 };
    }

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const annualizedReturn = avgReturn * this.config.tradingDaysPerYear;
    const riskFreeRate = this.config.riskFreeRate;

    // Sharpe Ratio
    const stdDev = this.calculateStandardDeviation(returns);
    const annualizedStdDev = stdDev * Math.sqrt(this.config.tradingDaysPerYear);
    const sharpeRatio = annualizedStdDev > 0 
      ? (annualizedReturn - riskFreeRate) / annualizedStdDev 
      : 0;

    // Sortino Ratio (downside deviation)
    const downsideReturns = returns.filter(r => r < 0);
    const downsideStdDev = downsideReturns.length > 0 
      ? this.calculateStandardDeviation(downsideReturns) 
      : 0;
    const annualizedDownsideStdDev = downsideStdDev * Math.sqrt(this.config.tradingDaysPerYear);
    const sortinoRatio = annualizedDownsideStdDev > 0 
      ? (annualizedReturn - riskFreeRate) / annualizedDownsideStdDev 
      : 0;

    // Calmar Ratio (return/max drawdown)
    const maxDrawdown = this.calculateMaxDrawdown(trades, portfolioHistory);
    const calmarRatio = maxDrawdown > 0 ? annualizedReturn / (maxDrawdown / 100) : 0;

    return {
      sharpeRatio: Math.round(sharpeRatio * 1000) / 1000,
      sortinoRatio: Math.round(sortinoRatio * 1000) / 1000,
      calmarRatio: Math.round(calmarRatio * 1000) / 1000,
      annualizedReturn: Math.round(annualizedReturn * 10000) / 100, // As percentage
      volatility: Math.round(annualizedStdDev * 10000) / 100 // As percentage
    };
  }

  /**
   * Calculate advanced performance metrics
   */
  calculateAdvancedMetrics(trades) {
    if (trades.length === 0) {
      return { 
        informationRatio: 0, 
        treynorRatio: 0, 
        jensenAlpha: 0,
        trackingError: 0,
        upCaptureRatio: 0,
        downCaptureRatio: 0
      };
    }

    const returns = trades.map(t => (t.pnlPercent || 0) / 100);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const annualizedReturn = avgReturn * this.config.tradingDaysPerYear;

    // Information Ratio (excess return / tracking error)
    const benchmarkReturn = this.config.benchmarkReturn;
    const excessReturn = annualizedReturn - benchmarkReturn;
    const trackingError = this.calculateStandardDeviation(returns) * Math.sqrt(this.config.tradingDaysPerYear);
    const informationRatio = trackingError > 0 ? excessReturn / trackingError : 0;

    // Jensen's Alpha (simplified - assumes beta = 1)
    const riskFreeRate = this.config.riskFreeRate;
    const jensenAlpha = annualizedReturn - (riskFreeRate + 1 * (benchmarkReturn - riskFreeRate));

    // Capture ratios (simplified)
    const positiveReturns = returns.filter(r => r > 0);
    const negativeReturns = returns.filter(r => r < 0);
    
    const avgPositiveReturn = positiveReturns.length > 0 
      ? positiveReturns.reduce((sum, r) => sum + r, 0) / positiveReturns.length 
      : 0;
    const avgNegativeReturn = negativeReturns.length > 0 
      ? negativeReturns.reduce((sum, r) => sum + r, 0) / negativeReturns.length 
      : 0;

    const upCaptureRatio = benchmarkReturn > 0 ? (avgPositiveReturn * this.config.tradingDaysPerYear) / benchmarkReturn : 0;
    const downCaptureRatio = benchmarkReturn > 0 && avgNegativeReturn < 0 
      ? Math.abs(avgNegativeReturn * this.config.tradingDaysPerYear) / benchmarkReturn 
      : 0;

    return {
      informationRatio: Math.round(informationRatio * 1000) / 1000,
      treynorRatio: Math.round((annualizedReturn - riskFreeRate) * 1000) / 1000, // Simplified (beta = 1)
      jensenAlpha: Math.round(jensenAlpha * 10000) / 100, // As percentage
      trackingError: Math.round(trackingError * 10000) / 100, // As percentage
      upCaptureRatio: Math.round(upCaptureRatio * 1000) / 1000,
      downCaptureRatio: Math.round(downCaptureRatio * 1000) / 1000
    };
  }

  /**
   * Calculate drawdown metrics
   */
  calculateDrawdownMetrics(trades, portfolioHistory = []) {
    if (trades.length === 0) {
      return {
        maxDrawdown: 0,
        avgDrawdown: 0,
        drawdownDuration: 0,
        recoveryFactor: 0,
        ulcerIndex: 0
      };
    }

    // Calculate running portfolio value
    let runningValue = 100; // Start with 100%
    const portfolioValues = [runningValue];
    
    for (const trade of trades) {
      runningValue *= (1 + (trade.pnlPercent || 0) / 100);
      portfolioValues.push(runningValue);
    }

    // Calculate drawdowns
    const drawdowns = [];
    let peak = portfolioValues[0];
    let drawdownStart = 0;
    let inDrawdown = false;

    for (let i = 1; i < portfolioValues.length; i++) {
      if (portfolioValues[i] > peak) {
        if (inDrawdown) {
          // End of drawdown period
          drawdowns.push({
            start: drawdownStart,
            end: i - 1,
            duration: i - 1 - drawdownStart,
            depth: ((peak - Math.min(...portfolioValues.slice(drawdownStart, i))) / peak) * 100
          });
          inDrawdown = false;
        }
        peak = portfolioValues[i];
      } else if (!inDrawdown && portfolioValues[i] < peak) {
        // Start of new drawdown
        drawdownStart = i;
        inDrawdown = true;
      }
    }

    // Handle ongoing drawdown
    if (inDrawdown) {
      drawdowns.push({
        start: drawdownStart,
        end: portfolioValues.length - 1,
        duration: portfolioValues.length - 1 - drawdownStart,
        depth: ((peak - Math.min(...portfolioValues.slice(drawdownStart))) / peak) * 100
      });
    }

    const maxDrawdown = drawdowns.length > 0 ? Math.max(...drawdowns.map(d => d.depth)) : 0;
    const avgDrawdown = drawdowns.length > 0 
      ? drawdowns.reduce((sum, d) => sum + d.depth, 0) / drawdowns.length 
      : 0;
    const avgDrawdownDuration = drawdowns.length > 0 
      ? drawdowns.reduce((sum, d) => sum + d.duration, 0) / drawdowns.length 
      : 0;

    // Recovery Factor
    const totalReturn = portfolioValues[portfolioValues.length - 1] - 100;
    const recoveryFactor = maxDrawdown > 0 ? totalReturn / maxDrawdown : 0;

    // Ulcer Index (average squared drawdown)
    const squaredDrawdowns = portfolioValues.map((value, i) => {
      let currentPeak = Math.max(...portfolioValues.slice(0, i + 1));
      let drawdown = ((currentPeak - value) / currentPeak) * 100;
      return drawdown * drawdown;
    });
    const ulcerIndex = Math.sqrt(squaredDrawdowns.reduce((sum, sq) => sum + sq, 0) / squaredDrawdowns.length);

    return {
      maxDrawdown: Math.round(maxDrawdown * 100) / 100,
      avgDrawdown: Math.round(avgDrawdown * 100) / 100,
      drawdownDuration: Math.round(avgDrawdownDuration * 100) / 100,
      recoveryFactor: Math.round(recoveryFactor * 100) / 100,
      ulcerIndex: Math.round(ulcerIndex * 100) / 100,
      totalDrawdowns: drawdowns.length
    };
  }

  /**
   * Calculate consistency metrics
   */
  calculateConsistencyMetrics(trades) {
    if (trades.length === 0) {
      return {
        consistencyRatio: 0,
        gainToPainRatio: 0,
        kRatio: 0,
        stabilityRatio: 0
      };
    }

    const returns = trades.map(t => (t.pnlPercent || 0) / 100);
    const positiveReturns = returns.filter(r => r > 0);
    const negativeReturns = returns.filter(r => r < 0);

    // Consistency Ratio (percentage of positive periods)
    const consistencyRatio = (positiveReturns.length / returns.length) * 100;

    // Gain-to-Pain Ratio
    const totalGain = positiveReturns.reduce((sum, r) => sum + r, 0);
    const totalPain = Math.abs(negativeReturns.reduce((sum, r) => sum + r, 0));
    const gainToPainRatio = totalPain > 0 ? totalGain / totalPain : 0;

    // K-Ratio (simplified linear regression slope)
    const kRatio = this.calculateKRatio(returns);

    // Stability Ratio (inverse of coefficient of variation)
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const stdDev = this.calculateStandardDeviation(returns);
    const stabilityRatio = stdDev > 0 && avgReturn > 0 ? avgReturn / stdDev : 0;

    return {
      consistencyRatio: Math.round(consistencyRatio * 100) / 100,
      gainToPainRatio: Math.round(gainToPainRatio * 1000) / 1000,
      kRatio: Math.round(kRatio * 1000) / 1000,
      stabilityRatio: Math.round(stabilityRatio * 1000) / 1000
    };
  }

  /**
   * Calculate standard deviation
   */
  calculateStandardDeviation(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, sq) => sum + sq, 0) / values.length;
    
    return Math.sqrt(avgSquaredDiff);
  }

  /**
   * Calculate maximum drawdown from trades
   */
  calculateMaxDrawdown(trades, portfolioHistory = []) {
    if (trades.length === 0) return 0;

    let runningValue = 100;
    let peak = runningValue;
    let maxDrawdown = 0;

    for (const trade of trades) {
      runningValue *= (1 + (trade.pnlPercent || 0) / 100);
      
      if (runningValue > peak) {
        peak = runningValue;
      } else {
        const drawdown = ((peak - runningValue) / peak) * 100;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  /**
   * Calculate K-Ratio (simplified)
   */
  calculateKRatio(returns) {
    if (returns.length < 2) return 0;

    // Simple linear regression to find slope
    const n = returns.length;
    const x = Array.from({ length: n }, (_, i) => i + 1);
    const y = returns.map((r, i) => returns.slice(0, i + 1).reduce((sum, ret) => sum + ret, 0));

    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    // Calculate standard error of slope
    const yPredicted = x.map(xi => (slope * xi) + ((sumY - slope * sumX) / n));
    const residuals = y.map((yi, i) => yi - yPredicted[i]);
    const mse = residuals.reduce((sum, r) => sum + r * r, 0) / (n - 2);
    const slopeStdError = Math.sqrt(mse / (sumXX - (sumX * sumX) / n));

    return slopeStdError > 0 ? slope / slopeStdError : 0;
  }

  /**
   * Get empty metrics structure
   */
  getEmptyMetrics() {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalPnL: 0,
      avgWin: 0,
      avgLoss: 0,
      profitFactor: 0,
      expectancy: 0,
      largestWin: 0,
      largestLoss: 0,
      sharpeRatio: 0,
      sortinoRatio: 0,
      calmarRatio: 0,
      annualizedReturn: 0,
      volatility: 0,
      informationRatio: 0,
      treynorRatio: 0,
      jensenAlpha: 0,
      trackingError: 0,
      upCaptureRatio: 0,
      downCaptureRatio: 0,
      maxDrawdown: 0,
      avgDrawdown: 0,
      drawdownDuration: 0,
      recoveryFactor: 0,
      ulcerIndex: 0,
      totalDrawdowns: 0,
      consistencyRatio: 0,
      gainToPainRatio: 0,
      kRatio: 0,
      stabilityRatio: 0,
      calculationDate: new Date()
    };
  }
}

module.exports = PerformanceMetricsCalculator;
