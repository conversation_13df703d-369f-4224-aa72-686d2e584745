const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

/**
 * Signal Strength Classifier - Rates signal quality as weak/medium/strong
 * Implements requirement 3.3: Signal strength rating
 */
class SignalStrengthClassifier {
  constructor() {
    this.strengthThresholds = {
      strong: 0.75,   // 75% and above
      medium: 0.50,   // 50-74%
      weak: 0.25      // 25-49%, below 25% is rejected
    };

    this.weights = {
      technicalAlignment: 0.25,    // EMA alignment, MACD, RSI
      patternStrength: 0.20,       // Candlestick patterns, body strength
      momentumConfirmation: 0.20,  // Price momentum, slope consistency
      volumeSupport: 0.15,         // Volume confirmation
      marketStructure: 0.10,       // Support/resistance levels
      riskReward: 0.10            // Risk/reward potential
    };
  }

  /**
   * Classify signal strength based on multiple factors
   * Requirement 3.3: Rate signal quality (weak/medium/strong)
   */
  async classifySignalStrength(signal, candles) {
    try {
      if (!signal || !candles || candles.length < 20) {
        return {
          strength: 'insufficient_data',
          score: 0,
          reason: 'Insufficient data for strength classification',
          breakdown: {}
        };
      }

      // Calculate strength components
      const components = this.calculateStrengthComponents(signal, candles);

      // Calculate weighted score
      const score = this.calculateWeightedScore(components);

      // Classify strength based on score
      const strength = this.classifyByScore(score);

      // Generate detailed breakdown
      const breakdown = this.generateBreakdown(components, score);

      return {
        strength,
        score,
        reason: this.getStrengthReason(strength, score),
        breakdown,
        components
      };
    } catch (error) {
      logger.logError('Error classifying signal strength:', error.message);
      return {
        strength: 'error',
        score: 0,
        reason: 'Error in strength classification',
        breakdown: {}
      };
    }
  }

  /**
   * Calculate individual strength components
   */
  calculateStrengthComponents(signal, candles) {
    return {
      technicalAlignment: this.assessTechnicalAlignment(signal, candles),
      patternStrength: this.assessPatternStrength(signal, candles),
      momentumConfirmation: this.assessMomentumConfirmation(signal, candles),
      volumeSupport: this.assessVolumeSupport(signal, candles),
      marketStructure: this.assessMarketStructure(signal, candles),
      riskReward: this.assessRiskReward(signal)
    };
  }

  /**
   * Assess technical indicator alignment
   */
  assessTechnicalAlignment(signal, candles) {
    let score = 0;
    let maxScore = 0;

    // EMA alignment (0-30 points)
    if (signal.indicators) {
      const { ema50, ema200 } = signal.indicators;
      const currentPrice = signal.entry;

      if (signal.type === 'BUY') {
        if (currentPrice > ema50 && ema50 > ema200) score += 30;
        else if (currentPrice > ema50 || ema50 > ema200) score += 15;
      } else {
        if (currentPrice < ema50 && ema50 < ema200) score += 30;
        else if (currentPrice < ema50 || ema50 < ema200) score += 15;
      }
      maxScore += 30;

      // MACD confirmation (0-25 points)
      if (signal.indicators.macd) {
        const { macd, signal: macdSignal, histogram } = signal.indicators.macd;

        if (signal.type === 'BUY') {
          if (macd > macdSignal && histogram > 0) score += 25;
          else if (macd > macdSignal || histogram > 0) score += 12;
        } else {
          if (macd < macdSignal && histogram < 0) score += 25;
          else if (macd < macdSignal || histogram < 0) score += 12;
        }
      }
      maxScore += 25;

      // RSI confirmation (0-20 points)
      if (signal.indicators.rsi) {
        const rsi = signal.indicators.rsi;

        if (signal.type === 'BUY' && rsi >= 50 && rsi <= 70) score += 20;
        else if (signal.type === 'SELL' && rsi >= 30 && rsi <= 50) score += 20;
        else if ((signal.type === 'BUY' && rsi > 40) || (signal.type === 'SELL' && rsi < 60)) score += 10;
      }
      maxScore += 20;
    }

    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Assess candlestick pattern strength
   */
  assessPatternStrength(signal, candles) {
    let score = 0;
    let maxScore = 0;

    // Strong body pattern (0-40 points)
    if (signal.indicators && signal.indicators.strongBody) {
      const { isStrong, bodyPercent } = signal.indicators.strongBody;

      if (isStrong && bodyPercent >= 70) score += 40;
      else if (isStrong && bodyPercent >= 60) score += 30;
      else if (bodyPercent >= 50) score += 20;
      else if (bodyPercent >= 40) score += 10;
    }
    maxScore += 40;

    // Engulfing pattern (0-35 points)
    if (signal.indicators && signal.indicators.engulfing) {
      const engulfing = signal.indicators.engulfing;

      if ((signal.type === 'BUY' && engulfing === 'bullish') ||
          (signal.type === 'SELL' && engulfing === 'bearish')) {
        score += 35;
      } else if (engulfing !== 'none') {
        score += 10; // Partial credit for any engulfing pattern
      }
    }
    maxScore += 35;

    // Pattern consistency (0-25 points)
    const patternConsistency = this.checkPatternConsistency(candles, signal.type);
    score += patternConsistency * 25;
    maxScore += 25;

    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Assess momentum confirmation
   */
  assessMomentumConfirmation(signal, candles) {
    let score = 0;
    let maxScore = 0;

    // Price momentum (0-40 points)
    const priceMomentum = this.calculatePriceMomentum(candles, signal.type);
    score += priceMomentum * 40;
    maxScore += 40;

    // EMA slope consistency (0-35 points)
    const slopeConsistency = this.calculateSlopeConsistency(candles, signal.type);
    score += slopeConsistency * 35;
    maxScore += 35;

    // Momentum acceleration (0-25 points)
    const acceleration = this.calculateMomentumAcceleration(candles);
    score += acceleration * 25;
    maxScore += 25;

    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Assess volume support for the signal
   */
  assessVolumeSupport(signal, candles) {
    if (candles.length < 10) return 0;

    let score = 0;
    let maxScore = 0;

    // Current volume vs average (0-50 points)
    const recentVolumes = candles.slice(-10).map(c => c.volume);
    const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
    const currentVolume = candles[candles.length - 1].volume;
    const volumeRatio = currentVolume / avgVolume;

    if (volumeRatio >= 2.0) score += 50;
    else if (volumeRatio >= 1.5) score += 35;
    else if (volumeRatio >= 1.2) score += 20;
    else if (volumeRatio >= 1.0) score += 10;
    maxScore += 50;

    // Volume trend (0-30 points)
    const volumeTrend = this.calculateVolumeTrend(candles.slice(-5));
    if (volumeTrend > 0) score += 30;
    else if (volumeTrend > -0.1) score += 15;
    maxScore += 30;

    // Volume-price relationship (0-20 points)
    const volumePriceRelation = this.assessVolumePriceRelation(candles, signal.type);
    score += volumePriceRelation * 20;
    maxScore += 20;

    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Assess market structure (support/resistance)
   */
  assessMarketStructure(signal, candles) {
    let score = 0;
    let maxScore = 0;

    try {
      const indicators = require('./indicators');
      const { supports, resistances } = indicators.findAdvancedSupportResistance(candles, 50);

      // Distance from key levels (0-60 points)
      const entryPrice = signal.entry;
      const tolerance = entryPrice * 0.02; // 2% tolerance

      let nearKeyLevel = false;
      let levelStrength = 0;

      // Check resistance levels for BUY signals
      if (signal.type === 'BUY') {
        for (const resistance of resistances.slice(0, 3)) {
          const distance = resistance.price - entryPrice;
          if (distance > tolerance && distance < entryPrice * 0.05) { // 2-5% above entry
            score += Math.min(30, resistance.strength * 10);
            levelStrength = Math.max(levelStrength, resistance.strength);
            nearKeyLevel = true;
            break;
          }
        }
      } else {
        // Check support levels for SELL signals
        for (const support of supports.slice(0, 3)) {
          const distance = entryPrice - support.price;
          if (distance > tolerance && distance < entryPrice * 0.05) { // 2-5% below entry
            score += Math.min(30, support.strength * 10);
            levelStrength = Math.max(levelStrength, support.strength);
            nearKeyLevel = true;
            break;
          }
        }
      }
      maxScore += 60;

      // Clear path to target (0-40 points)
      const clearPath = this.assessClearPathToTarget(signal, supports, resistances);
      score += clearPath * 40;
      maxScore += 40;

    } catch (error) {
      logger.logError('Error assessing market structure:', error.message);
    }

    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Assess risk/reward potential
   */
  assessRiskReward(signal) {
    let score = 0;

    if (signal.riskReward) {
      const rr = signal.riskReward;

      if (rr >= 3.0) score = 100;
      else if (rr >= 2.5) score = 85;
      else if (rr >= 2.0) score = 70;
      else if (rr >= 1.5) score = 50;
      else if (rr >= 1.2) score = 30;
      else score = 10;
    }

    return score / 100;
  }

  /**
   * Calculate weighted score from components
   */
  calculateWeightedScore(components) {
    let totalScore = 0;

    for (const [component, weight] of Object.entries(this.weights)) {
      if (components[component] !== undefined) {
        totalScore += components[component] * weight;
      }
    }

    return Math.max(0, Math.min(1, totalScore));
  }

  /**
   * Classify strength based on score
   */
  classifyByScore(score) {
    if (score >= this.strengthThresholds.strong) {
      return 'strong';
    } else if (score >= this.strengthThresholds.medium) {
      return 'medium';
    } else if (score >= this.strengthThresholds.weak) {
      return 'weak';
    } else {
      return 'very_weak';
    }
  }

  /**
   * Generate detailed breakdown
   */
  generateBreakdown(components, totalScore) {
    const breakdown = {};

    for (const [component, score] of Object.entries(components)) {
      const weight = this.weights[component] || 0;
      breakdown[component] = {
        score: Math.round(score * 100),
        weight: Math.round(weight * 100),
        contribution: Math.round(score * weight * 100),
        rating: this.getRatingFromScore(score)
      };
    }

    breakdown.total = {
      score: Math.round(totalScore * 100),
      rating: this.classifyByScore(totalScore)
    };

    return breakdown;
  }

  /**
   * Get strength reason
   */
  getStrengthReason(strength, score) {
    const scorePercent = Math.round(score * 100);

    const reasons = {
      strong: `Strong signal with ${scorePercent}% confidence - Multiple confirmations aligned`,
      medium: `Medium strength signal with ${scorePercent}% confidence - Good setup with some confirmations`,
      weak: `Weak signal with ${scorePercent}% confidence - Limited confirmations, higher risk`,
      very_weak: `Very weak signal with ${scorePercent}% confidence - Insufficient confirmations`,
      insufficient_data: 'Not enough data to classify signal strength',
      error: 'Error occurred during strength classification'
    };

    return reasons[strength] || `Signal strength: ${strength} (${scorePercent}%)`;
  }

  /**
   * Helper methods
   */

  checkPatternConsistency(candles, signalType) {
    if (candles.length < 5) return 0;

    const recentCandles = candles.slice(-5);
    let consistentCandles = 0;

    for (const candle of recentCandles) {
      const isBullish = candle.close > candle.open;
      if ((signalType === 'BUY' && isBullish) || (signalType === 'SELL' && !isBullish)) {
        consistentCandles++;
      }
    }

    return consistentCandles / recentCandles.length;
  }

  calculatePriceMomentum(candles, signalType) {
    if (candles.length < 10) return 0;

    const closes = candles.slice(-10).map(c => c.close);
    const shortMA = closes.slice(-5).reduce((sum, price) => sum + price, 0) / 5;
    const longMA = closes.reduce((sum, price) => sum + price, 0) / 10;

    const momentum = (shortMA - longMA) / longMA;

    if (signalType === 'BUY') {
      return Math.max(0, Math.min(1, momentum * 10));
    } else {
      return Math.max(0, Math.min(1, -momentum * 10));
    }
  }

  calculateSlopeConsistency(candles, signalType) {
    if (candles.length < 10) return 0;

    const closes = candles.slice(-10).map(c => c.close);
    const slope = this.calculateSlope(closes);

    if (signalType === 'BUY') {
      return slope > 0 ? Math.min(1, slope * 1000) : 0;
    } else {
      return slope < 0 ? Math.min(1, -slope * 1000) : 0;
    }
  }

  calculateMomentumAcceleration(candles) {
    if (candles.length < 15) return 0;

    const closes = candles.map(c => c.close);
    const recentSlope = this.calculateSlope(closes.slice(-5));
    const olderSlope = this.calculateSlope(closes.slice(-10, -5));

    const acceleration = Math.abs(recentSlope) - Math.abs(olderSlope);
    return Math.max(0, Math.min(1, acceleration * 10000));
  }

  calculateVolumeTrend(candles) {
    if (candles.length < 2) return 0;

    const volumes = candles.map(c => c.volume);
    return this.calculateSlope(volumes);
  }

  assessVolumePriceRelation(candles, signalType) {
    if (candles.length < 3) return 0;

    const recent = candles.slice(-3);
    let correlation = 0;

    for (let i = 1; i < recent.length; i++) {
      const priceChange = recent[i].close - recent[i-1].close;
      const volumeChange = recent[i].volume - recent[i-1].volume;

      if (signalType === 'BUY') {
        if (priceChange > 0 && volumeChange > 0) correlation += 1;
      } else {
        if (priceChange < 0 && volumeChange > 0) correlation += 1;
      }
    }

    return correlation / (recent.length - 1);
  }

  assessClearPathToTarget(signal, supports, resistances) {
    const entryPrice = signal.entry;
    const targetPrice = signal.takeProfit;

    let obstacleCount = 0;
    const tolerance = entryPrice * 0.01; // 1% tolerance

    if (signal.type === 'BUY') {
      // Check for resistance levels between entry and target
      for (const resistance of resistances) {
        if (resistance.price > entryPrice + tolerance &&
            resistance.price < targetPrice - tolerance) {
          obstacleCount += resistance.strength;
        }
      }
    } else {
      // Check for support levels between entry and target
      for (const support of supports) {
        if (support.price < entryPrice - tolerance &&
            support.price > targetPrice + tolerance) {
          obstacleCount += support.strength;
        }
      }
    }

    return Math.max(0, 1 - (obstacleCount * 0.2));
  }

  calculateSlope(data) {
    if (data.length < 2) return 0;

    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    const n = data.length;

    for (let i = 0; i < n; i++) {
      sumX += i;
      sumY += data[i];
      sumXY += i * data[i];
      sumXX += i * i;
    }

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return isNaN(slope) ? 0 : slope;
  }

  getRatingFromScore(score) {
    if (score >= 0.8) return 'excellent';
    if (score >= 0.6) return 'good';
    if (score >= 0.4) return 'fair';
    if (score >= 0.2) return 'poor';
    return 'very_poor';
  }
}

module.exports = SignalStrengthClassifier;