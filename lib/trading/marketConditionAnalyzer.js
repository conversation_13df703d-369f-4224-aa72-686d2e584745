// BinanceClient will be required in constructor

class MarketConditionAnalyzer {
  constructor() {
    this.binanceClient = require('./binanceClient');
    this.conditionCache = new Map(); // symbol -> condition data
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * <PERSON>ân tích điều kiện thị trường cho symbol
   */
  async analyzeMarketCondition(symbol, timeframe = '1h') {
    try {
      const cacheKey = `${symbol}_${timeframe}`;
      const cached = this.conditionCache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.condition;
      }

      // Lấy 100 candles để phân tích
      const candles = await this.binanceClient.getKlines(symbol, timeframe, 100);

      if (!candles || candles.length < 50) {
        return { condition: 'unknown', confidence: 0 };
      }

      const condition = this.detectMarketCondition(candles);

      // Cache kết quả
      this.conditionCache.set(cacheKey, {
        condition,
        timestamp: Date.now()
      });

      return condition;
    } catch (error) {
      logger.logError(`Error analyzing market condition for ${symbol}:`, error.message);
      return { condition: 'unknown', confidence: 0 };
    }
  }

  /**
   * Detect market condition từ candle data
   */
  detectMarketCondition(candles) {
    const closes = candles.map(c => parseFloat(c.close));
    const highs = candles.map(c => parseFloat(c.high));
    const lows = candles.map(c => parseFloat(c.low));
    const volumes = candles.map(c => parseFloat(c.volume));

    // 1. Tính volatility
    const volatility = this.calculateVolatility(closes);

    // 2. Tính trend strength
    const trendStrength = this.calculateTrendStrength(closes);

    // 3. Detect sideways market
    const sidewaysInfo = this.detectSidewaysMarket(closes, highs, lows);

    // 4. Detect false breakout patterns
    const falseBreakouts = this.detectFalseBreakouts(candles);

    // 5. Volume analysis
    const volumeProfile = this.analyzeVolume(volumes);

    // 6. Support/Resistance strength
    const srStrength = this.analyzeSupportResistance(highs, lows, closes);

    // Determine overall condition
    let condition = 'normal';
    let confidence = 0.5;
    let riskLevel = 'medium';
    let recommendations = [];

    // SIDEWAYS MARKET Detection
    if (sidewaysInfo.isSideways && sidewaysInfo.confidence > 0.7) {
      condition = 'sideways';
      confidence = sidewaysInfo.confidence;
      riskLevel = 'high';
      recommendations.push('Reduce signal frequency');
      recommendations.push('Increase entry confirmation requirements');
      recommendations.push('Use tighter stop losses');
    }

    // HIGH VOLATILITY Detection
    else if (volatility > 3.0) {
      condition = 'high_volatility';
      confidence = Math.min(volatility / 5.0, 1.0);
      riskLevel = 'very_high';
      recommendations.push('Avoid trading during extreme volatility');
      recommendations.push('Wait for volatility to normalize');
    }

    // STRONG TREND Detection
    else if (trendStrength.strength > 0.8) {
      condition = trendStrength.direction === 'up' ? 'strong_uptrend' : 'strong_downtrend';
      confidence = trendStrength.strength;
      riskLevel = 'low';
      recommendations.push('Follow trend direction');
      recommendations.push('Use wider take profits');
    }

    // FALSE BREAKOUT PRONE
    else if (falseBreakouts.frequency > 0.3) {
      condition = 'false_breakout_prone';
      confidence = falseBreakouts.frequency;
      riskLevel = 'high';
      recommendations.push('Wait for stronger confirmation');
      recommendations.push('Avoid breakout trades');
      recommendations.push('Use smaller position sizes');
    }

    return {
      condition,
      confidence,
      riskLevel,
      recommendations,
      metrics: {
        volatility,
        trendStrength,
        sidewaysInfo,
        falseBreakouts,
        volumeProfile,
        srStrength
      }
    };
  }

  /**
   * Tính volatility (standard deviation of returns)
   */
  calculateVolatility(closes) {
    if (closes.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < closes.length; i++) {
      returns.push((closes[i] - closes[i-1]) / closes[i-1] * 100);
    }

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  /**
   * Tính trend strength
   */
  calculateTrendStrength(closes) {
    if (closes.length < 20) return { strength: 0, direction: 'neutral' };

    const recent = closes.slice(-20);
    const older = closes.slice(-40, -20);

    const recentAvg = recent.reduce((sum, c) => sum + c, 0) / recent.length;
    const olderAvg = older.reduce((sum, c) => sum + c, 0) / older.length;

    const change = (recentAvg - olderAvg) / olderAvg;
    const strength = Math.min(Math.abs(change) * 10, 1.0);
    const direction = change > 0 ? 'up' : change < 0 ? 'down' : 'neutral';

    return { strength, direction, change };
  }

  /**
   * Detect sideways market
   */
  detectSidewaysMarket(closes, highs, lows) {
    if (closes.length < 50) return { isSideways: false, confidence: 0 };

    const recentData = closes.slice(-50);
    const recentHighs = highs.slice(-50);
    const recentLows = lows.slice(-50);

    const maxPrice = Math.max(...recentHighs);
    const minPrice = Math.min(...recentLows);
    const priceRange = (maxPrice - minPrice) / minPrice * 100;

    // Tính số lần price test support/resistance
    const supportLevel = minPrice * 1.005; // 0.5% buffer
    const resistanceLevel = maxPrice * 0.995;

    let supportTests = 0;
    let resistanceTests = 0;

    for (let i = 0; i < recentLows.length; i++) {
      if (recentLows[i] <= supportLevel) supportTests++;
      if (recentHighs[i] >= resistanceLevel) resistanceTests++;
    }

    // Sideways nếu:
    // 1. Price range nhỏ (< 5%)
    // 2. Nhiều lần test support/resistance
    // 3. Không có trend rõ ràng
    const isSmallRange = priceRange < 5.0;
    const hasMultipleTests = supportTests >= 3 && resistanceTests >= 3;
    const trendStrength = this.calculateTrendStrength(recentData);
    const isWeakTrend = trendStrength.strength < 0.3;

    const isSideways = isSmallRange && hasMultipleTests && isWeakTrend;

    let confidence = 0;
    if (isSideways) {
      confidence = Math.min(
        (1 - priceRange / 10) * 0.4 + // Range factor
        (Math.min(supportTests + resistanceTests, 10) / 10) * 0.4 + // Test frequency
        (1 - trendStrength.strength) * 0.2, // Weak trend
        1.0
      );
    }

    return {
      isSideways,
      confidence,
      priceRange,
      supportTests,
      resistanceTests,
      supportLevel,
      resistanceLevel
    };
  }

  /**
   * Detect false breakout patterns
   */
  detectFalseBreakouts(candles) {
    if (candles.length < 30) return { frequency: 0, patterns: [] };

    const patterns = [];
    let falseBreakoutCount = 0;

    // Look for breakout patterns in recent 30 candles
    for (let i = 10; i < candles.length - 5; i++) {
      const current = candles[i];
      const prev10 = candles.slice(i-10, i);
      const next5 = candles.slice(i+1, i+6);

      const prev10Highs = prev10.map(c => parseFloat(c.high));
      const prev10Lows = prev10.map(c => parseFloat(c.low));

      const resistance = Math.max(...prev10Highs);
      const support = Math.min(...prev10Lows);

      const currentHigh = parseFloat(current.high);
      const currentLow = parseFloat(current.low);
      const currentClose = parseFloat(current.close);

      // Check for upward breakout
      if (currentHigh > resistance * 1.002) { // 0.2% breakout
        // Check if it's false (price comes back down)
        const maxNext5Close = Math.max(...next5.map(c => parseFloat(c.close)));
        if (maxNext5Close < resistance * 0.999) {
          falseBreakoutCount++;
          patterns.push({
            type: 'false_upward_breakout',
            candle: i,
            resistance,
            breakoutHigh: currentHigh,
            failedAt: maxNext5Close
          });
        }
      }

      // Check for downward breakout
      if (currentLow < support * 0.998) { // 0.2% breakdown
        // Check if it's false (price comes back up)
        const minNext5Close = Math.min(...next5.map(c => parseFloat(c.close)));
        if (minNext5Close > support * 1.001) {
          falseBreakoutCount++;
          patterns.push({
            type: 'false_downward_breakout',
            candle: i,
            support,
            breakoutLow: currentLow,
            failedAt: minNext5Close
          });
        }
      }
    }

    const frequency = falseBreakoutCount / (candles.length - 15);

    return {
      frequency,
      patterns,
      count: falseBreakoutCount
    };
  }

  /**
   * Analyze volume profile
   */
  analyzeVolume(volumes) {
    if (volumes.length < 20) return { trend: 'unknown', strength: 0 };

    const recent = volumes.slice(-10);
    const older = volumes.slice(-20, -10);

    const recentAvg = recent.reduce((sum, v) => sum + v, 0) / recent.length;
    const olderAvg = older.reduce((sum, v) => sum + v, 0) / older.length;

    const volumeChange = (recentAvg - olderAvg) / olderAvg;
    const trend = volumeChange > 0.1 ? 'increasing' : volumeChange < -0.1 ? 'decreasing' : 'stable';
    const strength = Math.min(Math.abs(volumeChange), 1.0);

    return { trend, strength, change: volumeChange };
  }

  /**
   * Analyze support/resistance strength
   */
  analyzeSupportResistance(highs, lows, closes) {
    // Simplified S/R analysis
    const recentHighs = highs.slice(-20);
    const recentLows = lows.slice(-20);

    const maxHigh = Math.max(...recentHighs);
    const minLow = Math.min(...recentLows);

    // Count touches near S/R levels
    let resistanceTouches = 0;
    let supportTouches = 0;

    const resistanceZone = maxHigh * 0.995; // 0.5% below max
    const supportZone = minLow * 1.005; // 0.5% above min

    recentHighs.forEach(h => {
      if (h >= resistanceZone) resistanceTouches++;
    });

    recentLows.forEach(l => {
      if (l <= supportZone) supportTouches++;
    });

    const strength = Math.min((resistanceTouches + supportTouches) / 10, 1.0);

    return {
      strength,
      resistanceTouches,
      supportTouches,
      resistanceLevel: maxHigh,
      supportLevel: minLow
    };
  }

  /**
   * Get trading recommendations based on market condition
   */
  getTradingRecommendations(condition) {
    const recommendations = {
      shouldTrade: true,
      positionSizeMultiplier: 1.0,
      additionalFilters: [],
      riskAdjustments: []
    };

    switch (condition.condition) {
      case 'sideways':
        recommendations.shouldTrade = condition.confidence < 0.8;
        recommendations.positionSizeMultiplier = 0.5;
        recommendations.additionalFilters.push('require_volume_confirmation');
        recommendations.additionalFilters.push('require_multiple_timeframe_confirmation');
        recommendations.riskAdjustments.push('tighter_stop_loss');
        break;

      case 'high_volatility':
        recommendations.shouldTrade = false;
        recommendations.positionSizeMultiplier = 0.3;
        recommendations.riskAdjustments.push('wait_for_volatility_decrease');
        break;

      case 'false_breakout_prone':
        recommendations.shouldTrade = condition.confidence < 0.5;
        recommendations.positionSizeMultiplier = 0.6;
        recommendations.additionalFilters.push('avoid_breakout_signals');
        recommendations.additionalFilters.push('require_strong_momentum');
        break;

      case 'strong_uptrend':
      case 'strong_downtrend':
        recommendations.shouldTrade = true;
        recommendations.positionSizeMultiplier = 1.2;
        recommendations.additionalFilters.push('favor_trend_direction');
        break;
    }

    return recommendations;
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.conditionCache.clear();
  }
}

module.exports = new MarketConditionAnalyzer();