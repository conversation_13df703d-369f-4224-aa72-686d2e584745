const logger = require('../logger');

/**
 * News Event Detector
 * Detects and filters high-impact news events that could affect trading
 */
class NewsEventDetector {
  constructor(config = {}) {
    this.config = {
      // Time windows around events to avoid trading
      highImpactWindow: config.highImpactWindow || 2 * 60 * 60 * 1000, // 2 hours in ms
      mediumImpactWindow: config.mediumImpactWindow || 1 * 60 * 60 * 1000, // 1 hour in ms

      // Event categories and their impact levels
      eventCategories: {
        'fed_announcement': 'high',
        'employment_data': 'high',
        'inflation_data': 'high',
        'gdp_release': 'medium',
        'central_bank_meeting': 'high',
        'earnings_major': 'medium',
        'crypto_regulation': 'high',
        'exchange_hack': 'high',
        'whale_movement': 'medium'
      },

      // Keywords that indicate high-impact events
      highImpactKeywords: [
        'federal reserve', 'fed', 'interest rate', 'inflation', 'cpi', 'ppi',
        'employment', 'unemployment', 'nonfarm payroll', 'gdp',
        'central bank', 'ecb', 'boe', 'boj',
        'regulation', 'ban', 'legal', 'sec', 'cftc',
        'hack', 'exploit', 'security breach',
        'whale', 'large transfer', 'exchange outflow'
      ],

      ...config
    };

    // In-memory cache of recent events
    this.eventCache = new Map();
    this.lastCacheCleanup = Date.now();
  }

  /**
   * Check for news events that might affect trading
   */
  async checkNewsEvents(symbol) {
    try {
      // Clean old events from cache
      this.cleanupEventCache();

      // Get current events (this would integrate with news APIs in production)
      const currentEvents = await this.getCurrentEvents(symbol);

      // Analyze events for trading impact
      const analysis = this.analyzeEvents(currentEvents);

      return {
        highImpact: analysis.hasHighImpact,
        events: currentEvents,
        analysis: analysis,
        nextEventTime: analysis.nextEventTime,
        recommendedAction: analysis.recommendedAction
      };

    } catch (error) {
      logger.error('Error checking news events', { error: error.message, symbol });
      return {
        highImpact: false,
        events: [],
        analysis: { hasHighImpact: false },
        nextEventTime: null,
        recommendedAction: 'proceed'
      };
    }
  }

  /**
   * Get current events (placeholder implementation)
   * In production, this would integrate with news APIs like:
   * - Alpha Vantage News API
   * - NewsAPI
   * - Economic Calendar APIs
   * - Crypto news aggregators
   */
  async getCurrentEvents(symbol) {
    // Simulate some events for demonstration
    const now = new Date();
    const events = [];

    // Check if it's a typical high-impact time (e.g., US market open, major announcements)
    const hour = now.getUTCHours();
    const dayOfWeek = now.getUTCDay();

    // Simulate Fed announcement (first Wednesday of each month at 2 PM EST)
    if (this.isFedAnnouncementDay(now)) {
      events.push({
        id: 'fed_' + now.getTime(),
        title: 'Federal Reserve Interest Rate Decision',
        category: 'fed_announcement',
        impact: 'high',
        time: new Date(now.getFullYear(), now.getMonth(), this.getFedAnnouncementDay(now), 19, 0), // 2 PM EST = 19 UTC
        source: 'federal_reserve',
        symbols: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'], // Affects major cryptos
        description: 'Federal Reserve announces interest rate decision and monetary policy'
      });
    }

    // Simulate employment data (first Friday of each month)
    if (dayOfWeek === 5 && now.getDate() <= 7) {
      events.push({
        id: 'employment_' + now.getTime(),
        title: 'US Employment Data Release',
        category: 'employment_data',
        impact: 'high',
        time: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 13, 30), // 8:30 AM EST
        source: 'bureau_of_labor_statistics',
        symbols: ['BTCUSDT', 'ETHUSDT'],
        description: 'Non-farm payroll and unemployment rate data'
      });
    }

    // Simulate crypto-specific events
    if (Math.random() < 0.1) { // 10% chance of crypto event
      events.push({
        id: 'crypto_' + now.getTime(),
        title: 'Major Exchange Maintenance',
        category: 'exchange_maintenance',
        impact: 'medium',
        time: new Date(now.getTime() + 30 * 60 * 1000), // 30 minutes from now
        source: 'binance',
        symbols: [symbol],
        description: 'Scheduled maintenance affecting trading'
      });
    }

    return events;
  }

  /**
   * Analyze events for trading impact
   */
  analyzeEvents(events) {
    const now = new Date();
    let hasHighImpact = false;
    let hasMediumImpact = false;
    let nextEventTime = null;
    let affectedEvents = [];

    for (const event of events) {
      const eventTime = new Date(event.time);
      const timeDiff = Math.abs(eventTime.getTime() - now.getTime());

      // Check if we're within the impact window
      let withinWindow = false;
      if (event.impact === 'high' && timeDiff <= this.config.highImpactWindow) {
        withinWindow = true;
        hasHighImpact = true;
      } else if (event.impact === 'medium' && timeDiff <= this.config.mediumImpactWindow) {
        withinWindow = true;
        hasMediumImpact = true;
      }

      if (withinWindow) {
        affectedEvents.push(event);
      }

      // Track next upcoming event
      if (eventTime > now && (!nextEventTime || eventTime < nextEventTime)) {
        nextEventTime = eventTime;
      }
    }

    // Determine recommended action
    let recommendedAction = 'proceed';
    if (hasHighImpact) {
      recommendedAction = 'pause_trading';
    } else if (hasMediumImpact) {
      recommendedAction = 'reduce_position_size';
    }

    return {
      hasHighImpact,
      hasMediumImpact,
      affectedEvents,
      nextEventTime,
      recommendedAction,
      totalEvents: events.length,
      riskLevel: hasHighImpact ? 'high' : hasMediumImpact ? 'medium' : 'low'
    };
  }

  /**
   * Check if today is a Fed announcement day
   */
  isFedAnnouncementDay(date) {
    // Fed typically announces on first Wednesday of each month
    // This is a simplified check
    const dayOfWeek = date.getUTCDay();
    const dayOfMonth = date.getUTCDate();

    return dayOfWeek === 3 && dayOfMonth <= 7; // Wednesday in first week
  }

  /**
   * Get Fed announcement day for the month
   */
  getFedAnnouncementDay(date) {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const firstWednesday = 1 + (3 - firstDay.getDay() + 7) % 7;
    return firstWednesday;
  }

  /**
   * Add custom event to monitoring
   */
  addCustomEvent(event) {
    const eventId = event.id || 'custom_' + Date.now();
    this.eventCache.set(eventId, {
      ...event,
      id: eventId,
      addedAt: new Date()
    });

    logger.info('Custom event added', { eventId, title: event.title });
  }

  /**
   * Remove event from monitoring
   */
  removeEvent(eventId) {
    const removed = this.eventCache.delete(eventId);
    if (removed) {
      logger.info('Event removed', { eventId });
    }
    return removed;
  }

  /**
   * Get all monitored events
   */
  getAllEvents() {
    return Array.from(this.eventCache.values());
  }

  /**
   * Clean up old events from cache
   */
  cleanupEventCache() {
    const now = Date.now();

    // Only cleanup every hour
    if (now - this.lastCacheCleanup < 60 * 60 * 1000) {
      return;
    }

    const cutoffTime = now - 24 * 60 * 60 * 1000; // 24 hours ago

    for (const [eventId, event] of this.eventCache.entries()) {
      const eventTime = new Date(event.time).getTime();
      if (eventTime < cutoffTime) {
        this.eventCache.delete(eventId);
      }
    }

    this.lastCacheCleanup = now;
  }

  /**
   * Get events affecting specific symbol
   */
  getEventsForSymbol(symbol) {
    const allEvents = this.getAllEvents();
    return allEvents.filter(event =>
      !event.symbols || event.symbols.length === 0 || event.symbols.includes(symbol)
    );
  }

  /**
   * Check if trading should be paused due to events
   */
  shouldPauseTrading(symbol) {
    try {
      const events = this.getEventsForSymbol(symbol);
      const analysis = this.analyzeEvents(events);

      return {
        shouldPause: analysis.hasHighImpact,
        reason: analysis.hasHighImpact ? 'high_impact_news_event' : null,
        events: analysis.affectedEvents,
        nextEventTime: analysis.nextEventTime
      };
    } catch (error) {
      logger.error('Error checking if should pause trading', { error: error.message, symbol });
      return { shouldPause: false, reason: null, events: [], nextEventTime: null };
    }
  }

  /**
   * Get news-based risk assessment
   */
  getNewsRiskAssessment(symbol) {
    try {
      const events = this.getEventsForSymbol(symbol);
      const analysis = this.analyzeEvents(events);

      return {
        riskLevel: analysis.riskLevel,
        hasHighImpact: analysis.hasHighImpact,
        hasMediumImpact: analysis.hasMediumImpact,
        recommendedAction: analysis.recommendedAction,
        affectedEvents: analysis.affectedEvents,
        nextEventTime: analysis.nextEventTime,
        positionSizeMultiplier: this.calculatePositionSizeMultiplier(analysis)
      };
    } catch (error) {
      logger.error('Error getting news risk assessment', { error: error.message, symbol });
      return {
        riskLevel: 'unknown',
        hasHighImpact: false,
        hasMediumImpact: false,
        recommendedAction: 'proceed',
        affectedEvents: [],
        nextEventTime: null,
        positionSizeMultiplier: 1.0
      };
    }
  }

  /**
   * Calculate position size multiplier based on news events
   */
  calculatePositionSizeMultiplier(analysis) {
    if (analysis.hasHighImpact) {
      return 0.0; // No trading during high impact events
    } else if (analysis.hasMediumImpact) {
      return 0.5; // Reduce position size by 50%
    } else {
      return 1.0; // Normal position size
    }
  }
}

module.exports = NewsEventDetector;