const cron = require('cron');
const marketDataService = require('../services/marketDataService');
const signalService = require('../services/signalService');
const orderManager = require('./orderManager');
const telegramBot = require('./telegramBot');

class TradingScheduler {
  constructor() {
    this.jobs = new Map();
    this.isRunning = false;
  }

  /**
   * Khởi tạo và bắt đầu tất cả scheduled jobs
   */
  async start() {
    try {
      if (this.isRunning) {
        logger.warn('Scheduler is already running');
        return;
      }

      logger.logInfo('Starting Trading Scheduler...');

      // Job 1: Cập nhật danh sách top coins mỗi giờ
      this.scheduleTopCoinsUpdate();

      // Job 2: G<PERSON>i báo cáo thống kê hàng ngày
      this.scheduleDailyReport();

      // Job 3: Cleanup dữ liệu cũ hàng tuần
      this.scheduleWeeklyCleanup();

      // Job 4: Health check mỗi 5 phút
      this.scheduleHealthCheck();

      // Job 5: Backup statistics mỗi 6 giờ
      this.scheduleStatisticsBackup();

      // Job 6: Cleanup zombie signals mỗi 30 phút
      this.scheduleZombieCleanup();

      this.isRunning = true;
      logger.logInfo('Trading Scheduler started successfully');

      // Gửi thông báo khởi động
      await telegramBot.sendStatusNotification('online', 'ScalpWizard Bot đã khởi động thành công');

    } catch (error) {
      logger.logError('Error starting Trading Scheduler:', error.message);
      throw error;
    }
  }

  /**
   * Dừng tất cả scheduled jobs
   */
  async stop() {
    try {
      logger.logInfo('Stopping Trading Scheduler...');

      // Dừng tất cả jobs
      for (const [name, job] of this.jobs) {
        job.stop();
        logger.logInfo(`Stopped job: ${name}`);
      }

      this.jobs.clear();
      this.isRunning = false;

      logger.logInfo('Trading Scheduler stopped');

      // Gửi thông báo dừng
      await telegramBot.sendStatusNotification('offline', 'ScalpWizard Bot đã dừng hoạt động');

    } catch (error) {
      logger.logError('Error stopping Trading Scheduler:', error.message);
    }
  }

  /**
   * Schedule cập nhật top coins mỗi giờ
   */
  scheduleTopCoinsUpdate() {
    const job = new cron.CronJob(
      config.trading.binance.topCoinsUpdateInterval, // '0 0 */1 * * *' - mỗi giờ
      async () => {
        try {
          logger.logInfo('Running scheduled top coins update...');

          const result = await marketDataService.updateTopCoins();

          if (result.added.length > 0 || result.removed.length > 0) {
            // Cập nhật WebSocket connections
            await marketDataService.initializeWebSocketConnections();

            // Gửi thông báo về thay đổi
            const message = `📊 CẬP NHẬT DANH SÁCH COINS

➕ <b>Thêm mới:</b> ${result.added.length} coins
${result.added.slice(0, 5).join(', ')}${result.added.length > 5 ? '...' : ''}

➖ <b>Loại bỏ:</b> ${result.removed.length} coins
${result.removed.slice(0, 5).join(', ')}${result.removed.length > 5 ? '...' : ''}

📈 <b>Tổng đang theo dõi:</b> ${result.current.length} coins`;

            await telegramBot.sendStatusNotification('update', message);
          }

          logger.logInfo('Top coins update completed');
        } catch (error) {
          logger.logError('Error in scheduled top coins update:', error.message);
          await telegramBot.sendErrorNotification(error, 'Top Coins Update');
        }
      },
      null,
      true,
      'Asia/Ho_Chi_Minh'
    );

    this.jobs.set('topCoinsUpdate', job);
    logger.logInfo('Scheduled: Top coins update (every hour)');
  }

  /**
   * Schedule báo cáo thống kê hàng ngày
   */
  scheduleDailyReport() {
    const job = new cron.CronJob(
      '0 0 8 * * *', // 8:00 AM mỗi ngày
      async () => {
        try {
          logger.logInfo('Running scheduled daily report...');

          await signalService.sendStatisticsReport();

          // Gửi thêm thông tin về trạng thái hệ thống
          const marketStatus = marketDataService.getStatus();
          const signalStatus = signalService.getStatus();
          const orderStatus = orderManager.getMonitoringStatus();

          const systemReport = `🔧 **BÁO CÁO HỆ THỐNG HÀNG NGÀY**

📊 **Market Data Service:**
- Trạng thái: ${marketStatus.isInitialized ? '✅ Hoạt động' : '❌ Dừng'}
- Coins theo dõi: ${marketStatus.trackedSymbolsCount}
- Timeframes: ${marketStatus.timeframes.join(', ')}

🎯 **Signal Service:**
- Trạng thái: ${signalStatus.isRunning ? '✅ Hoạt động' : '❌ Dừng'}
- Queue size: ${signalStatus.queueSize}

📈 **Order Manager:**
- Trạng thái: ${orderStatus.isActive ? '✅ Hoạt động' : '❌ Dừng'}
- Lệnh đang theo dõi: ${orderStatus.activeSignalsCount}

⏰ **Thời gian:** ${moment().format('DD/MM/YYYY HH:mm:ss')}`;

          await telegramBot.sendStatusNotification('report', systemReport);

          logger.logInfo('Daily report completed');
        } catch (error) {
          logger.logError('Error in scheduled daily report:', error.message);
          await telegramBot.sendErrorNotification(error, 'Daily Report');
        }
      },
      null,
      true,
      'Asia/Ho_Chi_Minh'
    );

    this.jobs.set('dailyReport', job);
    logger.logInfo('Scheduled: Daily report (8:00 AM)');
  }

  /**
   * Schedule cleanup dữ liệu cũ hàng tuần
   */
  scheduleWeeklyCleanup() {
    const job = new cron.CronJob(
      '0 0 2 * * 0', // 2:00 AM Chủ nhật hàng tuần
      async () => {
        try {
          logger.logInfo('Running scheduled weekly cleanup...');

          // Cleanup old signals (30 ngày)
          const cleanedSignals = await orderManager.cleanupOldSignals(30);

          // Cleanup old market data (7 ngày)
          const MarketData = require('../models/marketData');
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - 7);

          const cleanedMarketData = await MarketData.deleteMany({
            createdAt: { $lt: cutoffDate }
          });

          const cleanupReport = `🧹 **BÁO CÁO DỌN DẸP HÀNG TUẦN**

🗑️ **Đã xóa:**
- ${cleanedSignals} signals cũ (>30 ngày)
- ${cleanedMarketData.deletedCount} market data cũ (>7 ngày)

⏰ **Thời gian:** ${moment().format('DD/MM/YYYY HH:mm:ss')}`;

          await telegramBot.sendStatusNotification('cleanup', cleanupReport);

          logger.logInfo(`Weekly cleanup completed: ${cleanedSignals} signals, ${cleanedMarketData.deletedCount} market data`);
        } catch (error) {
          logger.logError('Error in scheduled weekly cleanup:', error.message);
          await telegramBot.sendErrorNotification(error, 'Weekly Cleanup');
        }
      },
      null,
      true,
      'Asia/Ho_Chi_Minh'
    );

    this.jobs.set('weeklyCleanup', job);
    logger.logInfo('Scheduled: Weekly cleanup (2:00 AM Sunday)');
  }

  /**
   * Schedule health check mỗi 5 phút
   */
  scheduleHealthCheck() {
    const job = new cron.CronJob(
      '*/5 * * * *', // Mỗi 5 phút
      async () => {
        try {
          await this.performHealthCheck();
        } catch (error) {
          logger.logError('Error in scheduled health check:', error.message);
        }
      },
      null,
      true,
      'Asia/Ho_Chi_Minh'
    );

    this.jobs.set('healthCheck', job);
    logger.logInfo('Scheduled: Health check (every 5 minutes)');
  }

  /**
   * Schedule backup statistics mỗi 6 giờ
   */
  scheduleStatisticsBackup() {
    const job = new cron.CronJob(
      '0 0 */6 * * *', // Mỗi 6 giờ
      async () => {
        try {
          logger.logInfo('Running scheduled statistics backup...');

          const statistics = await signalService.getSignalStatistics(30);
          const symbolPerformance = await signalService.getSymbolPerformance(30);

          // Lưu vào file backup
          const backupData = {
            timestamp: new Date(),
            statistics,
            symbolPerformance,
            systemStatus: {
              market: marketDataService.getStatus(),
              signal: signalService.getStatus(),
              order: orderManager.getMonitoringStatus()
            }
          };

          const backupFile = `logs/statistics-backup-${moment().format('YYYY-MM-DD-HH')}.json`;
          fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));

          logger.logInfo(`Statistics backup saved: ${backupFile}`);
        } catch (error) {
          logger.logError('Error in scheduled statistics backup:', error.message);
        }
      },
      null,
      true,
      'Asia/Ho_Chi_Minh'
    );

    this.jobs.set('statisticsBackup', job);
    logger.logInfo('Scheduled: Statistics backup (every 6 hours)');
  }

  /**
   * Thực hiện health check
   */
  async performHealthCheck() {
    try {
      const issues = [];

      // Kiểm tra Market Data Service
      const marketStatus = marketDataService.getStatus();
      if (!marketStatus.isInitialized) {
        issues.push('Market Data Service not initialized');
      }

      // Kiểm tra Signal Service
      const signalStatus = signalService.getStatus();
      if (!signalStatus.isRunning) {
        issues.push('Signal Service not running');
      }

      // Kiểm tra Order Manager
      const orderStatus = orderManager.getMonitoringStatus();
      if (!orderStatus.isActive) {
        issues.push('Order Manager not active');
      }

      // Kiểm tra Telegram Bot
      const telegramStatus = await telegramBot.testConnection();
      if (!telegramStatus) {
        issues.push('Telegram Bot connection failed');
      }

      // Kiểm tra WebSocket connections
      const connectionStatus = marketStatus.connectionStatus;
      const disconnectedConnections = Object.entries(connectionStatus)
        .filter(([key, status]) => status !== 'connected').length;

      if (disconnectedConnections > 0) {
        issues.push(`${disconnectedConnections} WebSocket connections disconnected`);
      }

      // Nếu có vấn đề nghiêm trọng, gửi cảnh báo
      if (issues.length > 0) {
        logger.warn('Health check issues detected:', issues);

        // Chỉ gửi cảnh báo nếu có vấn đề nghiêm trọng
        const criticalIssues = issues.filter(issue =>
          issue.includes('not initialized') ||
          issue.includes('not running') ||
          issue.includes('not active')
        );

        if (criticalIssues.length > 0) {
          await telegramBot.sendErrorNotification(
            new Error(criticalIssues.join(', ')),
            'Health Check'
          );
        }
      }

    } catch (error) {
      logger.logError('Error performing health check:', error.message);
    }
  }

  /**
   * Schedule zombie signals cleanup mỗi 30 phút
   */
  scheduleZombieCleanup() {
    const job = new cron.CronJob(
      '*/30 * * * *', // Mỗi 30 phút
      async () => {
        try {
          logger.logInfo('Running scheduled zombie signals cleanup...');

          const signalAnalyzer = require('./signalAnalyzer');
          const cleanedCount = await signalAnalyzer.cleanupZombieSignals();

          if (cleanedCount > 0) {
            logger.logInfo(`Zombie cleanup completed: ${cleanedCount} signals cleaned`);

            // Gửi thông báo nếu cleanup nhiều signals
            if (cleanedCount >= 5) {
              await telegramBot.sendStatusNotification(
                'cleanup',
                `🧹 Đã dọn dẹp ${cleanedCount} signals zombie (active trong DB nhưng không được monitor)`
              );
            }
          }
        } catch (error) {
          logger.logError('Error in scheduled zombie cleanup:', error.message);
        }
      },
      null,
      true,
      'Asia/Ho_Chi_Minh'
    );

    this.jobs.set('zombieCleanup', job);
    logger.logInfo('Scheduled: Zombie signals cleanup (every 30 minutes)');
  }

  /**
   * Lấy trạng thái scheduler
   */
  getStatus() {
    const jobStatuses = {};
    for (const [name, job] of this.jobs) {
      jobStatuses[name] = {
        running: job.running,
        lastDate: job.lastDate(),
        nextDate: job.nextDate()
      };
    }

    return {
      isRunning: this.isRunning,
      jobCount: this.jobs.size,
      jobs: jobStatuses
    };
  }
}

module.exports = new TradingScheduler();
