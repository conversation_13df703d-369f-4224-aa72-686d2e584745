const logger = require('../logger');
const PerformanceMetricsCalculator = require('./performanceMetricsCalculator');

/**
 * Symbol Performance Evaluator
 * Evaluates performance of individual symbols and provides allocation recommendations
 */
class SymbolPerformanceEvaluator {
  constructor(config = {}) {
    this.config = {
      // Evaluation periods
      evaluationPeriods: config.evaluationPeriods || [7, 30, 90, 365],
      
      // Performance thresholds
      thresholds: {
        excellent: { winRate: 70, profitFactor: 2.0, sharpeRatio: 1.5 },
        good: { winRate: 60, profitFactor: 1.5, sharpeRatio: 1.0 },
        average: { winRate: 50, profitFactor: 1.2, sharpeRatio: 0.5 },
        poor: { winRate: 40, profitFactor: 1.0, sharpeRatio: 0.0 },
        ...config.thresholds
      },

      // Allocation settings
      allocation: {
        maxAllocationPerSymbol: config.allocation?.maxAllocationPerSymbol || 25, // 25%
        minAllocationPerSymbol: config.allocation?.minAllocationPerSymbol || 5,  // 5%
        rebalanceThreshold: config.allocation?.rebalanceThreshold || 10,         // 10% change
        minTradesForEvaluation: config.allocation?.minTradesForEvaluation || 10,
        ...config.allocation
      },

      // Risk factors
      riskFactors: {
        maxDrawdownLimit: config.riskFactors?.maxDrawdownLimit || 15, // 15%
        volatilityLimit: config.riskFactors?.volatilityLimit || 30,   // 30%
        correlationLimit: config.riskFactors?.correlationLimit || 0.8,
        ...config.riskFactors
      },

      ...config
    };

    this.metricsCalculator = new PerformanceMetricsCalculator();
    this.symbolCache = new Map();
    this.lastEvaluationTime = new Map();
  }

  /**
   * Evaluate performance of all symbols
   */
  async evaluateAllSymbols(trades, currentAllocations = {}) {
    try {
      const symbolTrades = this.groupTradesBySymbol(trades);
      const evaluations = new Map();

      for (const [symbol, symbolTradeList] of symbolTrades.entries()) {
        const evaluation = await this.evaluateSymbol(symbol, symbolTradeList);
        evaluations.set(symbol, evaluation);
      }

      // Generate allocation recommendations
      const allocationRecommendations = this.generateAllocationRecommendations(
        evaluations,
        currentAllocations
      );

      return {
        symbolEvaluations: Object.fromEntries(evaluations),
        allocationRecommendations,
        summary: this.generateEvaluationSummary(evaluations),
        evaluationDate: new Date()
      };

    } catch (error) {
      logger.error('Error evaluating all symbols', { error: error.message });
      return {
        symbolEvaluations: {},
        allocationRecommendations: [],
        summary: {},
        evaluationDate: new Date()
      };
    }
  }

  /**
   * Evaluate performance of a specific symbol
   */
  async evaluateSymbol(symbol, trades) {
    try {
      // Check cache
      const cacheKey = `${symbol}_${trades.length}`;
      const cached = this.symbolCache.get(cacheKey);
      const lastEval = this.lastEvaluationTime.get(symbol);
      
      if (cached && lastEval && (Date.now() - lastEval) < 60 * 60 * 1000) { // 1 hour cache
        return cached;
      }

      const evaluation = {
        symbol,
        totalTrades: trades.length,
        evaluationPeriods: {},
        overallRating: 'unknown',
        riskAssessment: {},
        recommendations: [],
        strengths: [],
        weaknesses: []
      };

      // Evaluate for different time periods
      for (const days of this.config.evaluationPeriods) {
        const periodTrades = this.getTradesInPeriod(trades, days);
        if (periodTrades.length >= this.config.allocation.minTradesForEvaluation) {
          const metrics = this.metricsCalculator.calculateComprehensiveMetrics(periodTrades);
          const rating = this.ratePerformance(metrics);
          
          evaluation.evaluationPeriods[`${days}d`] = {
            trades: periodTrades.length,
            metrics,
            rating,
            score: this.calculatePerformanceScore(metrics)
          };
        }
      }

      // Calculate overall rating
      evaluation.overallRating = this.calculateOverallRating(evaluation.evaluationPeriods);

      // Risk assessment
      evaluation.riskAssessment = this.assessSymbolRisk(trades);

      // Generate recommendations
      evaluation.recommendations = this.generateSymbolRecommendations(evaluation);

      // Identify strengths and weaknesses
      evaluation.strengths = this.identifyStrengths(evaluation);
      evaluation.weaknesses = this.identifyWeaknesses(evaluation);

      // Cache the result
      this.symbolCache.set(cacheKey, evaluation);
      this.lastEvaluationTime.set(symbol, Date.now());

      return evaluation;

    } catch (error) {
      logger.error('Error evaluating symbol', { error: error.message, symbol });
      return {
        symbol,
        totalTrades: trades.length,
        evaluationPeriods: {},
        overallRating: 'error',
        riskAssessment: {},
        recommendations: ['Error in evaluation - manual review required'],
        strengths: [],
        weaknesses: ['Evaluation error']
      };
    }
  }

  /**
   * Group trades by symbol
   */
  groupTradesBySymbol(trades) {
    const symbolTrades = new Map();
    
    for (const trade of trades) {
      const symbol = trade.symbol;
      if (!symbolTrades.has(symbol)) {
        symbolTrades.set(symbol, []);
      }
      symbolTrades.get(symbol).push(trade);
    }

    return symbolTrades;
  }

  /**
   * Get trades within a specific period
   */
  getTradesInPeriod(trades, days) {
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return trades.filter(trade => {
      const tradeDate = trade.createdAt || trade.timestamp || new Date();
      return new Date(tradeDate) >= cutoffDate;
    });
  }

  /**
   * Rate performance based on metrics
   */
  ratePerformance(metrics) {
    const thresholds = this.config.thresholds;
    
    if (metrics.winRate >= thresholds.excellent.winRate &&
        metrics.profitFactor >= thresholds.excellent.profitFactor &&
        metrics.sharpeRatio >= thresholds.excellent.sharpeRatio) {
      return 'excellent';
    }
    
    if (metrics.winRate >= thresholds.good.winRate &&
        metrics.profitFactor >= thresholds.good.profitFactor &&
        metrics.sharpeRatio >= thresholds.good.sharpeRatio) {
      return 'good';
    }
    
    if (metrics.winRate >= thresholds.average.winRate &&
        metrics.profitFactor >= thresholds.average.profitFactor &&
        metrics.sharpeRatio >= thresholds.average.sharpeRatio) {
      return 'average';
    }
    
    if (metrics.winRate >= thresholds.poor.winRate &&
        metrics.profitFactor >= thresholds.poor.profitFactor) {
      return 'poor';
    }
    
    return 'very_poor';
  }

  /**
   * Calculate performance score (0-100)
   */
  calculatePerformanceScore(metrics) {
    let score = 0;
    
    // Win rate component (40% weight)
    score += (metrics.winRate / 100) * 40;
    
    // Profit factor component (30% weight)
    const pfScore = Math.min(metrics.profitFactor / 3, 1); // Cap at 3.0
    score += pfScore * 30;
    
    // Sharpe ratio component (20% weight)
    const sharpeScore = Math.min(Math.max(metrics.sharpeRatio / 2, 0), 1); // 0-2 range
    score += sharpeScore * 20;
    
    // Drawdown penalty (10% weight)
    const drawdownPenalty = Math.min(metrics.maxDrawdown / 20, 1); // 0-20% range
    score += (1 - drawdownPenalty) * 10;
    
    return Math.round(score * 100) / 100;
  }

  /**
   * Calculate overall rating from period evaluations
   */
  calculateOverallRating(evaluationPeriods) {
    const periods = Object.values(evaluationPeriods);
    if (periods.length === 0) return 'insufficient_data';

    const ratings = periods.map(p => p.rating);
    const scores = periods.map(p => p.score);
    
    // Weight recent periods more heavily
    let weightedScore = 0;
    let totalWeight = 0;
    
    periods.forEach((period, index) => {
      const weight = Math.pow(2, index); // More recent periods get higher weight
      weightedScore += period.score * weight;
      totalWeight += weight;
    });
    
    const avgScore = weightedScore / totalWeight;
    
    if (avgScore >= 80) return 'excellent';
    if (avgScore >= 65) return 'good';
    if (avgScore >= 50) return 'average';
    if (avgScore >= 35) return 'poor';
    return 'very_poor';
  }

  /**
   * Assess symbol-specific risks
   */
  assessSymbolRisk(trades) {
    const metrics = this.metricsCalculator.calculateComprehensiveMetrics(trades);
    const riskFactors = [];
    let riskLevel = 'low';

    // High drawdown risk
    if (metrics.maxDrawdown > this.config.riskFactors.maxDrawdownLimit) {
      riskFactors.push(`High maximum drawdown: ${metrics.maxDrawdown.toFixed(2)}%`);
      riskLevel = 'high';
    }

    // High volatility risk
    if (metrics.volatility > this.config.riskFactors.volatilityLimit) {
      riskFactors.push(`High volatility: ${metrics.volatility.toFixed(2)}%`);
      if (riskLevel !== 'high') riskLevel = 'medium';
    }

    // Poor consistency
    if (metrics.consistencyRatio < 40) {
      riskFactors.push(`Low consistency: ${metrics.consistencyRatio.toFixed(2)}%`);
      if (riskLevel === 'low') riskLevel = 'medium';
    }

    // Negative expectancy
    if (metrics.expectancy < 0) {
      riskFactors.push(`Negative expectancy: ${metrics.expectancy.toFixed(2)}%`);
      riskLevel = 'high';
    }

    return {
      riskLevel,
      riskFactors,
      maxDrawdown: metrics.maxDrawdown,
      volatility: metrics.volatility,
      consistencyRatio: metrics.consistencyRatio,
      expectancy: metrics.expectancy
    };
  }

  /**
   * Generate allocation recommendations
   */
  generateAllocationRecommendations(evaluations, currentAllocations) {
    const recommendations = [];
    const symbolScores = new Map();

    // Calculate scores for all symbols
    for (const [symbol, evaluation] of evaluations.entries()) {
      const periods = Object.values(evaluation.evaluationPeriods);
      if (periods.length > 0) {
        const avgScore = periods.reduce((sum, p) => sum + p.score, 0) / periods.length;
        symbolScores.set(symbol, {
          score: avgScore,
          rating: evaluation.overallRating,
          riskLevel: evaluation.riskAssessment.riskLevel
        });
      }
    }

    // Sort symbols by score
    const sortedSymbols = Array.from(symbolScores.entries())
      .sort(([,a], [,b]) => b.score - a.score);

    // Generate recommendations
    const totalSymbols = sortedSymbols.length;
    let allocatedPercentage = 0;

    for (let i = 0; i < sortedSymbols.length; i++) {
      const [symbol, data] = sortedSymbols[i];
      const currentAllocation = currentAllocations[symbol] || 0;
      
      let recommendedAllocation = 0;
      
      // Calculate recommended allocation based on performance and risk
      if (data.riskLevel === 'low' && data.score >= 70) {
        recommendedAllocation = Math.min(this.config.allocation.maxAllocationPerSymbol, 20);
      } else if (data.riskLevel === 'medium' && data.score >= 60) {
        recommendedAllocation = Math.min(this.config.allocation.maxAllocationPerSymbol, 15);
      } else if (data.riskLevel === 'low' && data.score >= 50) {
        recommendedAllocation = Math.min(this.config.allocation.maxAllocationPerSymbol, 10);
      } else if (data.score >= 40) {
        recommendedAllocation = this.config.allocation.minAllocationPerSymbol;
      }

      // Ensure we don't over-allocate
      if (allocatedPercentage + recommendedAllocation > 100) {
        recommendedAllocation = Math.max(0, 100 - allocatedPercentage);
      }

      allocatedPercentage += recommendedAllocation;

      // Generate recommendation if allocation should change
      const allocationChange = Math.abs(recommendedAllocation - currentAllocation);
      if (allocationChange >= this.config.allocation.rebalanceThreshold) {
        recommendations.push({
          symbol,
          currentAllocation,
          recommendedAllocation,
          change: recommendedAllocation - currentAllocation,
          reason: this.getAllocationReason(data, recommendedAllocation),
          priority: this.getAllocationPriority(allocationChange, data.riskLevel),
          score: data.score,
          rating: data.rating
        });
      }
    }

    return recommendations;
  }

  /**
   * Get allocation reason
   */
  getAllocationReason(data, allocation) {
    if (allocation === 0) {
      return `Poor performance (score: ${data.score.toFixed(1)}) and ${data.riskLevel} risk`;
    } else if (allocation >= 15) {
      return `Excellent performance (score: ${data.score.toFixed(1)}) with ${data.riskLevel} risk`;
    } else if (allocation >= 10) {
      return `Good performance (score: ${data.score.toFixed(1)}) with manageable risk`;
    } else {
      return `Average performance (score: ${data.score.toFixed(1)}) - minimal allocation`;
    }
  }

  /**
   * Get allocation priority
   */
  getAllocationPriority(changeAmount, riskLevel) {
    if (changeAmount >= 15) return 'high';
    if (changeAmount >= 10) return 'medium';
    if (riskLevel === 'high') return 'high';
    return 'low';
  }

  /**
   * Generate symbol-specific recommendations
   */
  generateSymbolRecommendations(evaluation) {
    const recommendations = [];
    const periods = Object.values(evaluation.evaluationPeriods);
    
    if (periods.length === 0) {
      return ['Insufficient trade data for recommendations'];
    }

    const latestPeriod = periods[periods.length - 1];
    const metrics = latestPeriod.metrics;

    // Performance-based recommendations
    if (metrics.winRate < 50) {
      recommendations.push('Consider reviewing entry criteria - low win rate detected');
    }

    if (metrics.profitFactor < 1.2) {
      recommendations.push('Improve risk-reward ratio - profit factor below optimal');
    }

    if (metrics.maxDrawdown > 15) {
      recommendations.push('Implement stricter risk management - high drawdown detected');
    }

    if (metrics.sharpeRatio < 0.5) {
      recommendations.push('Focus on risk-adjusted returns - low Sharpe ratio');
    }

    // Risk-based recommendations
    if (evaluation.riskAssessment.riskLevel === 'high') {
      recommendations.push('Reduce position sizes due to high risk profile');
    }

    if (metrics.consistencyRatio < 40) {
      recommendations.push('Work on trade consistency - high variability in results');
    }

    return recommendations.length > 0 ? recommendations : ['Performance within acceptable parameters'];
  }

  /**
   * Identify symbol strengths
   */
  identifyStrengths(evaluation) {
    const strengths = [];
    const periods = Object.values(evaluation.evaluationPeriods);
    
    if (periods.length === 0) return strengths;

    const latestMetrics = periods[periods.length - 1].metrics;

    if (latestMetrics.winRate >= 65) strengths.push('High win rate');
    if (latestMetrics.profitFactor >= 2.0) strengths.push('Excellent profit factor');
    if (latestMetrics.sharpeRatio >= 1.0) strengths.push('Good risk-adjusted returns');
    if (latestMetrics.maxDrawdown <= 10) strengths.push('Low drawdown');
    if (latestMetrics.consistencyRatio >= 60) strengths.push('Consistent performance');

    return strengths;
  }

  /**
   * Identify symbol weaknesses
   */
  identifyWeaknesses(evaluation) {
    const weaknesses = [];
    const periods = Object.values(evaluation.evaluationPeriods);
    
    if (periods.length === 0) return ['Insufficient data'];

    const latestMetrics = periods[periods.length - 1].metrics;

    if (latestMetrics.winRate < 45) weaknesses.push('Low win rate');
    if (latestMetrics.profitFactor < 1.2) weaknesses.push('Poor profit factor');
    if (latestMetrics.sharpeRatio < 0.3) weaknesses.push('Poor risk-adjusted returns');
    if (latestMetrics.maxDrawdown > 20) weaknesses.push('High drawdown');
    if (latestMetrics.consistencyRatio < 40) weaknesses.push('Inconsistent performance');

    return weaknesses;
  }

  /**
   * Generate evaluation summary
   */
  generateEvaluationSummary(evaluations) {
    const symbols = Array.from(evaluations.values());
    const totalSymbols = symbols.length;
    
    if (totalSymbols === 0) {
      return { totalSymbols: 0, message: 'No symbols to evaluate' };
    }

    const ratingCounts = {
      excellent: 0,
      good: 0,
      average: 0,
      poor: 0,
      very_poor: 0
    };

    symbols.forEach(symbol => {
      if (ratingCounts.hasOwnProperty(symbol.overallRating)) {
        ratingCounts[symbol.overallRating]++;
      }
    });

    const topPerformers = symbols
      .filter(s => s.overallRating === 'excellent' || s.overallRating === 'good')
      .map(s => s.symbol);

    const underPerformers = symbols
      .filter(s => s.overallRating === 'poor' || s.overallRating === 'very_poor')
      .map(s => s.symbol);

    return {
      totalSymbols,
      ratingDistribution: ratingCounts,
      topPerformers,
      underPerformers,
      excellentPercentage: Math.round((ratingCounts.excellent / totalSymbols) * 100),
      needsAttentionPercentage: Math.round(((ratingCounts.poor + ratingCounts.very_poor) / totalSymbols) * 100)
    };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.symbolCache.clear();
    this.lastEvaluationTime.clear();
    logger.info('Symbol performance evaluator cache cleared');
  }
}

module.exports = SymbolPerformanceEvaluator;
