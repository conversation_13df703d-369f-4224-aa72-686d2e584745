const indicators = require('./indicators');
const TradingSignal = require('../models/tradingSignal');
const marketConditionAnalyzer = require('./marketConditionAnalyzer');

class SignalAnalyzer {
  constructor() {
    this.timeframes = config.trading.timeframes;
  }

  /**
   * Phân tích tín hiệu cho một symbol và timeframe
   */
  async analyzeSignal(symbol, timeframe, candles) {
    try {
      if (!candles || candles.length < 220) {
        return null;
      }

      // 🔍 MARKET CONDITION ANALYSIS - Phân tích điều kiện thị trường
      const marketCondition = await marketConditionAnalyzer.analyzeMarketCondition(symbol, timeframe);
      const tradingRecommendations = marketConditionAnalyzer.getTradingRecommendations(marketCondition);

      // ❌ Skip trading nếu điều kiện thị trường không phù hợp
      if (!tradingRecommendations.shouldTrade) {
        logger.logInfo(`Skipping ${symbol} - Market condition: ${marketCondition.condition} (confidence: ${marketCondition.confidence.toFixed(2)})`);
        return null;
      }

      // Tính toán các chỉ báo
      const indicatorData = indicators.calculateAllIndicators(candles);
      if (!indicatorData) {
        return null;
      }

      // Kiểm tra MACD crossover
      const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, candles);

      // Kiểm tra điều kiện BUY với market condition filtering
      const buySignal = this.checkBuyConditions(indicatorData, macdCrossover, candles, marketCondition, tradingRecommendations);
      if (buySignal.isValid) {
        return this.createSignal(symbol, timeframe, 'BUY', indicatorData, candles, buySignal.reason, marketCondition);
      }

      // Kiểm tra điều kiện SELL với market condition filtering
      const sellSignal = this.checkSellConditions(indicatorData, macdCrossover, candles, marketCondition, tradingRecommendations);
      if (sellSignal.isValid) {
        return this.createSignal(symbol, timeframe, 'SELL', indicatorData, candles, sellSignal.reason, marketCondition);
      }

      return null;
    } catch (error) {
      logger.logError(`Error analyzing signal for ${symbol} ${timeframe}:`, error.message);
      return null;
    }
  }

  /**
   * Kiểm tra điều kiện BUY nâng cao
   */
  checkBuyConditions(indicatorData, macdCrossover, candles, marketCondition = null, tradingRecommendations = null) {
    const conditions = {
      // Core conditions (BẮT BUỘC)
      priceAboveEMA200: indicatorData.currentPrice > indicatorData.ema200,
      emaAlignment: indicatorData.ema50 > indicatorData.ema200,

      // Signal conditions (CẦN ÍT NHẤT 2/3)
      macdCrossover: macdCrossover === 'bullish',
      rsiZone: indicators.checkRSIZone(indicatorData.rsi, 'BUY'),
      volumeConfirmation: this.checkVolumeConfirmation(candles, 'BUY'),

      // Pattern conditions (BẮT BUỘC)
      strongBodyOrEngulfing: this.checkStrongPattern(indicatorData),

      // Momentum confirmation (THÊM MỚI)
      priceAboveEMA50: indicatorData.currentPrice > indicatorData.ema50,
      emaSlope: this.checkEMASlope(candles, 'bullish'),
      macdHistogramPositive: indicatorData.macd?.histogram > 0
    };

    // Tính signal score (cần ít nhất 2/3)
    const signalScore = [
      conditions.macdCrossover,
      conditions.rsiZone,
      conditions.volumeConfirmation
    ].filter(Boolean).length;

    // Logic mới: Core + (2/3 Signal) + Pattern + Momentum
    const coreConditions = conditions.priceAboveEMA200 && conditions.emaAlignment;
    const signalCondition = signalScore >= 2;
    const patternCondition = conditions.strongBodyOrEngulfing;
    const momentumCondition = conditions.priceAboveEMA50 && conditions.emaSlope;

    let isValid = coreConditions && signalCondition && patternCondition && momentumCondition;

    // 🔍 MARKET CONDITION FILTERING
    if (isValid && marketCondition && tradingRecommendations) {
      isValid = this.applyMarketConditionFiltering(isValid, 'BUY', marketCondition, tradingRecommendations, conditions);
    }

    return {
      isValid,
      conditions,
      signalScore,
      reason: this.getAdvancedConditionsSummary(conditions, 'BUY', signalScore),
      details: {
        coreConditions,
        signalCondition,
        patternCondition,
        momentumCondition,
        strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0,
        marketCondition: marketCondition?.condition || 'unknown',
        marketRisk: marketCondition?.riskLevel || 'medium'
      }
    };
  }

  /**
   * Kiểm tra điều kiện SELL nâng cao
   */
  checkSellConditions(indicatorData, macdCrossover, candles, marketCondition = null, tradingRecommendations = null) {
    const conditions = {
      // Core conditions (BẮT BUỘC)
      priceBelowEMA200: indicatorData.currentPrice < indicatorData.ema200,
      emaAlignment: indicatorData.ema50 < indicatorData.ema200,

      // Signal conditions (CẦN ÍT NHẤT 2/3)
      macdCrossover: macdCrossover === 'bearish',
      rsiZone: indicators.checkRSIZone(indicatorData.rsi, 'SELL'),
      volumeConfirmation: this.checkVolumeConfirmation(candles, 'SELL'),

      // Pattern conditions (BẮT BUỘC)
      strongBodyOrEngulfing: this.checkStrongPattern(indicatorData),

      // Momentum confirmation (THÊM MỚI)
      priceBelowEMA50: indicatorData.currentPrice < indicatorData.ema50,
      emaSlope: this.checkEMASlope(candles, 'bearish'),
      macdHistogramNegative: indicatorData.macd?.histogram < 0
    };

    // Tính signal score (cần ít nhất 2/3)
    const signalScore = [
      conditions.macdCrossover,
      conditions.rsiZone,
      conditions.volumeConfirmation
    ].filter(Boolean).length;

    // Logic mới: Core + (2/3 Signal) + Pattern + Momentum
    const coreConditions = conditions.priceBelowEMA200 && conditions.emaAlignment;
    const signalCondition = signalScore >= 2;
    const patternCondition = conditions.strongBodyOrEngulfing;
    const momentumCondition = conditions.priceBelowEMA50 && conditions.emaSlope;

    let isValid = coreConditions && signalCondition && patternCondition && momentumCondition;

    // 🔍 MARKET CONDITION FILTERING
    if (isValid && marketCondition && tradingRecommendations) {
      isValid = this.applyMarketConditionFiltering(isValid, 'SELL', marketCondition, tradingRecommendations, conditions);
    }

    return {
      isValid,
      conditions,
      signalScore,
      reason: this.getAdvancedConditionsSummary(conditions, 'SELL', signalScore),
      details: {
        coreConditions,
        signalCondition,
        patternCondition,
        momentumCondition,
        strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0,
        marketCondition: marketCondition?.condition || 'unknown',
        marketRisk: marketCondition?.riskLevel || 'medium'
      }
    };
  }

  /**
   * Tạo signal object
   */
  createSignal(symbol, timeframe, type, indicatorData, candles, conditions = null, marketCondition = null) {
    const entry = indicatorData.currentPrice;
    const sltpResult = indicators.calculateSLTP(entry, type, candles);

    const latestCandle = candles[candles.length - 1];

    return {
      symbol,
      timeframe,
      type,
      entry,
      stopLoss: sltpResult.stopLoss,
      takeProfit: sltpResult.takeProfit,
      riskReward: sltpResult.riskReward,
      tpMethod: sltpResult.tpMethod,
      atr: sltpResult.atr,
      riskAmount: sltpResult.riskAmount,
      rewardAmount: sltpResult.rewardAmount,
      conditions: conditions, // Điều kiện đã thỏa mãn
      indicators: {
        ema50: indicatorData.ema50,
        ema200: indicatorData.ema200,
        macd: indicatorData.macd,
        rsi: indicatorData.rsi,
        ...(indicatorData.engulfing !== 'none' && { engulfing: indicatorData.engulfing }),
        strongBody: indicatorData.strongBody
      },
      marketData: {
        open: latestCandle.open,
        high: latestCandle.high,
        low: latestCandle.low,
        close: latestCandle.close,
        volume: latestCandle.volume
      },
      marketCondition: marketCondition ? {
        condition: marketCondition.condition,
        confidence: marketCondition.confidence,
        riskLevel: marketCondition.riskLevel,
        recommendations: marketCondition.recommendations,
        volatility: marketCondition.metrics?.volatility,
        trendStrength: marketCondition.metrics?.trendStrength
      } : null
    };
  }

  /**
   * Kiểm tra xác nhận volume
   */
  checkVolumeConfirmation(candles, type) {
    if (candles.length < 20) return false;

    const recentVolumes = candles.slice(-20).map(c => c.volume);
    const avgVolume = recentVolumes.reduce((a, b) => a + b) / recentVolumes.length;
    const currentVolume = candles[candles.length - 1].volume;

    // Volume phải cao hơn 1.5x trung bình
    return currentVolume > avgVolume * 1.5;
  }

  /**
   * Kiểm tra pattern mạnh (body + engulfing)
   */
  checkStrongPattern(indicatorData) {
    const hasStrongBody = indicatorData.strongBody?.isStrong;
    const hasEngulfing = indicatorData.engulfing !== 'none';

    return hasStrongBody || hasEngulfing;
  }

  /**
   * Kiểm tra độ dốc EMA để xác nhận xu hướng
   */
  checkEMASlope(candles, direction) {
    if (candles.length < 10) return false;

    const closes = candles.map(c => c.close);
    const ema50Values = [];

    // Tính EMA50 cho 5 nến gần nhất
    for (let i = candles.length - 5; i < candles.length; i++) {
      const ema = indicators.calculateEMA(closes.slice(0, i + 1), 50);
      if (ema) ema50Values.push(ema);
    }

    if (ema50Values.length < 3) return false;

    if (direction === 'bullish') {
      // EMA50 phải tăng trong 3/5 nến gần nhất
      let increasingCount = 0;
      for (let i = 1; i < ema50Values.length; i++) {
        if (ema50Values[i] > ema50Values[i - 1]) increasingCount++;
      }
      return increasingCount >= Math.floor(ema50Values.length * 0.6);
    } else {
      // EMA50 phải giảm trong 3/5 nến gần nhất
      let decreasingCount = 0;
      for (let i = 1; i < ema50Values.length; i++) {
        if (ema50Values[i] < ema50Values[i - 1]) decreasingCount++;
      }
      return decreasingCount >= Math.floor(ema50Values.length * 0.6);
    }
  }

  /**
   * Tóm tắt điều kiện nâng cao
   */
  getAdvancedConditionsSummary(conditions, type, signalScore) {
    const summary = [];

    if (type === 'BUY') {
      if (conditions.priceAboveEMA200) summary.push('✅ Giá > EMA200');
      if (conditions.emaAlignment) summary.push('✅ EMA50 > EMA200');
      if (conditions.priceAboveEMA50) summary.push('✅ Giá > EMA50');
      if (conditions.emaSlope) summary.push('✅ EMA50 slope tăng');

      summary.push(`📊 Signal Score: ${signalScore}/3`);
      if (conditions.macdCrossover) summary.push('  ✅ MACD cắt lên Signal');
      if (conditions.rsiZone) summary.push('  ✅ RSI trong vùng mua (50-70)');
      if (conditions.volumeConfirmation) summary.push('  ✅ Volume xác nhận (>1.5x avg)');

      if (conditions.strongBodyOrEngulfing) summary.push('✅ Pattern mạnh (Body ≥60% hoặc Engulfing)');
      if (conditions.macdHistogramPositive) summary.push('✅ MACD Histogram > 0');
    } else {
      if (conditions.priceBelowEMA200) summary.push('✅ Giá < EMA200');
      if (conditions.emaAlignment) summary.push('✅ EMA50 < EMA200');
      if (conditions.priceBelowEMA50) summary.push('✅ Giá < EMA50');
      if (conditions.emaSlope) summary.push('✅ EMA50 slope giảm');

      summary.push(`📊 Signal Score: ${signalScore}/3`);
      if (conditions.macdCrossover) summary.push('  ✅ MACD cắt xuống Signal');
      if (conditions.rsiZone) summary.push('  ✅ RSI trong vùng bán (30-50)');
      if (conditions.volumeConfirmation) summary.push('  ✅ Volume xác nhận (>1.5x avg)');

      if (conditions.strongBodyOrEngulfing) summary.push('✅ Pattern mạnh (Body ≥60% hoặc Engulfing)');
      if (conditions.macdHistogramNegative) summary.push('✅ MACD Histogram < 0');
    }

    return summary.join('\n');
  }

  /**
   * Tóm tắt điều kiện (legacy method for backward compatibility)
   */
  getConditionsSummary(conditions, type) {
    return this.getAdvancedConditionsSummary(conditions, type, 0);
  }

  /**
   * Kiểm tra xem có signal trùng lặp không
   */
  async checkDuplicateSignal(symbol, timeframe, type) {
    try {
      // Kiểm tra trong 1 giờ gần nhất
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const existingSignal = await TradingSignal.findOne({
        symbol,
        timeframe,
        type,
        status: 'active',
        createdAt: { $gte: oneHourAgo },
        isConflictNotification: false
      });

      return !!existingSignal;
    } catch (error) {
      logger.logError(`Error checking duplicate signal:`, error.message);
      return false;
    }
  }

  /**
   * Kiểm tra conflict với lệnh đang active
   * Logic thông minh: Chỉ conflict nếu entry mới nằm trong range SL/TP của lệnh cũ
   */
  async checkActiveSignalConflict(symbol, timeframe, newSignalType, newSignalData = null) {
    try {
      const activeSignal = await TradingSignal.getActiveSignalBySymbol(symbol, timeframe);

      if (!activeSignal) {
        return { hasConflict: false };
      }

      // Kiểm tra thêm: Signal có thực sự còn active không?
      const signalAge = Date.now() - new Date(activeSignal.createdAt).getTime();
      const maxSignalAge = 24 * 60 * 60 * 1000; // 24 giờ

      // Nếu signal quá cũ (>24h) và vẫn active, có thể có vấn đề
      if (signalAge > maxSignalAge) {
        logger.warn(`Found very old active signal: ${symbol} ${timeframe} - Age: ${Math.round(signalAge / (60 * 60 * 1000))}h`);
        // Auto-cancel old signals
        await this.updateSignalStatus(activeSignal._id, 'cancelled', null, new Date());
        return { hasConflict: false };
      }

      // Kiểm tra xem signal có đang được monitor không
      const orderManager = require('./orderManager');
      const isBeingMonitored = orderManager.activeSignals.has(activeSignal._id.toString());

      if (!isBeingMonitored) {
        logger.warn(`Active signal not in monitoring: ${symbol} ${timeframe} - May have been closed but not updated in DB`);
        // Signal đã đóng nhưng chưa cập nhật DB - cho phép signal mới
        return { hasConflict: false };
      }

      // ✅ LOGIC THÔNG MINH: Check entry của signal mới có nằm trong range SL/TP không
      if (newSignalData && newSignalData.entry) {
        const newEntry = newSignalData.entry;
        const oldSL = activeSignal.stopLoss;
        const oldTP = activeSignal.takeProfit;

        // Xác định range của lệnh cũ (từ SL đến TP)
        let rangeMin, rangeMax;
        if (activeSignal.type === 'BUY') {
          rangeMin = oldSL;   // SL thấp hơn entry
          rangeMax = oldTP;   // TP cao hơn entry
        } else { // SELL
          rangeMin = oldTP;   // TP thấp hơn entry
          rangeMax = oldSL;   // SL cao hơn entry
        }

        // Check xem entry mới có nằm trong range SL/TP của lệnh cũ không
        const isInRange = newEntry >= rangeMin && newEntry <= rangeMax;

        logger.logInfo(`Conflict analysis for ${symbol}:`, {
          activeSignal: `${activeSignal.type} Entry:${activeSignal.entry} SL:${oldSL} TP:${oldTP}`,
          newSignal: `${newSignalType} Entry:${newEntry}`,
          range: `${rangeMin} - ${rangeMax}`,
          isInRange: isInRange
        });

        if (!isInRange) {
          // Entry mới nằm NGOÀI range SL/TP của lệnh cũ
          // Có thể lệnh cũ đã hit SL/TP rồi, cần check và update
          logger.logInfo(`New entry ${newEntry} is outside old signal range [${rangeMin}, ${rangeMax}] - Checking if old signal should be closed`);

          // Lấy giá hiện tại để check xem lệnh cũ có hit SL/TP chưa
          try {
            const binanceClient = require('./binanceClient');
            const currentPrice = await binanceClient.getCurrentPrice(symbol);

            if (currentPrice) {
              // Check xem lệnh cũ có hit SL/TP với giá hiện tại không
              const hitSL = activeSignal.checkStopLoss(currentPrice);
              const hitTP = activeSignal.checkTakeProfit(currentPrice);

              if (hitSL) {
                logger.logInfo(`Old signal hit SL at current price ${currentPrice} - Auto-closing`);
                await this.updateSignalStatus(activeSignal._id, 'hit_sl', currentPrice, new Date());
                // Remove from monitoring
                orderManager.removeSignalFromMonitoring(activeSignal._id.toString());
                return {
                  hasConflict: false,
                  autoClosedOldSignal: true,
                  closedReason: 'hit_sl',
                  closedPrice: currentPrice
                };
              }

              if (hitTP) {
                logger.logInfo(`Old signal hit TP at current price ${currentPrice} - Auto-closing`);
                await this.updateSignalStatus(activeSignal._id, 'hit_tp', currentPrice, new Date());
                // Remove from monitoring
                orderManager.removeSignalFromMonitoring(activeSignal._id.toString());
                return {
                  hasConflict: false,
                  autoClosedOldSignal: true,
                  closedReason: 'hit_tp',
                  closedPrice: currentPrice
                };
              }
            }
          } catch (error) {
            logger.logError('Error checking current price for old signal:', error.message);
          }

          // Nếu không hit SL/TP nhưng entry mới nằm ngoài range
          // Có thể là signal mới hợp lệ, không conflict
          return {
            hasConflict: false,
            reason: 'new_entry_outside_old_range'
          };
        }

        // Entry mới nằm TRONG range SL/TP của lệnh cũ → Thực sự conflict
        const isSameDirection = activeSignal.type === newSignalType;

        return {
          hasConflict: true,
          activeSignal,
          conflictType: isSameDirection ? 'same_direction' : 'opposite_direction',
          message: isSameDirection
            ? `Đã có lệnh ${activeSignal.type} đang chạy cho ${symbol} ${timeframe} (entry mới trong range SL/TP)`
            : `Có lệnh ${activeSignal.type} đang chạy, tín hiệu mới ${newSignalType} nằm trong range SL/TP`,
          conflictDetails: {
            oldRange: `${rangeMin} - ${rangeMax}`,
            newEntry: newEntry,
            isInRange: true
          }
        };
      }

      // Fallback: Nếu không có newSignalData, dùng logic cũ
      const isSameDirection = activeSignal.type === newSignalType;

      return {
        hasConflict: true,
        activeSignal,
        conflictType: isSameDirection ? 'same_direction' : 'opposite_direction',
        message: isSameDirection
          ? `Đã có lệnh ${activeSignal.type} đang chạy cho ${symbol} ${timeframe}`
          : `Có lệnh ${activeSignal.type} đang chạy, tín hiệu mới là ${newSignalType}`
      };
    } catch (error) {
      logger.logError('Error checking active signal conflict:', error.message);
      return { hasConflict: false };
    }
  }

  /**
   * Lưu signal vào database
   */
  async saveSignal(signalData) {
    try {
      // Kiểm tra trùng lặp
      const isDuplicate = await this.checkDuplicateSignal(
        signalData.symbol,
        signalData.timeframe,
        signalData.type
      );

      if (isDuplicate) {
        logger.warn(`Duplicate signal detected for ${signalData.symbol} ${signalData.timeframe} ${signalData.type}`);
        return null;
      }

      const signal = new TradingSignal(signalData);
      await signal.save();

      logger.logInfo(`Signal saved: ${signalData.symbol} ${signalData.timeframe} ${signalData.type} at ${signalData.entry}`);
      return signal;
    } catch (error) {
      logger.logError(`Error saving signal:`, error.message);
      return null;
    }
  }

  /**
   * Lấy tất cả signals đang active
   */
  async getActiveSignals() {
    try {
      return await TradingSignal.getActiveSignals();
    } catch (error) {
      logger.logError(`Error getting active signals:`, error.message);
      return [];
    }
  }

  /**
   * Cleanup signals zombie (active trong DB nhưng không được monitor)
   */
  async cleanupZombieSignals() {
    try {
      const activeSignalsInDB = await TradingSignal.getActiveSignals();
      const orderManager = require('./orderManager');
      let cleanedCount = 0;

      for (const signal of activeSignalsInDB) {
        const isBeingMonitored = orderManager.activeSignals.has(signal._id.toString());

        if (!isBeingMonitored) {
          // Signal active trong DB nhưng không được monitor
          const signalAge = Date.now() - new Date(signal.createdAt).getTime();
          const maxAge = 2 * 60 * 60 * 1000; // 2 giờ

          if (signalAge > maxAge) {
            // Cancel signal zombie
            await this.updateSignalStatus(signal._id, 'cancelled', null, new Date());
            cleanedCount++;

            logger.logInfo(`Cleaned zombie signal: ${signal.symbol} ${signal.timeframe} ${signal.type} - Age: ${Math.round(signalAge / (60 * 60 * 1000))}h`);
          }
        }
      }

      if (cleanedCount > 0) {
        logger.logInfo(`Cleaned up ${cleanedCount} zombie signals`);
      }

      return cleanedCount;
    } catch (error) {
      logger.logError('Error cleaning zombie signals:', error.message);
      return 0;
    }
  }

  /**
   * Apply market condition filtering to signals
   */
  applyMarketConditionFiltering(isValid, signalType, marketCondition, tradingRecommendations, conditions) {
    if (!isValid) return false;

    const { condition, confidence, riskLevel } = marketCondition;
    const { additionalFilters } = tradingRecommendations;

    // 1. SIDEWAYS MARKET - Tăng yêu cầu confirmation
    if (condition === 'sideways' && confidence > 0.7) {
      logger.logInfo(`Sideways market detected (${confidence.toFixed(2)}) - applying stricter filters`);

      // Yêu cầu volume confirmation mạnh hơn
      if (!conditions.volumeConfirmation) {
        logger.logInfo(`Signal rejected: No volume confirmation in sideways market`);
        return false;
      }

      // Yêu cầu MACD crossover mạnh
      if (!conditions.macdCrossover) {
        logger.logInfo(`Signal rejected: No MACD crossover in sideways market`);
        return false;
      }

      // Yêu cầu RSI zone phù hợp
      if (!conditions.rsiZone) {
        logger.logInfo(`Signal rejected: RSI not in proper zone for sideways market`);
        return false;
      }
    }

    // 2. FALSE BREAKOUT PRONE - Tránh breakout signals
    if (condition === 'false_breakout_prone' && confidence > 0.4) {
      logger.logInfo(`False breakout prone market detected (${confidence.toFixed(2)})`);

      // Yêu cầu momentum confirmation mạnh hơn
      const momentumConfirmed = signalType === 'BUY' ?
        (conditions.priceAboveEMA50 && conditions.emaSlope && conditions.macdHistogramPositive) :
        (conditions.priceBelowEMA50 && conditions.emaSlope && conditions.macdHistogramNegative);

      if (!momentumConfirmed) {
        logger.logInfo(`Signal rejected: Insufficient momentum in false breakout prone market`);
        return false;
      }
    }

    // 3. HIGH VOLATILITY - Reject tất cả signals
    if (condition === 'high_volatility' && confidence > 0.6) {
      logger.logInfo(`High volatility market detected (${confidence.toFixed(2)}) - rejecting signal`);
      return false;
    }

    // 4. STRONG TREND - Favor trend direction
    if ((condition === 'strong_uptrend' || condition === 'strong_downtrend') && confidence > 0.7) {
      const trendDirection = condition === 'strong_uptrend' ? 'BUY' : 'SELL';

      if (signalType !== trendDirection) {
        logger.logInfo(`Signal against strong trend (${condition}) - requiring extra confirmation`);

        // Yêu cầu tất cả conditions phải pass
        const allConditionsPassed = Object.values(conditions).every(c => c === true);
        if (!allConditionsPassed) {
          logger.logInfo(`Signal rejected: Not all conditions met for counter-trend trade`);
          return false;
        }
      }
    }

    // 5. Additional filters from recommendations
    if (additionalFilters.includes('require_volume_confirmation') && !conditions.volumeConfirmation) {
      logger.logInfo(`Signal rejected: Volume confirmation required by market condition`);
      return false;
    }

    if (additionalFilters.includes('avoid_breakout_signals')) {
      // Check if this looks like a breakout signal (price near recent high/low)
      // This is a simplified check - could be enhanced
      if (conditions.strongBodyOrEngulfing) {
        logger.logInfo(`Signal rejected: Avoiding potential breakout signal`);
        return false;
      }
    }

    // Signal passed all market condition filters
    logger.logInfo(`Signal passed market condition filtering: ${condition} (${confidence.toFixed(2)})`);
    return true;
  }

  /**
   * Cập nhật trạng thái signal
   */
  async updateSignalStatus(signalId, status, exitPrice, exitTime) {
    try {
      const signal = await TradingSignal.findById(signalId);
      if (!signal) {
        return null;
      }

      signal.status = status;
      signal.exitPrice = exitPrice;
      signal.exitTime = exitTime || new Date();

      // Tính toán P&L
      signal.pnlPercent = signal.calculatePnL(exitPrice);

      await signal.save();

      logger.logInfo(`Signal updated: ${signal.symbol} ${signal.type} - ${status} at ${exitPrice}`);
      return signal;
    } catch (error) {
      logger.logError(`Error updating signal status:`, error.message);
      return null;
    }
  }
}

module.exports = new SignalAnalyzer();
