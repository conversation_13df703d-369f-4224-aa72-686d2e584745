const mongoose = require('mongoose');

/**
 * Performance Analytics Schema for storing AI analysis results and trade performance data
 */
const performanceAnalyticsSchema = new mongoose.Schema({
  // Analysis identification
  analysisId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  // Analysis metadata
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },

  timeframe: {
    type: String,
    required: true,
    enum: ['7d', '30d', '90d', '1y'],
    index: true
  },

  analysisType: {
    type: String,
    required: true,
    enum: ['performance_review', 'parameter_optimization', 'risk_assessment', 'symbol_analysis'],
    index: true
  },

  // Trade statistics used for analysis
  tradeStatistics: {
    totalTrades: { type: Number, required: true },
    winningTrades: { type: Number, required: true },
    losingTrades: { type: Number, required: true },
    winRate: { type: Number, required: true },
    profitFactor: { type: Number, required: true },
    sharpeRatio: { type: Number },
    maxDrawdown: { type: Number, required: true },
    avgWin: { type: Number, required: true },
    avgLoss: { type: Number, required: true },
    expectancy: { type: Number },
    totalPnL: { type: Number, required: true },
    largestWin: { type: Number },
    largestLoss: { type: Number },
    consecutiveWins: { type: Number },
    consecutiveLosses: { type: Number }
  },

  // Symbol-specific performance
  symbolPerformance: [{
    symbol: { type: String, required: true },
    trades: { type: Number, required: true },
    winRate: { type: Number, required: true },
    profitFactor: { type: Number, required: true },
    totalPnL: { type: Number, required: true },
    avgPnL: { type: Number, required: true },
    sharpeRatio: { type: Number },
    maxDrawdown: { type: Number },
    lastTradeDate: { type: Date }
  }],

  // Timeframe-specific performance
  timeframePerformance: [{
    timeframe: { type: String, required: true },
    trades: { type: Number, required: true },
    winRate: { type: Number, required: true },
    profitFactor: { type: Number, required: true },
    totalPnL: { type: Number, required: true },
    avgPnL: { type: Number, required: true },
    sharpeRatio: { type: Number }
  }],

  // Recent losing trades for analysis
  recentLosses: [{
    signalId: { type: String, required: true },
    symbol: { type: String, required: true },
    timeframe: { type: String, required: true },
    entry: { type: Number, required: true },
    exitPrice: { type: Number, required: true },
    pnlPercent: { type: Number, required: true },
    exitReason: { type: String, required: true },
    tradeDate: { type: Date, required: true },
    duration: { type: Number }, // in minutes
    marketCondition: { type: String }
  }],

  // AI analysis results
  aiAnalysis: {
    // AI insights
    insights: [{
      category: {
        type: String,
        required: true,
        enum: ['entry', 'exit', 'risk_management', 'symbol_selection', 'timeframe', 'market_condition']
      },
      finding: { type: String, required: true },
      impact: {
        type: String,
        required: true,
        enum: ['high', 'medium', 'low']
      },
      confidence: {
        type: Number,
        required: true,
        min: 0,
        max: 100
      }
    }],

    // AI recommendations
    recommendations: [{
      type: {
        type: String,
        required: true,
        enum: ['parameter_change', 'strategy_adjustment', 'symbol_allocation', 'risk_adjustment']
      },
      description: { type: String, required: true },
      expectedImprovement: { type: String, required: true },
      riskLevel: {
        type: String,
        required: true,
        enum: ['low', 'medium', 'high']
      },
      priority: {
        type: Number,
        required: true,
        min: 1,
        max: 10
      },
      implementationStatus: {
        type: String,
        default: 'pending',
        enum: ['pending', 'implemented', 'rejected', 'testing']
      },
      implementationDate: { type: Date },
      implementationResults: {
        beforeMetrics: { type: mongoose.Schema.Types.Mixed },
        afterMetrics: { type: mongoose.Schema.Types.Mixed },
        improvement: { type: Number },
        success: { type: Boolean }
      }
    }],

    // Parameter suggestions
    parameterSuggestions: {
      riskManagement: {
        stopLossMultiplier: { type: Number },
        takeProfitMultiplier: { type: Number },
        maxConsecutiveLosses: { type: Number },
        maxDrawdownPercent: { type: Number },
        positionSizeMultiplier: { type: Number }
      },
      symbolAllocations: [{
        symbol: { type: String, required: true },
        currentAllocation: { type: Number },
        recommendedAllocation: { type: Number, required: true },
        reason: { type: String, required: true },
        confidence: { type: Number, min: 0, max: 100 }
      }],
      timeframeOptimization: [{
        timeframe: { type: String, required: true },
        recommendation: {
          type: String,
          required: true,
          enum: ['increase', 'decrease', 'pause', 'maintain']
        },
        reason: { type: String, required: true },
        confidence: { type: Number, min: 0, max: 100 }
      }]
    },

    // Risk assessment
    riskAssessment: {
      currentRiskLevel: {
        type: String,
        required: true,
        enum: ['low', 'medium', 'high', 'critical']
      },
      mainConcerns: [{ type: String }],
      urgentActions: [{ type: String }],
      riskScore: { type: Number, min: 0, max: 100 }
    },

    // AI confidence and metadata
    confidenceScore: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    },

    aiMetadata: {
      model: { type: String, required: true },
      promptTokens: { type: Number },
      completionTokens: { type: Number },
      totalTokens: { type: Number },
      processingTime: { type: Number }, // in milliseconds
      apiVersion: { type: String }
    }
  },

  // Implementation tracking
  implementationStatus: {
    type: String,
    default: 'pending',
    enum: ['pending', 'partial', 'completed', 'failed'],
    index: true
  },

  implementationResults: {
    implementedRecommendations: { type: Number, default: 0 },
    totalRecommendations: { type: Number, default: 0 },
    implementationDate: { type: Date },
    rollbackDate: { type: Date },
    rollbackReason: { type: String },
    performanceImprovement: { type: Number }, // percentage improvement
    success: { type: Boolean }
  },

  // Validation and comparison data
  validationData: {
    beforeImplementation: {
      winRate: { type: Number },
      profitFactor: { type: Number },
      sharpeRatio: { type: Number },
      maxDrawdown: { type: Number },
      totalPnL: { type: Number }
    },
    afterImplementation: {
      winRate: { type: Number },
      profitFactor: { type: Number },
      sharpeRatio: { type: Number },
      maxDrawdown: { type: Number },
      totalPnL: { type: Number }
    },
    validationPeriod: { type: String }, // e.g., "30d"
    validationStartDate: { type: Date },
    validationEndDate: { type: Date }
  }
}, {
  timestamps: true,
  collection: 'performance_analytics'
});

// Indexes for efficient querying
performanceAnalyticsSchema.index({ timestamp: -1 });
performanceAnalyticsSchema.index({ timeframe: 1, timestamp: -1 });
performanceAnalyticsSchema.index({ analysisType: 1, timestamp: -1 });
performanceAnalyticsSchema.index({ 'aiAnalysis.confidenceScore': -1 });
performanceAnalyticsSchema.index({ implementationStatus: 1 });
performanceAnalyticsSchema.index({ 'aiAnalysis.riskAssessment.currentRiskLevel': 1 });

// Virtual for implementation success rate
performanceAnalyticsSchema.virtual('implementationSuccessRate').get(function() {
  if (this.implementationResults.totalRecommendations === 0) return 0;
  return (this.implementationResults.implementedRecommendations / this.implementationResults.totalRecommendations) * 100;
});

// Static methods for common queries
performanceAnalyticsSchema.statics.getLatestAnalysis = function(timeframe = '30d') {
  return this.findOne({ timeframe })
    .sort({ timestamp: -1 })
    .exec();
};

performanceAnalyticsSchema.statics.getAnalysisByDateRange = function(startDate, endDate, timeframe = null) {
  const query = {
    timestamp: {
      $gte: startDate,
      $lte: endDate
    }
  };

  if (timeframe) {
    query.timeframe = timeframe;
  }

  return this.find(query)
    .sort({ timestamp: -1 })
    .exec();
};

performanceAnalyticsSchema.statics.getPendingRecommendations = function() {
  return this.find({
    'aiAnalysis.recommendations.implementationStatus': 'pending',
    'aiAnalysis.confidenceScore': { $gte: 70 }
  })
    .sort({ 'aiAnalysis.confidenceScore': -1, timestamp: -1 })
    .exec();
};

performanceAnalyticsSchema.statics.getHighRiskAnalyses = function() {
  return this.find({
    'aiAnalysis.riskAssessment.currentRiskLevel': { $in: ['high', 'critical'] }
  })
    .sort({ timestamp: -1 })
    .exec();
};

// Instance methods
performanceAnalyticsSchema.methods.markRecommendationImplemented = function(recommendationIndex, results) {
  if (this.aiAnalysis.recommendations[recommendationIndex]) {
    this.aiAnalysis.recommendations[recommendationIndex].implementationStatus = 'implemented';
    this.aiAnalysis.recommendations[recommendationIndex].implementationDate = new Date();
    this.aiAnalysis.recommendations[recommendationIndex].implementationResults = results;

    // Update overall implementation status
    const implementedCount = this.aiAnalysis.recommendations.filter(r => r.implementationStatus === 'implemented').length;
    this.implementationResults.implementedRecommendations = implementedCount;

    if (implementedCount === this.aiAnalysis.recommendations.length) {
      this.implementationStatus = 'completed';
    } else if (implementedCount > 0) {
      this.implementationStatus = 'partial';
    }
  }

  return this.save();
};

performanceAnalyticsSchema.methods.calculateOverallImprovement = function() {
  if (!this.validationData.beforeImplementation || !this.validationData.afterImplementation) {
    return null;
  }

  const before = this.validationData.beforeImplementation;
  const after = this.validationData.afterImplementation;

  // Calculate weighted improvement score
  const winRateImprovement = ((after.winRate - before.winRate) / before.winRate) * 100;
  const profitFactorImprovement = ((after.profitFactor - before.profitFactor) / before.profitFactor) * 100;
  const sharpeImprovement = after.sharpeRatio && before.sharpeRatio ?
    ((after.sharpeRatio - before.sharpeRatio) / Math.abs(before.sharpeRatio)) * 100 : 0;
  const drawdownImprovement = ((before.maxDrawdown - after.maxDrawdown) / before.maxDrawdown) * 100;

  // Weighted average (profit factor and drawdown are most important)
  const overallImprovement = (
    winRateImprovement * 0.2 +
    profitFactorImprovement * 0.4 +
    sharpeImprovement * 0.2 +
    drawdownImprovement * 0.2
  );

  this.implementationResults.performanceImprovement = overallImprovement;
  this.implementationResults.success = overallImprovement > 0;

  return overallImprovement;
};

const PerformanceAnalytics = mongoose.model('PerformanceAnalytics', performanceAnalyticsSchema);

module.exports = PerformanceAnalytics;