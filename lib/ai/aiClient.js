const axios = require('axios');
const config = require('config');
const loggerFactory = require('../logger');
const logger = loggerFactory(config.dirLog || 'logs');

/**
 * AI Client wrapper for external LLM API communication
 * Handles communication with the configured AI service
 */
class AIClient {
  constructor(options = {}) {
    this.apiKey = options.apiKey || config.ai.apiKey;
    this.url = options.url || config.ai.url;
    this.model = options.model || config.ai.model;
    this.timeout = options.timeout || 30000; // 30 seconds
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000; // 1 second
  }

  /**
   * Send a request to the AI service
   * @param {string} prompt - The prompt to send to the AI
   * @param {Object} options - Additional options for the request
   * @returns {Promise<Object>} AI response
   */
  async sendRequest(prompt, options = {}) {
    const requestData = {
      model: this.model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 2000,
      ...options.additionalParams
    };

    let lastError;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        logger.logInfo(`AI request attempt ${attempt}/${this.maxRetries}`, {
          model: this.model,
          promptLength: prompt.length
        });

        const response = await axios.post(`${this.url}/chat/completions`, requestData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          timeout: this.timeout
        });

        if (response.data && response.data.choices && response.data.choices.length > 0) {
          const aiResponse = response.data.choices[0].message.content;

          logger.logInfo('AI request successful', {
            model: this.model,
            responseLength: aiResponse.length,
            usage: response.data.usage
          });

          return {
            success: true,
            content: aiResponse,
            usage: response.data.usage,
            model: this.model,
            timestamp: new Date()
          };
        } else {
          throw new Error('Invalid response format from AI service');
        }

      } catch (error) {
        lastError = error;

        logger.warn(`AI request attempt ${attempt} failed`, {
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText
        });

        // Don't retry on certain errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          break;
        }

        // Wait before retrying (except on last attempt)
        if (attempt < this.maxRetries) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    logger.logError('AI request failed after all retries', {
      error: lastError.message,
      attempts: this.maxRetries
    });

    return {
      success: false,
      error: lastError.message,
      timestamp: new Date()
    };
  }

  /**
   * Analyze trading performance with AI
   * @param {Object} performanceData - Trading performance data
   * @returns {Promise<Object>} AI analysis results
   */
  async analyzePerformance(performanceData) {
    const prompt = this.buildPerformanceAnalysisPrompt(performanceData);

    const response = await this.sendRequest(prompt, {
      temperature: 0.3, // Lower temperature for more consistent analysis
      maxTokens: 3000
    });

    if (!response.success) {
      throw new Error(`AI analysis failed: ${response.error}`);
    }

    try {
      // Try to parse JSON response
      const analysis = JSON.parse(response.content);
      return {
        ...analysis,
        metadata: {
          model: this.model,
          timestamp: response.timestamp,
          usage: response.usage
        }
      };
    } catch (parseError) {
      // If JSON parsing fails, return raw content with error flag
      logger.warn('Failed to parse AI response as JSON', {
        error: parseError.message,
        content: response.content.substring(0, 200)
      });

      return {
        success: false,
        error: 'Failed to parse AI response',
        rawContent: response.content,
        metadata: {
          model: this.model,
          timestamp: response.timestamp,
          usage: response.usage
        }
      };
    }
  }

  /**
   * Get trailing stop recommendations from AI
   * @param {Object} trailingData - Current trailing stop data
   * @returns {Promise<Object>} AI trailing recommendations
   */
  async getTrailingRecommendation(trailingData) {
    const prompt = this.buildTrailingAnalysisPrompt(trailingData);

    const response = await this.sendRequest(prompt, {
      temperature: 0.2, // Very low temperature for consistent trading decisions
      maxTokens: 1000
    });

    if (!response.success) {
      throw new Error(`AI trailing analysis failed: ${response.error}`);
    }

    try {
      const recommendation = JSON.parse(response.content);
      return {
        ...recommendation,
        metadata: {
          model: this.model,
          timestamp: response.timestamp,
          usage: response.usage
        }
      };
    } catch (parseError) {
      logger.warn('Failed to parse AI trailing recommendation', {
        error: parseError.message,
        content: response.content.substring(0, 200)
      });

      return {
        action: 'hold', // Safe default
        confidence: 0,
        reason: 'Failed to parse AI response',
        metadata: {
          model: this.model,
          timestamp: response.timestamp,
          usage: response.usage,
          parseError: parseError.message
        }
      };
    }
  }

  /**
   * Build performance analysis prompt
   * @param {Object} data - Performance data
   * @returns {string} Formatted prompt
   */
  buildPerformanceAnalysisPrompt(data) {
    return `
Phân tích performance trading system với data sau:

TRADE STATISTICS:
- Total trades: ${data.totalTrades}
- Win rate: ${data.winRate}%
- Profit factor: ${data.profitFactor}
- Sharpe ratio: ${data.sharpeRatio}
- Max drawdown: ${data.maxDrawdown}%
- Average win: ${data.avgWin}%
- Average loss: ${data.avgLoss}%

RECENT LOSING TRADES:
${data.recentLosses?.map(trade =>
  `Symbol: ${trade.symbol}, Entry: ${trade.entry}, Exit: ${trade.exitPrice}, Loss: ${trade.pnlPercent}%, Reason: ${trade.exitReason}`
).join('\n') || 'No recent losses data'}

SYMBOL PERFORMANCE:
${data.symbolStats?.map(stat =>
  `${stat.symbol}: WR=${stat.winRate}%, PF=${stat.profitFactor}, Trades=${stat.count}`
).join('\n') || 'No symbol stats data'}

TIMEFRAME PERFORMANCE:
${data.timeframeStats?.map(stat =>
  `${stat.timeframe}: WR=${stat.winRate}%, PF=${stat.profitFactor}, Trades=${stat.count}`
).join('\n') || 'No timeframe stats data'}

Hãy phân tích và đưa ra:
1. Top 3 nguyên nhân chính gây loss
2. Đề xuất tối ưu tham số (RR, SL, TP multipliers)
3. Symbols nào nên tăng/giảm allocation
4. Timeframes nào hiệu quả nhất
5. Risk management improvements
6. Confidence score (0-100) cho các recommendations

Format response as JSON với các fields:
{
  "insights": [
    {
      "category": "entry|exit|risk_management|symbol_selection",
      "finding": "description",
      "impact": "high|medium|low",
      "confidence": 0-100
    }
  ],
  "recommendations": [
    {
      "type": "parameter_change|strategy_adjustment|symbol_allocation",
      "description": "detailed description",
      "expectedImprovement": "description",
      "riskLevel": "low|medium|high",
      "priority": 1-10
    }
  ],
  "parameterSuggestions": {
    "riskManagement": {
      "stopLossMultiplier": number,
      "takeProfitMultiplier": number,
      "maxConsecutiveLosses": number
    },
    "symbolAllocations": [
      {
        "symbol": "string",
        "recommendedAllocation": number,
        "reason": "string"
      }
    ],
    "timeframeOptimization": [
      {
        "timeframe": "string",
        "recommendation": "increase|decrease|pause"
      }
    ]
  },
  "riskAssessment": {
    "currentRiskLevel": "low|medium|high|critical",
    "mainConcerns": ["string"],
    "urgentActions": ["string"]
  },
  "confidenceScore": 0-100
}
`;
  }

  /**
   * Build trailing analysis prompt
   * @param {Object} data - Trailing data
   * @returns {string} Formatted prompt
   */
  buildTrailingAnalysisPrompt(data) {
    return `
Phân tích trailing stop cho trade hiện tại:

TRADE INFO:
- Symbol: ${data.signal?.symbol}
- Entry Price: ${data.signal?.entry}
- Current Price: ${data.currentPrice}
- Current P&L: ${data.currentPnL}%
- Trade Direction: ${data.signal?.type}
- Time in Trade: ${data.timeInTrade || 'unknown'}

MARKET CONDITIONS:
- Recent volatility: ${data.marketData?.volatility || 'unknown'}
- Volume trend: ${data.marketData?.volumeTrend || 'unknown'}
- Market regime: ${data.marketData?.regime || 'unknown'}

CURRENT TRAILING:
- Trailing distance: ${data.currentTrailingDistance}%
- Locked profit: ${data.lockedProfit}%

Đưa ra recommendation cho trailing stop:

Format response as JSON:
{
  "action": "tighten|loosen|hold|partial_exit",
  "newDistance": number (if tighten/loosen),
  "percentage": number (if partial_exit),
  "confidence": 0-100,
  "reason": "detailed explanation",
  "riskLevel": "low|medium|high"
}
`;
  }

  /**
   * Test AI service connectivity
   * @returns {Promise<Object>} Test result
   */
  async testConnection() {
    try {
      const testPrompt = "Test connection. Respond with: {'status': 'connected', 'timestamp': current_time}";
      const response = await this.sendRequest(testPrompt, {
        temperature: 0,
        maxTokens: 100
      });

      return {
        success: response.success,
        connected: response.success,
        model: this.model,
        url: this.url,
        timestamp: new Date(),
        response: response.success ? response.content : response.error
      };
    } catch (error) {
      return {
        success: false,
        connected: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Utility method to add delay
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current configuration
   * @returns {Object} Current AI client configuration
   */
  getConfig() {
    return {
      model: this.model,
      url: this.url,
      timeout: this.timeout,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}

module.exports = AIClient;