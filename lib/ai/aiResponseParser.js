const loggerFactory = require('../logger');
const config = require('config');
const logger = loggerFactory(config.dirLog || 'logs');

/**
 * AI Response Parser and Validator
 * Handles parsing and validation of AI responses for trading analysis
 */
class AIResponseParser {
  constructor(options = {}) {
    this.strictMode = options.strictMode || false;
    this.defaultConfidence = options.defaultConfidence || 50;
    this.maxRetries = options.maxRetries || 3;
  }

  /**
   * Parse and validate AI performance analysis response
   * @param {string} rawResponse - Raw AI response content
   * @param {Object} context - Context for validation
   * @returns {Object} Parsed and validated response
   */
  parsePerformanceAnalysis(rawResponse, context = {}) {
    try {
      // First, try to extract JSON from the response
      const jsonContent = this.extractJSON(rawResponse);

      if (!jsonContent) {
        return this.handleParseFailure(rawResponse, 'No valid JSON found in response');
      }

      // Parse the JSON
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(jsonContent);
      } catch (jsonError) {
        return this.handleParseFailure(rawResponse, `JSON parse error: ${jsonError.message}`);
      }

      // Validate the structure
      const validationResult = this.validatePerformanceAnalysisStructure(parsedResponse);
      if (!validationResult.isValid) {
        return this.handleValidationFailure(parsedResponse, validationResult.errors);
      }

      // Sanitize and normalize the data
      const sanitizedResponse = this.sanitizePerformanceAnalysis(parsedResponse);

      // Add metadata
      sanitizedResponse.parsingMetadata = {
        originalLength: rawResponse.length,
        parsedAt: new Date(),
        validationPassed: true,
        sanitizationApplied: true
      };

      logger.logInfo('AI performance analysis parsed successfully', {
        insightsCount: sanitizedResponse.insights?.length || 0,
        recommendationsCount: sanitizedResponse.recommendations?.length || 0,
        confidenceScore: sanitizedResponse.confidenceScore
      });

      return {
        success: true,
        data: sanitizedResponse,
        rawResponse: this.strictMode ? undefined : rawResponse
      };

    } catch (error) {
      logger.logError('Failed to parse AI performance analysis', {
        error: error.message,
        responseLength: rawResponse?.length || 0
      });

      return this.handleParseFailure(rawResponse, error.message);
    }
  }

  /**
   * Parse and validate AI trailing recommendation response
   * @param {string} rawResponse - Raw AI response content
   * @param {Object} context - Context for validation
   * @returns {Object} Parsed and validated response
   */
  parseTrailingRecommendation(rawResponse, context = {}) {
    try {
      const jsonContent = this.extractJSON(rawResponse);

      if (!jsonContent) {
        return this.createSafeTrailingDefault('No valid JSON found in response');
      }

      let parsedResponse;
      try {
        parsedResponse = JSON.parse(jsonContent);
      } catch (jsonError) {
        return this.createSafeTrailingDefault(`JSON parse error: ${jsonError.message}`);
      }

      // Validate trailing recommendation structure
      const validationResult = this.validateTrailingRecommendationStructure(parsedResponse);
      if (!validationResult.isValid) {
        return this.createSafeTrailingDefault(validationResult.errors.join(', '));
      }

      // Sanitize trailing recommendation
      const sanitizedResponse = this.sanitizeTrailingRecommendation(parsedResponse);

      logger.logInfo('AI trailing recommendation parsed successfully', {
        action: sanitizedResponse.action,
        confidence: sanitizedResponse.confidence
      });

      return {
        success: true,
        data: sanitizedResponse,
        rawResponse: this.strictMode ? undefined : rawResponse
      };

    } catch (error) {
      logger.logError('Failed to parse AI trailing recommendation', {
        error: error.message,
        responseLength: rawResponse?.length || 0
      });

      return this.createSafeTrailingDefault(error.message);
    }
  }

  /**
   * Extract JSON content from AI response
   * @param {string} response - Raw response
   * @returns {string|null} Extracted JSON string
   */
  extractJSON(response) {
    if (!response || typeof response !== 'string') {
      return null;
    }

    // Try to find JSON block markers
    const jsonBlockRegex = /```json\s*([\s\S]*?)\s*```/i;
    const jsonBlockMatch = response.match(jsonBlockRegex);

    if (jsonBlockMatch) {
      return jsonBlockMatch[1].trim();
    }

    // Try to find JSON object by looking for { ... }
    const jsonObjectRegex = /\{[\s\S]*\}/;
    const jsonObjectMatch = response.match(jsonObjectRegex);

    if (jsonObjectMatch) {
      return jsonObjectMatch[0];
    }

    // Try to find JSON array by looking for [ ... ]
    const jsonArrayRegex = /\[[\s\S]*\]/;
    const jsonArrayMatch = response.match(jsonArrayRegex);

    if (jsonArrayMatch) {
      return jsonArrayMatch[0];
    }

    // If no JSON markers found, assume the entire response is JSON
    const trimmed = response.trim();
    if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
        (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
      return trimmed;
    }

    return null;
  }

  /**
   * Validate performance analysis structure
   * @param {Object} data - Parsed data to validate
   * @returns {Object} Validation result
   */
  validatePerformanceAnalysisStructure(data) {
    const errors = [];
    const warnings = [];

    // Check required top-level fields
    if (!data.insights || !Array.isArray(data.insights)) {
      errors.push('Missing or invalid insights array');
    }

    if (!data.recommendations || !Array.isArray(data.recommendations)) {
      errors.push('Missing or invalid recommendations array');
    }

    if (typeof data.confidenceScore !== 'number' || data.confidenceScore < 0 || data.confidenceScore > 100) {
      errors.push('Missing or invalid confidenceScore (must be 0-100)');
    }

    // Validate insights structure
    if (data.insights && Array.isArray(data.insights)) {
      data.insights.forEach((insight, index) => {
        if (!insight.category || !insight.finding) {
          errors.push(`Insight ${index}: missing category or finding`);
        }

        if (!['entry', 'exit', 'risk_management', 'symbol_selection', 'timeframe', 'market_condition'].includes(insight.category)) {
          warnings.push(`Insight ${index}: invalid category '${insight.category}'`);
        }

        if (!['high', 'medium', 'low'].includes(insight.impact)) {
          warnings.push(`Insight ${index}: invalid impact '${insight.impact}'`);
        }

        if (typeof insight.confidence !== 'number' || insight.confidence < 0 || insight.confidence > 100) {
          warnings.push(`Insight ${index}: invalid confidence score`);
        }
      });
    }

    // Validate recommendations structure
    if (data.recommendations && Array.isArray(data.recommendations)) {
      data.recommendations.forEach((rec, index) => {
        if (!rec.type || !rec.description) {
          errors.push(`Recommendation ${index}: missing type or description`);
        }

        if (!['parameter_change', 'strategy_adjustment', 'symbol_allocation', 'risk_adjustment'].includes(rec.type)) {
          warnings.push(`Recommendation ${index}: invalid type '${rec.type}'`);
        }

        if (!['low', 'medium', 'high'].includes(rec.riskLevel)) {
          warnings.push(`Recommendation ${index}: invalid riskLevel '${rec.riskLevel}'`);
        }

        if (typeof rec.priority !== 'number' || rec.priority < 1 || rec.priority > 10) {
          warnings.push(`Recommendation ${index}: invalid priority (must be 1-10)`);
        }
      });
    }

    // Validate risk assessment
    if (data.riskAssessment) {
      if (!['low', 'medium', 'high', 'critical'].includes(data.riskAssessment.currentRiskLevel)) {
        warnings.push('Invalid risk assessment level');
      }
    }

    if (warnings.length > 0) {
      logger.warn('AI response validation warnings', { warnings });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate trailing recommendation structure
   * @param {Object} data - Parsed data to validate
   * @returns {Object} Validation result
   */
  validateTrailingRecommendationStructure(data) {
    const errors = [];
    const warnings = [];

    // Check required fields
    if (!data.action || !['tighten', 'loosen', 'hold', 'partial_exit'].includes(data.action)) {
      errors.push('Missing or invalid action (must be: tighten, loosen, hold, partial_exit)');
    }

    if (typeof data.confidence !== 'number' || data.confidence < 0 || data.confidence > 100) {
      errors.push('Missing or invalid confidence (must be 0-100)');
    }

    if (!data.reason || typeof data.reason !== 'string') {
      errors.push('Missing or invalid reason');
    }

    // Validate action-specific fields
    if (data.action === 'tighten' || data.action === 'loosen') {
      if (typeof data.newDistance !== 'number' || data.newDistance <= 0) {
        errors.push('Missing or invalid newDistance for tighten/loosen action');
      }
    }

    if (data.action === 'partial_exit') {
      if (typeof data.percentage !== 'number' || data.percentage <= 0 || data.percentage > 100) {
        errors.push('Missing or invalid percentage for partial_exit action (must be 1-100)');
      }
    }

    if (data.riskLevel && !['low', 'medium', 'high'].includes(data.riskLevel)) {
      warnings.push(`Invalid riskLevel '${data.riskLevel}'`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Sanitize performance analysis data
   * @param {Object} data - Data to sanitize
   * @returns {Object} Sanitized data
   */
  sanitizePerformanceAnalysis(data) {
    const sanitized = {
      insights: [],
      recommendations: [],
      parameterSuggestions: {},
      riskAssessment: {},
      confidenceScore: 0
    };

    // Sanitize insights
    if (data.insights && Array.isArray(data.insights)) {
      sanitized.insights = data.insights.map(insight => ({
        category: this.sanitizeCategory(insight.category),
        finding: this.sanitizeString(insight.finding, 500),
        impact: this.sanitizeImpact(insight.impact),
        confidence: this.sanitizeConfidence(insight.confidence)
      })).filter(insight => insight.category && insight.finding);
    }

    // Sanitize recommendations
    if (data.recommendations && Array.isArray(data.recommendations)) {
      sanitized.recommendations = data.recommendations.map(rec => ({
        type: this.sanitizeRecommendationType(rec.type),
        description: this.sanitizeString(rec.description, 1000),
        expectedImprovement: this.sanitizeString(rec.expectedImprovement, 500),
        riskLevel: this.sanitizeRiskLevel(rec.riskLevel),
        priority: this.sanitizePriority(rec.priority)
      })).filter(rec => rec.type && rec.description);
    }

    // Sanitize parameter suggestions
    if (data.parameterSuggestions) {
      sanitized.parameterSuggestions = this.sanitizeParameterSuggestions(data.parameterSuggestions);
    }

    // Sanitize risk assessment
    if (data.riskAssessment) {
      sanitized.riskAssessment = {
        currentRiskLevel: this.sanitizeRiskLevel(data.riskAssessment.currentRiskLevel) || 'medium',
        mainConcerns: Array.isArray(data.riskAssessment.mainConcerns) ?
          data.riskAssessment.mainConcerns.map(c => this.sanitizeString(c, 200)).filter(Boolean) : [],
        urgentActions: Array.isArray(data.riskAssessment.urgentActions) ?
          data.riskAssessment.urgentActions.map(a => this.sanitizeString(a, 200)).filter(Boolean) : [],
        riskScore: this.sanitizeConfidence(data.riskAssessment.riskScore)
      };
    }

    // Sanitize confidence score
    sanitized.confidenceScore = this.sanitizeConfidence(data.confidenceScore);

    return sanitized;
  }

  /**
   * Sanitize trailing recommendation data
   * @param {Object} data - Data to sanitize
   * @returns {Object} Sanitized data
   */
  sanitizeTrailingRecommendation(data) {
    const sanitized = {
      action: this.sanitizeTrailingAction(data.action),
      confidence: this.sanitizeConfidence(data.confidence),
      reason: this.sanitizeString(data.reason, 500),
      riskLevel: this.sanitizeRiskLevel(data.riskLevel) || 'medium'
    };

    // Add action-specific fields
    if (data.action === 'tighten' || data.action === 'loosen') {
      sanitized.newDistance = this.sanitizeNumber(data.newDistance, 0.1, 10);
    }

    if (data.action === 'partial_exit') {
      sanitized.percentage = this.sanitizeNumber(data.percentage, 1, 100);
    }

    return sanitized;
  }

  /**
   * Sanitization helper methods
   */
  sanitizeString(value, maxLength = 1000) {
    if (typeof value !== 'string') return '';
    return value.trim().substring(0, maxLength);
  }

  sanitizeNumber(value, min = 0, max = 100) {
    const num = parseFloat(value);
    if (isNaN(num)) return min;
    return Math.max(min, Math.min(max, num));
  }

  sanitizeConfidence(value) {
    return this.sanitizeNumber(value, 0, 100);
  }

  sanitizePriority(value) {
    return Math.round(this.sanitizeNumber(value, 1, 10));
  }

  sanitizeCategory(value) {
    const validCategories = ['entry', 'exit', 'risk_management', 'symbol_selection', 'timeframe', 'market_condition'];
    return validCategories.includes(value) ? value : 'risk_management';
  }

  sanitizeImpact(value) {
    const validImpacts = ['high', 'medium', 'low'];
    return validImpacts.includes(value) ? value : 'medium';
  }

  sanitizeRiskLevel(value) {
    const validRiskLevels = ['low', 'medium', 'high', 'critical'];
    return validRiskLevels.includes(value) ? value : 'medium';
  }

  sanitizeRecommendationType(value) {
    const validTypes = ['parameter_change', 'strategy_adjustment', 'symbol_allocation', 'risk_adjustment'];
    return validTypes.includes(value) ? value : 'parameter_change';
  }

  sanitizeTrailingAction(value) {
    const validActions = ['tighten', 'loosen', 'hold', 'partial_exit'];
    return validActions.includes(value) ? value : 'hold';
  }

  sanitizeParameterSuggestions(suggestions) {
    const sanitized = {};

    if (suggestions.riskManagement) {
      sanitized.riskManagement = {
        stopLossMultiplier: this.sanitizeNumber(suggestions.riskManagement.stopLossMultiplier, 0.5, 3.0),
        takeProfitMultiplier: this.sanitizeNumber(suggestions.riskManagement.takeProfitMultiplier, 1.0, 5.0),
        maxConsecutiveLosses: Math.round(this.sanitizeNumber(suggestions.riskManagement.maxConsecutiveLosses, 3, 10)),
        maxDrawdownPercent: this.sanitizeNumber(suggestions.riskManagement.maxDrawdownPercent, 5, 25),
        positionSizeMultiplier: this.sanitizeNumber(suggestions.riskManagement.positionSizeMultiplier, 0.1, 2.0)
      };
    }

    if (suggestions.symbolAllocations && Array.isArray(suggestions.symbolAllocations)) {
      sanitized.symbolAllocations = suggestions.symbolAllocations.map(alloc => ({
        symbol: this.sanitizeString(alloc.symbol, 20),
        currentAllocation: this.sanitizeNumber(alloc.currentAllocation, 0, 100),
        recommendedAllocation: this.sanitizeNumber(alloc.recommendedAllocation, 0, 100),
        reason: this.sanitizeString(alloc.reason, 200),
        confidence: this.sanitizeConfidence(alloc.confidence)
      })).filter(alloc => alloc.symbol);
    }

    if (suggestions.timeframeOptimization && Array.isArray(suggestions.timeframeOptimization)) {
      sanitized.timeframeOptimization = suggestions.timeframeOptimization.map(opt => ({
        timeframe: this.sanitizeString(opt.timeframe, 10),
        recommendation: ['increase', 'decrease', 'pause', 'maintain'].includes(opt.recommendation) ?
          opt.recommendation : 'maintain',
        reason: this.sanitizeString(opt.reason, 200),
        confidence: this.sanitizeConfidence(opt.confidence)
      })).filter(opt => opt.timeframe);
    }

    return sanitized;
  }

  /**
   * Handle parse failures with safe defaults
   */
  handleParseFailure(rawResponse, error) {
    logger.warn('AI response parse failure, using safe defaults', { error });

    return {
      success: false,
      error: error,
      data: this.createSafeAnalysisDefault(),
      rawResponse: this.strictMode ? undefined : rawResponse
    };
  }

  handleValidationFailure(parsedResponse, errors) {
    logger.warn('AI response validation failure', { errors });

    return {
      success: false,
      error: `Validation failed: ${errors.join(', ')}`,
      data: this.createSafeAnalysisDefault(),
      rawResponse: this.strictMode ? undefined : parsedResponse
    };
  }

  createSafeAnalysisDefault() {
    return {
      insights: [{
        category: 'risk_management',
        finding: 'Unable to parse AI analysis - using safe defaults',
        impact: 'low',
        confidence: 0
      }],
      recommendations: [{
        type: 'risk_adjustment',
        description: 'Review AI service configuration and retry analysis',
        expectedImprovement: 'Improved analysis accuracy',
        riskLevel: 'low',
        priority: 5
      }],
      parameterSuggestions: {},
      riskAssessment: {
        currentRiskLevel: 'medium',
        mainConcerns: ['AI analysis unavailable'],
        urgentActions: ['Check AI service connectivity'],
        riskScore: 50
      },
      confidenceScore: 0
    };
  }

  createSafeTrailingDefault(error) {
    logger.warn('Using safe trailing default', { error });

    return {
      success: false,
      error: error,
      data: {
        action: 'hold',
        confidence: 0,
        reason: `AI analysis failed: ${error}`,
        riskLevel: 'medium'
      }
    };
  }
}

module.exports = AIResponseParser;