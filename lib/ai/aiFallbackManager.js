const loggerFactory = require('../logger');
const config = require('config');
const logger = loggerFactory(config.dirLog || 'logs');

/**
 * AI Fallback Manager
 * Handles AI service failures and provides fallback mechanisms
 */
class AIFallbackManager {
  constructor(options = {}) {
    this.fallbackEnabled = options.fallbackEnabled !== false;
    this.cacheEnabled = options.cacheEnabled !== false;
    this.maxCacheAge = options.maxCacheAge || 24 * 60 * 60 * 1000; // 24 hours
    this.fallbackCache = new Map();
    this.serviceHealthStatus = {
      isHealthy: true,
      lastCheck: new Date(),
      consecutiveFailures: 0,
      lastSuccessfulCall: new Date()
    };
    this.maxConsecutiveFailures = options.maxConsecutiveFailures || 5;
    this.healthCheckInterval = options.healthCheckInterval || 5 * 60 * 1000; // 5 minutes
    this.circuitBreakerTimeout = options.circuitBreakerTimeout || 30 * 60 * 1000; // 30 minutes
  }

  /**
   * Execute AI analysis with fallback handling
   * @param {Function} aiFunction - AI function to execute
   * @param {Object} params - Parameters for the AI function
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} Analysis result with fallback handling
   */
  async executeWithFallback(aiFunction, params, options = {}) {
    const cacheKey = this.generateCacheKey(aiFunction.name, params);

    try {
      // Check circuit breaker
      if (!this.isServiceHealthy()) {
        logger.warn('AI service circuit breaker is open, using fallback');
        return await this.handleFallback(cacheKey, params, options);
      }

      // Try to execute AI function
      const result = await this.executeWithTimeout(aiFunction, params, options.timeout);

      // Success - update health status and cache result
      this.recordSuccess();
      if (this.cacheEnabled) {
        this.cacheResult(cacheKey, result);
      }

      return {
        success: true,
        source: 'ai',
        data: result,
        timestamp: new Date()
      };

    } catch (error) {
      logger.logError('AI function execution failed', {
        function: aiFunction.name,
        error: error.message,
        consecutiveFailures: this.serviceHealthStatus.consecutiveFailures + 1
      });

      // Record failure and handle fallback
      this.recordFailure(error);
      return await this.handleFallback(cacheKey, params, options);
    }
  }

  /**
   * Handle fallback when AI service fails
   * @param {string} cacheKey - Cache key for the request
   * @param {Object} params - Original parameters
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} Fallback result
   */
  async handleFallback(cacheKey, params, options = {}) {
    if (!this.fallbackEnabled) {
      throw new Error('AI service unavailable and fallback is disabled');
    }

    // Try cached result first
    if (this.cacheEnabled) {
      const cachedResult = this.getCachedResult(cacheKey);
      if (cachedResult) {
        logger.logInfo('Using cached AI result as fallback', { cacheKey });
        return {
          success: true,
          source: 'cache',
          data: cachedResult.data,
          timestamp: cachedResult.timestamp,
          cacheAge: Date.now() - cachedResult.timestamp.getTime()
        };
      }
    }

    // Use rule-based fallback
    const fallbackResult = await this.generateRuleBasedFallback(params, options);

    return {
      success: true,
      source: 'fallback',
      data: fallbackResult,
      timestamp: new Date(),
      warning: 'AI service unavailable - using rule-based fallback'
    };
  }

  /**
   * Generate rule-based fallback analysis
   * @param {Object} params - Analysis parameters
   * @param {Object} options - Options
   * @returns {Promise<Object>} Rule-based analysis
   */
  async generateRuleBasedFallback(params, options = {}) {
    const analysisType = options.analysisType || 'performance';

    if (analysisType === 'performance') {
      return this.generatePerformanceFallback(params);
    } else if (analysisType === 'trailing') {
      return this.generateTrailingFallback(params);
    } else {
      return this.generateGenericFallback(params);
    }
  }

  /**
   * Generate performance analysis fallback
   * @param {Object} performanceData - Performance data
   * @returns {Object} Rule-based performance analysis
   */
  generatePerformanceFallback(performanceData) {
    const insights = [];
    const recommendations = [];
    let riskLevel = 'medium';
    let confidenceScore = 60; // Lower confidence for rule-based analysis

    // Analyze win rate
    if (performanceData.winRate < 40) {
      insights.push({
        category: 'risk_management',
        finding: 'Win rate is below 40% - indicates potential issues with entry criteria or market conditions',
        impact: 'high',
        confidence: 80
      });

      recommendations.push({
        type: 'strategy_adjustment',
        description: 'Review and tighten entry criteria to improve signal quality',
        expectedImprovement: 'Increase win rate by 5-10%',
        riskLevel: 'low',
        priority: 8
      });

      riskLevel = 'high';
    } else if (performanceData.winRate > 70) {
      insights.push({
        category: 'entry',
        finding: 'High win rate indicates good entry criteria but may suggest overly conservative approach',
        impact: 'medium',
        confidence: 70
      });
    }

    // Analyze profit factor
    if (performanceData.profitFactor < 1.2) {
      insights.push({
        category: 'risk_management',
        finding: 'Low profit factor suggests poor risk/reward ratio or exit strategy issues',
        impact: 'high',
        confidence: 85
      });

      recommendations.push({
        type: 'parameter_change',
        description: 'Increase take profit targets or improve trailing stop strategy',
        expectedImprovement: 'Improve profit factor to 1.5+',
        riskLevel: 'medium',
        priority: 9
      });

      riskLevel = 'high';
    }

    // Analyze drawdown
    if (performanceData.maxDrawdown > 15) {
      insights.push({
        category: 'risk_management',
        finding: 'High maximum drawdown indicates insufficient risk management',
        impact: 'high',
        confidence: 90
      });

      recommendations.push({
        type: 'risk_adjustment',
        description: 'Implement stricter position sizing and stop loss management',
        expectedImprovement: 'Reduce maximum drawdown to under 10%',
        riskLevel: 'low',
        priority: 10
      });

      riskLevel = 'critical';
    }

    // Analyze consecutive losses
    if (performanceData.consecutiveLosses > 5) {
      insights.push({
        category: 'risk_management',
        finding: 'High consecutive losses indicate need for trading pause mechanisms',
        impact: 'high',
        confidence: 85
      });

      recommendations.push({
        type: 'risk_adjustment',
        description: 'Implement automatic trading pause after 3-4 consecutive losses',
        expectedImprovement: 'Prevent extended losing streaks',
        riskLevel: 'low',
        priority: 8
      });
    }

    // Symbol performance analysis
    if (performanceData.symbolStats && performanceData.symbolStats.length > 0) {
      const poorPerformers = performanceData.symbolStats.filter(s => s.profitFactor < 1.0);
      if (poorPerformers.length > 0) {
        insights.push({
          category: 'symbol_selection',
          finding: `${poorPerformers.length} symbols showing negative performance`,
          impact: 'medium',
          confidence: 75
        });

        recommendations.push({
          type: 'symbol_allocation',
          description: 'Reduce or pause trading on underperforming symbols',
          expectedImprovement: 'Focus resources on profitable symbols',
          riskLevel: 'low',
          priority: 6
        });
      }
    }

    // Generate parameter suggestions based on performance
    const parameterSuggestions = this.generateParameterSuggestions(performanceData);

    return {
      insights,
      recommendations,
      parameterSuggestions,
      riskAssessment: {
        currentRiskLevel: riskLevel,
        mainConcerns: this.identifyMainConcerns(performanceData),
        urgentActions: this.identifyUrgentActions(performanceData, riskLevel),
        riskScore: this.calculateRiskScore(performanceData)
      },
      confidenceScore,
      metadata: {
        source: 'rule_based_fallback',
        analysisDate: new Date(),
        fallbackReason: 'AI service unavailable'
      }
    };
  }

  /**
   * Generate trailing recommendation fallback
   * @param {Object} trailingData - Trailing data
   * @returns {Object} Rule-based trailing recommendation
   */
  generateTrailingFallback(trailingData) {
    let action = 'hold';
    let confidence = 50;
    let reason = 'Conservative approach due to AI service unavailability';
    let riskLevel = 'medium';

    const currentPnL = trailingData.currentPnL || 0;
    const timeInTrade = trailingData.timeInTrade || 0;

    // Simple rule-based logic
    if (currentPnL > 2.0) { // More than 2% profit
      if (timeInTrade > 60) { // More than 1 hour in trade
        action = 'tighten';
        confidence = 70;
        reason = 'Significant profit achieved, tightening to lock gains';
        riskLevel = 'low';
      } else {
        action = 'hold';
        confidence = 60;
        reason = 'Good profit but early in trade, holding current trailing';
      }
    } else if (currentPnL > 0.5) { // Small profit
      action = 'hold';
      confidence = 65;
      reason = 'Small profit, maintaining current trailing distance';
    } else if (currentPnL < -0.5) { // Small loss
      action = 'hold';
      confidence = 55;
      reason = 'Small loss, maintaining trailing to avoid premature exit';
      riskLevel = 'medium';
    }

    return {
      action,
      confidence,
      reason,
      riskLevel,
      metadata: {
        source: 'rule_based_fallback',
        fallbackReason: 'AI service unavailable'
      }
    };
  }

  /**
   * Generate generic fallback
   * @param {Object} params - Parameters
   * @returns {Object} Generic fallback response
   */
  generateGenericFallback(params) {
    return {
      action: 'hold',
      confidence: 30,
      reason: 'AI service unavailable - using conservative default',
      riskLevel: 'medium',
      recommendations: [{
        type: 'risk_adjustment',
        description: 'Check AI service connectivity and retry analysis',
        expectedImprovement: 'Restore full AI analysis capabilities',
        riskLevel: 'low',
        priority: 5
      }],
      metadata: {
        source: 'generic_fallback',
        fallbackReason: 'AI service unavailable'
      }
    };
  }

  /**
   * Generate parameter suggestions based on performance data
   * @param {Object} performanceData - Performance data
   * @returns {Object} Parameter suggestions
   */
  generateParameterSuggestions(performanceData) {
    const suggestions = {
      riskManagement: {},
      symbolAllocations: [],
      timeframeOptimization: []
    };

    // Risk management suggestions
    if (performanceData.maxDrawdown > 10) {
      suggestions.riskManagement.stopLossMultiplier = Math.max(0.8, 1.0 - (performanceData.maxDrawdown - 10) * 0.02);
    }

    if (performanceData.profitFactor < 1.2) {
      suggestions.riskManagement.takeProfitMultiplier = Math.min(3.0, 2.0 + (1.2 - performanceData.profitFactor));
    }

    if (performanceData.consecutiveLosses > 3) {
      suggestions.riskManagement.maxConsecutiveLosses = Math.max(3, performanceData.consecutiveLosses - 1);
    }

    // Symbol allocation suggestions
    if (performanceData.symbolStats) {
      performanceData.symbolStats.forEach(symbol => {
        if (symbol.profitFactor < 1.0) {
          suggestions.symbolAllocations.push({
            symbol: symbol.symbol,
            recommendedAllocation: 0,
            reason: 'Negative performance - recommend pause',
            confidence: 70
          });
        } else if (symbol.profitFactor > 1.5) {
          suggestions.symbolAllocations.push({
            symbol: symbol.symbol,
            recommendedAllocation: 150,
            reason: 'Strong performance - recommend increase',
            confidence: 65
          });
        }
      });
    }

    return suggestions;
  }

  /**
   * Service health management methods
   */
  isServiceHealthy() {
    const now = new Date();

    // If we haven't checked recently, assume healthy
    if (now - this.serviceHealthStatus.lastCheck > this.healthCheckInterval) {
      return true;
    }

    // Circuit breaker logic
    if (this.serviceHealthStatus.consecutiveFailures >= this.maxConsecutiveFailures) {
      const timeSinceLastFailure = now - this.serviceHealthStatus.lastCheck;
      return timeSinceLastFailure > this.circuitBreakerTimeout;
    }

    return this.serviceHealthStatus.isHealthy;
  }

  recordSuccess() {
    this.serviceHealthStatus.isHealthy = true;
    this.serviceHealthStatus.consecutiveFailures = 0;
    this.serviceHealthStatus.lastSuccessfulCall = new Date();
    this.serviceHealthStatus.lastCheck = new Date();
  }

  recordFailure(error) {
    this.serviceHealthStatus.consecutiveFailures++;
    this.serviceHealthStatus.lastCheck = new Date();

    if (this.serviceHealthStatus.consecutiveFailures >= this.maxConsecutiveFailures) {
      this.serviceHealthStatus.isHealthy = false;
      logger.warn('AI service marked as unhealthy due to consecutive failures', {
        consecutiveFailures: this.serviceHealthStatus.consecutiveFailures,
        error: error.message
      });
    }
  }

  /**
   * Cache management methods
   */
  generateCacheKey(functionName, params) {
    const paramString = JSON.stringify(params, Object.keys(params).sort());
    return `${functionName}_${Buffer.from(paramString).toString('base64').substring(0, 32)}`;
  }

  cacheResult(key, result) {
    this.fallbackCache.set(key, {
      data: result,
      timestamp: new Date()
    });

    // Clean old cache entries
    this.cleanCache();
  }

  getCachedResult(key) {
    const cached = this.fallbackCache.get(key);
    if (!cached) return null;

    const age = Date.now() - cached.timestamp.getTime();
    if (age > this.maxCacheAge) {
      this.fallbackCache.delete(key);
      return null;
    }

    return cached;
  }

  cleanCache() {
    const now = Date.now();
    for (const [key, value] of this.fallbackCache.entries()) {
      if (now - value.timestamp.getTime() > this.maxCacheAge) {
        this.fallbackCache.delete(key);
      }
    }
  }

  /**
   * Utility methods
   */
  async executeWithTimeout(func, params, timeout = 30000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('AI function execution timeout'));
      }, timeout);

      func(params)
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  identifyMainConcerns(performanceData) {
    const concerns = [];

    if (performanceData.winRate < 40) concerns.push('Low win rate');
    if (performanceData.profitFactor < 1.2) concerns.push('Poor profit factor');
    if (performanceData.maxDrawdown > 15) concerns.push('High drawdown');
    if (performanceData.consecutiveLosses > 5) concerns.push('Extended losing streaks');

    return concerns;
  }

  identifyUrgentActions(performanceData, riskLevel) {
    const actions = [];

    if (riskLevel === 'critical') {
      actions.push('Pause trading immediately');
      actions.push('Review risk management parameters');
    } else if (riskLevel === 'high') {
      actions.push('Reduce position sizes');
      actions.push('Tighten entry criteria');
    }

    if (performanceData.consecutiveLosses > 5) {
      actions.push('Implement trading pause mechanism');
    }

    return actions;
  }

  calculateRiskScore(performanceData) {
    let score = 50; // Base score

    // Adjust based on key metrics
    if (performanceData.winRate < 40) score += 20;
    if (performanceData.profitFactor < 1.0) score += 25;
    if (performanceData.maxDrawdown > 15) score += 20;
    if (performanceData.consecutiveLosses > 5) score += 15;

    return Math.min(100, score);
  }

  /**
   * Get service health status
   * @returns {Object} Current health status
   */
  getHealthStatus() {
    return {
      ...this.serviceHealthStatus,
      cacheSize: this.fallbackCache.size,
      fallbackEnabled: this.fallbackEnabled
    };
  }

  /**
   * Reset service health (for testing or manual recovery)
   */
  resetHealth() {
    this.serviceHealthStatus = {
      isHealthy: true,
      lastCheck: new Date(),
      consecutiveFailures: 0,
      lastSuccessfulCall: new Date()
    };
    logger.logInfo('AI service health status reset');
  }
}

module.exports = AIFallbackManager;