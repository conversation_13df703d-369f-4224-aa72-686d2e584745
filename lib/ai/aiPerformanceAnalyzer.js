const AIClient = require('./aiClient');
const PerformanceAnalytics = require('../models/performanceAnalytics');
const TradingSignal = require('../models/tradingSignal');
const loggerFactory = require('../logger');
const config = require('config');
const logger = loggerFactory(config.dirLog || 'logs');
const { v4: uuidv4 } = require('uuid');

/**
 * AI Performance Analyzer
 * Uses AI/LLM to analyze trading performance and provide optimization recommendations
 */
class AIPerformanceAnalyzer {
  constructor(options = {}) {
    this.aiClient = new AIClient(options.aiClient);
    this.analysisInterval = options.analysisInterval || '24h';
    this.confidenceThreshold = options.confidenceThreshold || 70;
    this.autoImplement = options.autoImplement || false;
    this.maxAnalysisAge = options.maxAnalysisAge || 7 * 24 * 60 * 60 * 1000; // 7 days
  }

  /**
   * Perform comprehensive performance analysis with AI
   * @param {string} timeframe - Analysis timeframe (7d, 30d, 90d)
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} Analysis results
   */
  async analyzePerformance(timeframe = '30d', options = {}) {
    try {
      logger.logInfo('Starting AI performance analysis', { timeframe, options });

      // 1. Gather performance data
      const performanceData = await this.gatherPerformanceData(timeframe);

      if (performanceData.totalTrades < 10) {
        logger.warn('Insufficient trade data for analysis', {
          totalTrades: performanceData.totalTrades,
          timeframe
        });
        return {
          success: false,
          error: 'Insufficient trade data for meaningful analysis',
          minTradesRequired: 10,
          currentTrades: performanceData.totalTrades
        };
      }

      // 2. Perform AI analysis
      const aiAnalysis = await this.aiClient.analyzePerformance(performanceData);

      if (!aiAnalysis.confidenceScore || aiAnalysis.confidenceScore < 50) {
        logger.warn('AI analysis confidence too low', {
          confidence: aiAnalysis.confidenceScore
        });
      }

      // 3. Create analysis record
      const analysisRecord = await this.createAnalysisRecord(
        timeframe,
        performanceData,
        aiAnalysis,
        options.analysisType || 'performance_review'
      );

      // 4. Auto-implement high-confidence recommendations if enabled
      if (this.autoImplement && aiAnalysis.confidenceScore >= this.confidenceThreshold) {
        await this.implementRecommendations(analysisRecord);
      }

      logger.logInfo('AI performance analysis completed', {
        analysisId: analysisRecord.analysisId,
        confidence: aiAnalysis.confidenceScore,
        recommendationsCount: aiAnalysis.recommendations?.length || 0
      });

      return {
        success: true,
        analysisId: analysisRecord.analysisId,
        analysis: analysisRecord,
        autoImplemented: this.autoImplement && aiAnalysis.confidenceScore >= this.confidenceThreshold
      };

    } catch (error) {
      logger.logError('AI performance analysis failed', {
        error: error.message,
        timeframe,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        timeframe
      };
    }
  }

  /**
   * Gather comprehensive performance data for analysis
   * @param {string} timeframe - Time period for data gathering
   * @returns {Promise<Object>} Performance data
   */
  async gatherPerformanceData(timeframe) {
    const days = this.parseTimeframeToDays(timeframe);
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Get all trades in the timeframe
    const trades = await TradingSignal.find({
      createdAt: { $gte: startDate },
      status: { $in: ['completed', 'stopped'] }
    }).sort({ createdAt: -1 });

    // Calculate basic statistics
    const totalTrades = trades.length;
    const winningTrades = trades.filter(t => t.pnlPercent > 0);
    const losingTrades = trades.filter(t => t.pnlPercent <= 0);

    const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
    const totalPnL = trades.reduce((sum, t) => sum + (t.pnlPercent || 0), 0);

    const avgWin = winningTrades.length > 0 ?
      winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ?
      Math.abs(losingTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / losingTrades.length) : 0;

    const profitFactor = avgLoss > 0 ? Math.abs(avgWin / avgLoss) : 0;

    // Calculate Sharpe ratio (simplified)
    const returns = trades.map(t => t.pnlPercent || 0);
    const avgReturn = returns.length > 0 ? returns.reduce((a, b) => a + b, 0) / returns.length : 0;
    const stdDev = this.calculateStandardDeviation(returns);
    const sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;

    // Calculate max drawdown
    const maxDrawdown = this.calculateMaxDrawdown(trades);

    // Get symbol-specific performance
    const symbolStats = await this.getSymbolPerformance(trades);

    // Get timeframe-specific performance
    const timeframeStats = await this.getTimeframePerformance(trades);

    // Get recent losing trades for analysis
    const recentLosses = losingTrades
      .slice(0, 10) // Last 10 losses
      .map(trade => ({
        signalId: trade._id.toString(),
        symbol: trade.symbol,
        timeframe: trade.timeframe,
        entry: trade.entry,
        exitPrice: trade.exitPrice,
        pnlPercent: trade.pnlPercent,
        exitReason: trade.exitReason || 'unknown',
        tradeDate: trade.createdAt,
        duration: trade.exitTime ?
          Math.round((new Date(trade.exitTime) - new Date(trade.createdAt)) / (1000 * 60)) : null,
        marketCondition: trade.marketCondition
      }));

    // Calculate consecutive losses/wins
    const { consecutiveLosses, consecutiveWins } = this.calculateConsecutiveResults(trades);

    return {
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: Math.round(winRate * 100) / 100,
      profitFactor: Math.round(profitFactor * 100) / 100,
      sharpeRatio: Math.round(sharpeRatio * 100) / 100,
      maxDrawdown: Math.round(maxDrawdown * 100) / 100,
      avgWin: Math.round(avgWin * 100) / 100,
      avgLoss: Math.round(avgLoss * 100) / 100,
      totalPnL: Math.round(totalPnL * 100) / 100,
      expectancy: Math.round(((winRate / 100) * avgWin - ((100 - winRate) / 100) * avgLoss) * 100) / 100,
      largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnlPercent)) : 0,
      largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnlPercent)) : 0,
      consecutiveLosses,
      consecutiveWins,
      symbolStats,
      timeframeStats,
      recentLosses,
      analysisDate: new Date(),
      timeframe
    };
  }

  /**
   * Get symbol-specific performance statistics
   * @param {Array} trades - Array of trade records
   * @returns {Array} Symbol performance data
   */
  async getSymbolPerformance(trades) {
    const symbolGroups = {};

    trades.forEach(trade => {
      if (!symbolGroups[trade.symbol]) {
        symbolGroups[trade.symbol] = [];
      }
      symbolGroups[trade.symbol].push(trade);
    });

    return Object.entries(symbolGroups).map(([symbol, symbolTrades]) => {
      const wins = symbolTrades.filter(t => t.pnlPercent > 0).length;
      const totalPnL = symbolTrades.reduce((sum, t) => sum + (t.pnlPercent || 0), 0);
      const avgPnL = symbolTrades.length > 0 ? totalPnL / symbolTrades.length : 0;

      const winRate = symbolTrades.length > 0 ? (wins / symbolTrades.length) * 100 : 0;
      const avgWin = wins > 0 ?
        symbolTrades.filter(t => t.pnlPercent > 0).reduce((sum, t) => sum + t.pnlPercent, 0) / wins : 0;
      const losses = symbolTrades.filter(t => t.pnlPercent <= 0);
      const avgLoss = losses.length > 0 ?
        Math.abs(losses.reduce((sum, t) => sum + t.pnlPercent, 0) / losses.length) : 0;
      const profitFactor = avgLoss > 0 ? Math.abs(avgWin / avgLoss) : 0;

      return {
        symbol,
        trades: symbolTrades.length,
        winRate: Math.round(winRate * 100) / 100,
        profitFactor: Math.round(profitFactor * 100) / 100,
        totalPnL: Math.round(totalPnL * 100) / 100,
        avgPnL: Math.round(avgPnL * 100) / 100,
        maxDrawdown: this.calculateMaxDrawdown(symbolTrades),
        lastTradeDate: symbolTrades.length > 0 ?
          new Date(Math.max(...symbolTrades.map(t => new Date(t.createdAt)))) : null
      };
    }).sort((a, b) => b.profitFactor - a.profitFactor);
  }

  /**
   * Get timeframe-specific performance statistics
   * @param {Array} trades - Array of trade records
   * @returns {Array} Timeframe performance data
   */
  async getTimeframePerformance(trades) {
    const timeframeGroups = {};

    trades.forEach(trade => {
      if (!timeframeGroups[trade.timeframe]) {
        timeframeGroups[trade.timeframe] = [];
      }
      timeframeGroups[trade.timeframe].push(trade);
    });

    return Object.entries(timeframeGroups).map(([timeframe, timeframeTrades]) => {
      const wins = timeframeTrades.filter(t => t.pnlPercent > 0).length;
      const totalPnL = timeframeTrades.reduce((sum, t) => sum + (t.pnlPercent || 0), 0);
      const avgPnL = timeframeTrades.length > 0 ? totalPnL / timeframeTrades.length : 0;

      const winRate = timeframeTrades.length > 0 ? (wins / timeframeTrades.length) * 100 : 0;
      const avgWin = wins > 0 ?
        timeframeTrades.filter(t => t.pnlPercent > 0).reduce((sum, t) => sum + t.pnlPercent, 0) / wins : 0;
      const losses = timeframeTrades.filter(t => t.pnlPercent <= 0);
      const avgLoss = losses.length > 0 ?
        Math.abs(losses.reduce((sum, t) => sum + t.pnlPercent, 0) / losses.length) : 0;
      const profitFactor = avgLoss > 0 ? Math.abs(avgWin / avgLoss) : 0;

      return {
        timeframe,
        trades: timeframeTrades.length,
        winRate: Math.round(winRate * 100) / 100,
        profitFactor: Math.round(profitFactor * 100) / 100,
        totalPnL: Math.round(totalPnL * 100) / 100,
        avgPnL: Math.round(avgPnL * 100) / 100
      };
    }).sort((a, b) => b.profitFactor - a.profitFactor);
  }

  /**
   * Create analysis record in database
   * @param {string} timeframe - Analysis timeframe
   * @param {Object} performanceData - Performance data
   * @param {Object} aiAnalysis - AI analysis results
   * @param {string} analysisType - Type of analysis
   * @returns {Promise<Object>} Created analysis record
   */
  async createAnalysisRecord(timeframe, performanceData, aiAnalysis, analysisType) {
    const analysisId = uuidv4();

    const analysisRecord = new PerformanceAnalytics({
      analysisId,
      timeframe,
      analysisType,

      tradeStatistics: {
        totalTrades: performanceData.totalTrades,
        winningTrades: performanceData.winningTrades,
        losingTrades: performanceData.losingTrades,
        winRate: performanceData.winRate,
        profitFactor: performanceData.profitFactor,
        sharpeRatio: performanceData.sharpeRatio,
        maxDrawdown: performanceData.maxDrawdown,
        avgWin: performanceData.avgWin,
        avgLoss: performanceData.avgLoss,
        expectancy: performanceData.expectancy,
        totalPnL: performanceData.totalPnL,
        largestWin: performanceData.largestWin,
        largestLoss: performanceData.largestLoss,
        consecutiveWins: performanceData.consecutiveWins,
        consecutiveLosses: performanceData.consecutiveLosses
      },

      symbolPerformance: performanceData.symbolStats,
      timeframePerformance: performanceData.timeframeStats,
      recentLosses: performanceData.recentLosses,

      aiAnalysis: {
        insights: aiAnalysis.insights || [],
        recommendations: aiAnalysis.recommendations || [],
        parameterSuggestions: aiAnalysis.parameterSuggestions || {},
        riskAssessment: aiAnalysis.riskAssessment || {
          currentRiskLevel: 'medium',
          mainConcerns: [],
          urgentActions: []
        },
        confidenceScore: aiAnalysis.confidenceScore || 0,
        aiMetadata: aiAnalysis.metadata || {}
      },

      implementationResults: {
        totalRecommendations: aiAnalysis.recommendations?.length || 0
      }
    });

    await analysisRecord.save();
    return analysisRecord;
  }

  /**
   * Implement AI recommendations automatically
   * @param {Object} analysisRecord - Analysis record with recommendations
   * @returns {Promise<Object>} Implementation results
   */
  async implementRecommendations(analysisRecord) {
    const recommendations = analysisRecord.aiAnalysis.recommendations;
    const implementationResults = {
      implemented: 0,
      failed: 0,
      skipped: 0,
      results: []
    };

    for (let i = 0; i < recommendations.length; i++) {
      const recommendation = recommendations[i];

      try {
        // Only implement high-priority, low-risk recommendations automatically
        if (recommendation.priority >= 7 && recommendation.riskLevel === 'low') {
          const result = await this.implementSingleRecommendation(recommendation);

          if (result.success) {
            await analysisRecord.markRecommendationImplemented(i, result);
            implementationResults.implemented++;
          } else {
            implementationResults.failed++;
          }

          implementationResults.results.push(result);
        } else {
          implementationResults.skipped++;
          logger.logInfo('Skipped recommendation due to risk/priority', {
            type: recommendation.type,
            priority: recommendation.priority,
            riskLevel: recommendation.riskLevel
          });
        }
      } catch (error) {
        logger.logError('Failed to implement recommendation', {
          error: error.message,
          recommendation: recommendation.type
        });
        implementationResults.failed++;
      }
    }

    logger.logInfo('Recommendation implementation completed', implementationResults);
    return implementationResults;
  }

  /**
   * Implement a single recommendation
   * @param {Object} recommendation - Recommendation to implement
   * @returns {Promise<Object>} Implementation result
   */
  async implementSingleRecommendation(recommendation) {
    // This is a placeholder - actual implementation would depend on the recommendation type
    // For now, we'll just log and return success for demonstration

    logger.logInfo('Implementing recommendation', {
      type: recommendation.type,
      description: recommendation.description
    });

    // TODO: Implement actual parameter changes based on recommendation type
    // This would involve updating configuration files, database settings, etc.

    return {
      success: true,
      implementationType: recommendation.type,
      implementationDate: new Date(),
      beforeMetrics: {}, // Would capture current metrics
      afterMetrics: {},  // Would capture metrics after change
      improvement: 0     // Would calculate actual improvement
    };
  }

  /**
   * Get latest analysis for a timeframe
   * @param {string} timeframe - Timeframe to get analysis for
   * @returns {Promise<Object>} Latest analysis or null
   */
  async getLatestAnalysis(timeframe = '30d') {
    return await PerformanceAnalytics.getLatestAnalysis(timeframe);
  }

  /**
   * Get pending recommendations that need manual review
   * @returns {Promise<Array>} Pending recommendations
   */
  async getPendingRecommendations() {
    return await PerformanceAnalytics.getPendingRecommendations();
  }

  /**
   * Utility methods
   */
  parseTimeframeToDays(timeframe) {
    const timeframeMap = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };
    return timeframeMap[timeframe] || 30;
  }

  calculateStandardDeviation(values) {
    if (values.length === 0) return 0;

    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;

    return Math.sqrt(avgSquaredDiff);
  }

  calculateMaxDrawdown(trades) {
    if (trades.length === 0) return 0;

    let peak = 0;
    let maxDrawdown = 0;
    let runningPnL = 0;

    // Sort trades by date
    const sortedTrades = trades.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

    for (const trade of sortedTrades) {
      runningPnL += trade.pnlPercent || 0;

      if (runningPnL > peak) {
        peak = runningPnL;
      }

      const drawdown = peak - runningPnL;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return Math.round(maxDrawdown * 100) / 100;
  }

  calculateConsecutiveResults(trades) {
    if (trades.length === 0) return { consecutiveLosses: 0, consecutiveWins: 0 };

    // Sort trades by date (most recent first)
    const sortedTrades = trades.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    let consecutiveLosses = 0;
    let consecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let maxConsecutiveWins = 0;

    for (const trade of sortedTrades) {
      if (trade.pnlPercent > 0) {
        consecutiveWins++;
        consecutiveLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, consecutiveWins);
      } else {
        consecutiveLosses++;
        consecutiveWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, consecutiveLosses);
      }
    }

    return {
      consecutiveLosses: maxConsecutiveLosses,
      consecutiveWins: maxConsecutiveWins
    };
  }
}

module.exports = AIPerformanceAnalyzer;