const logger = require('../logger');
const BacktestEngine = require('./backtestEngine');

/**
 * Parameter Optimizer
 * Optimizes trading parameters using genetic algorithms and grid search
 */
class ParameterOptimizer {
  constructor(config = {}) {
    this.config = {
      // Optimization methods
      methods: {
        gridSearch: config.methods?.gridSearch || true,
        geneticAlgorithm: config.methods?.geneticAlgorithm || true,
        bayesianOptimization: config.methods?.bayesianOptimization || false
      },

      // Grid search settings
      gridSearch: {
        maxCombinations: config.gridSearch?.maxCombinations || 1000,
        stepSize: config.gridSearch?.stepSize || 0.1,
        maxIterations: config.gridSearch?.maxIterations || 100
      },

      // Genetic algorithm settings
      geneticAlgorithm: {
        populationSize: config.geneticAlgorithm?.populationSize || 50,
        generations: config.geneticAlgorithm?.generations || 20,
        mutationRate: config.geneticAlgorithm?.mutationRate || 0.1,
        crossoverRate: config.geneticAlgorithm?.crossoverRate || 0.8,
        elitismRate: config.geneticAlgorithm?.elitismRate || 0.2
      },

      // Parameter ranges
      parameterRanges: {
        stopLossPercent: { min: 0.2, max: 2.0, step: 0.1 },
        takeProfitMultiplier: { min: 1.0, max: 4.0, step: 0.2 },
        maxRiskPerTrade: { min: 0.5, max: 3.0, step: 0.1 },
        strengthThreshold: { min: 0.3, max: 0.9, step: 0.05 },
        volumeMultiplier: { min: 1.0, max: 3.0, step: 0.1 },
        trailingStopPercent: { min: 0.1, max: 1.0, step: 0.05 },
        breakEvenThreshold: { min: 0.5, max: 2.0, step: 0.1 },
        ...config.parameterRanges
      },

      // Fitness function weights
      fitnessWeights: {
        winRate: config.fitnessWeights?.winRate || 0.25,
        profitFactor: config.fitnessWeights?.profitFactor || 0.25,
        sharpeRatio: config.fitnessWeights?.sharpeRatio || 0.20,
        maxDrawdown: config.fitnessWeights?.maxDrawdown || 0.15,
        expectancy: config.fitnessWeights?.expectancy || 0.15
      },

      // Constraints
      constraints: {
        minWinRate: config.constraints?.minWinRate || 40,
        maxDrawdown: config.constraints?.maxDrawdown || 20,
        minProfitFactor: config.constraints?.minProfitFactor || 1.1,
        minTrades: config.constraints?.minTrades || 50
      },

      ...config
    };

    this.backtestEngine = new BacktestEngine();
    this.optimizationHistory = [];
  }

  /**
   * Optimize parameters using specified method
   */
  async optimize(options = {}) {
    try {
      const {
        currentParameters,
        performanceData,
        aiSuggestions = {},
        backtestPeriod = 90,
        method = 'auto'
      } = options;

      logger.info('Starting parameter optimization', {
        method,
        backtestPeriod,
        currentParams: Object.keys(currentParameters).length
      });

      let optimizationMethod = method;
      if (method === 'auto') {
        optimizationMethod = this.selectOptimalMethod(performanceData);
      }

      let results;
      switch (optimizationMethod) {
        case 'grid_search':
          results = await this.gridSearchOptimization(currentParameters, performanceData, aiSuggestions);
          break;
        case 'genetic_algorithm':
          results = await this.geneticAlgorithmOptimization(currentParameters, performanceData, aiSuggestions);
          break;
        case 'ai_guided':
          results = await this.aiGuidedOptimization(currentParameters, performanceData, aiSuggestions);
          break;
        default:
          results = await this.hybridOptimization(currentParameters, performanceData, aiSuggestions);
      }

      // Validate results
      const validatedResults = await this.validateOptimizationResults(results, currentParameters);

      // Record optimization
      this.recordOptimization({
        method: optimizationMethod,
        currentParameters,
        results: validatedResults,
        performanceData,
        timestamp: new Date()
      });

      return validatedResults;

    } catch (error) {
      logger.error('Error in parameter optimization', { error: error.message });
      throw error;
    }
  }

  /**
   * Grid search optimization
   */
  async gridSearchOptimization(currentParameters, performanceData, aiSuggestions) {
    try {
      logger.info('Running grid search optimization');

      const parameterCombinations = this.generateParameterCombinations(currentParameters, aiSuggestions);
      const results = [];

      // Limit combinations to prevent excessive computation
      const limitedCombinations = parameterCombinations.slice(0, this.config.gridSearch.maxCombinations);

      for (let i = 0; i < limitedCombinations.length; i++) {
        const params = limitedCombinations[i];
        
        try {
          const backtestResult = await this.backtestEngine.runBacktest(params, performanceData);
          const fitness = this.calculateFitness(backtestResult);

          if (this.meetsConstraints(backtestResult)) {
            results.push({
              parameters: params,
              performance: backtestResult,
              fitness: fitness,
              method: 'grid_search'
            });
          }

          // Progress logging
          if ((i + 1) % 50 === 0) {
            logger.debug(`Grid search progress: ${i + 1}/${limitedCombinations.length}`);
          }

        } catch (error) {
          logger.warn('Backtest failed for parameter combination', { 
            error: error.message, 
            params: JSON.stringify(params) 
          });
        }
      }

      // Sort by fitness and return best result
      results.sort((a, b) => b.fitness - a.fitness);

      if (results.length === 0) {
        throw new Error('No valid parameter combinations found in grid search');
      }

      const bestResult = results[0];
      const improvement = this.calculateImprovement(performanceData.metrics, bestResult.performance);

      return {
        bestParameters: bestResult.parameters,
        improvement: improvement,
        backtestResults: bestResult.performance,
        confidence: this.calculateConfidence(results, bestResult),
        method: 'grid_search',
        totalCombinations: limitedCombinations.length,
        validCombinations: results.length
      };

    } catch (error) {
      logger.error('Error in grid search optimization', { error: error.message });
      throw error;
    }
  }

  /**
   * Genetic algorithm optimization
   */
  async geneticAlgorithmOptimization(currentParameters, performanceData, aiSuggestions) {
    try {
      logger.info('Running genetic algorithm optimization');

      const config = this.config.geneticAlgorithm;
      let population = this.initializePopulation(currentParameters, aiSuggestions, config.populationSize);
      let bestIndividual = null;
      let bestFitness = -Infinity;

      for (let generation = 0; generation < config.generations; generation++) {
        // Evaluate population
        const evaluatedPopulation = await this.evaluatePopulation(population, performanceData);
        
        // Track best individual
        const generationBest = evaluatedPopulation.reduce((best, individual) => 
          individual.fitness > best.fitness ? individual : best
        );

        if (generationBest.fitness > bestFitness) {
          bestFitness = generationBest.fitness;
          bestIndividual = { ...generationBest };
        }

        // Selection, crossover, and mutation
        const selectedParents = this.selection(evaluatedPopulation);
        const offspring = this.crossover(selectedParents, config.crossoverRate);
        const mutatedOffspring = this.mutation(offspring, config.mutationRate);

        // Create new population with elitism
        const eliteCount = Math.floor(config.populationSize * config.elitismRate);
        const elite = evaluatedPopulation
          .sort((a, b) => b.fitness - a.fitness)
          .slice(0, eliteCount)
          .map(ind => ind.parameters);

        population = [...elite, ...mutatedOffspring.slice(0, config.populationSize - eliteCount)];

        logger.debug(`Generation ${generation + 1}: Best fitness = ${generationBest.fitness.toFixed(4)}`);
      }

      if (!bestIndividual) {
        throw new Error('No valid individuals found in genetic algorithm');
      }

      const improvement = this.calculateImprovement(performanceData.metrics, bestIndividual.performance);

      return {
        bestParameters: bestIndividual.parameters,
        improvement: improvement,
        backtestResults: bestIndividual.performance,
        confidence: this.calculateGeneticConfidence(bestFitness, config.generations),
        method: 'genetic_algorithm',
        generations: config.generations,
        finalFitness: bestFitness
      };

    } catch (error) {
      logger.error('Error in genetic algorithm optimization', { error: error.message });
      throw error;
    }
  }

  /**
   * AI-guided optimization
   */
  async aiGuidedOptimization(currentParameters, performanceData, aiSuggestions) {
    try {
      logger.info('Running AI-guided optimization');

      // Start with AI suggestions as base
      const aiBasedParameters = this.applyAISuggestions(currentParameters, aiSuggestions);
      
      // Create variations around AI suggestions
      const variations = this.generateAIGuidedVariations(aiBasedParameters, 20);
      
      const results = [];

      for (const params of variations) {
        try {
          const backtestResult = await this.backtestEngine.runBacktest(params, performanceData);
          const fitness = this.calculateFitness(backtestResult);

          if (this.meetsConstraints(backtestResult)) {
            results.push({
              parameters: params,
              performance: backtestResult,
              fitness: fitness,
              method: 'ai_guided'
            });
          }
        } catch (error) {
          logger.warn('AI-guided backtest failed', { error: error.message });
        }
      }

      if (results.length === 0) {
        throw new Error('No valid AI-guided parameter combinations found');
      }

      results.sort((a, b) => b.fitness - a.fitness);
      const bestResult = results[0];
      const improvement = this.calculateImprovement(performanceData.metrics, bestResult.performance);

      return {
        bestParameters: bestResult.parameters,
        improvement: improvement,
        backtestResults: bestResult.performance,
        confidence: this.calculateConfidence(results, bestResult),
        method: 'ai_guided',
        aiSuggestionsUsed: Object.keys(aiSuggestions).length,
        validCombinations: results.length
      };

    } catch (error) {
      logger.error('Error in AI-guided optimization', { error: error.message });
      throw error;
    }
  }

  /**
   * Hybrid optimization combining multiple methods
   */
  async hybridOptimization(currentParameters, performanceData, aiSuggestions) {
    try {
      logger.info('Running hybrid optimization');

      const results = [];

      // Run AI-guided optimization first (fast)
      if (Object.keys(aiSuggestions).length > 0) {
        try {
          const aiResult = await this.aiGuidedOptimization(currentParameters, performanceData, aiSuggestions);
          results.push(aiResult);
        } catch (error) {
          logger.warn('AI-guided optimization failed in hybrid mode', { error: error.message });
        }
      }

      // Run limited grid search
      try {
        const gridResult = await this.gridSearchOptimization(currentParameters, performanceData, aiSuggestions);
        results.push(gridResult);
      } catch (error) {
        logger.warn('Grid search failed in hybrid mode', { error: error.message });
      }

      // Run genetic algorithm with smaller population
      try {
        const originalPopSize = this.config.geneticAlgorithm.populationSize;
        const originalGenerations = this.config.geneticAlgorithm.generations;
        
        this.config.geneticAlgorithm.populationSize = 20;
        this.config.geneticAlgorithm.generations = 10;
        
        const geneticResult = await this.geneticAlgorithmOptimization(currentParameters, performanceData, aiSuggestions);
        results.push(geneticResult);
        
        // Restore original config
        this.config.geneticAlgorithm.populationSize = originalPopSize;
        this.config.geneticAlgorithm.generations = originalGenerations;
      } catch (error) {
        logger.warn('Genetic algorithm failed in hybrid mode', { error: error.message });
      }

      if (results.length === 0) {
        throw new Error('All optimization methods failed in hybrid mode');
      }

      // Select best result
      const bestResult = results.reduce((best, current) => 
        current.improvement > best.improvement ? current : best
      );

      return {
        ...bestResult,
        method: 'hybrid',
        methodsUsed: results.map(r => r.method),
        totalMethods: results.length
      };

    } catch (error) {
      logger.error('Error in hybrid optimization', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate parameter combinations for grid search
   */
  generateParameterCombinations(currentParameters, aiSuggestions) {
    const combinations = [];
    const paramNames = Object.keys(this.config.parameterRanges);
    
    // Generate base combinations
    const baseCombinations = this.generateGridCombinations(paramNames, 0, {}, currentParameters);
    combinations.push(...baseCombinations);

    // Add AI-suggested variations
    if (Object.keys(aiSuggestions).length > 0) {
      const aiVariations = this.generateAIGuidedVariations(
        this.applyAISuggestions(currentParameters, aiSuggestions), 
        50
      );
      combinations.push(...aiVariations);
    }

    return combinations;
  }

  /**
   * Generate grid combinations recursively
   */
  generateGridCombinations(paramNames, index, current, baseParams) {
    if (index >= paramNames.length) {
      return [{ ...baseParams, ...current }];
    }

    const combinations = [];
    const paramName = paramNames[index];
    const range = this.config.parameterRanges[paramName];
    
    if (!range) {
      return this.generateGridCombinations(paramNames, index + 1, current, baseParams);
    }

    // Generate values for this parameter
    const values = [];
    for (let value = range.min; value <= range.max; value += range.step) {
      values.push(Math.round(value * 1000) / 1000); // Round to 3 decimal places
    }

    // Limit values to prevent explosion
    const limitedValues = values.slice(0, 10);

    for (const value of limitedValues) {
      const newCurrent = { ...current };
      this.setNestedParameter(newCurrent, paramName, value);
      
      const subCombinations = this.generateGridCombinations(paramNames, index + 1, newCurrent, baseParams);
      combinations.push(...subCombinations);
      
      // Limit total combinations
      if (combinations.length >= this.config.gridSearch.maxCombinations) {
        break;
      }
    }

    return combinations;
  }

  /**
   * Set nested parameter value
   */
  setNestedParameter(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * Apply AI suggestions to current parameters
   */
  applyAISuggestions(currentParameters, aiSuggestions) {
    const newParameters = JSON.parse(JSON.stringify(currentParameters));
    
    for (const [path, suggestion] of Object.entries(aiSuggestions)) {
      try {
        const currentValue = this.getNestedParameter(currentParameters, path);
        if (currentValue !== undefined) {
          let newValue;
          
          if (typeof suggestion === 'number') {
            // Absolute value
            newValue = suggestion;
          } else if (typeof suggestion === 'string' && suggestion.includes('%')) {
            // Percentage change
            const percent = parseFloat(suggestion.replace('%', '')) / 100;
            newValue = currentValue * (1 + percent);
          } else if (typeof suggestion === 'object' && suggestion.adjustment) {
            // Relative adjustment
            newValue = currentValue + suggestion.adjustment;
          } else {
            newValue = suggestion;
          }
          
          this.setNestedParameter(newParameters, path, newValue);
        }
      } catch (error) {
        logger.warn('Failed to apply AI suggestion', { path, suggestion, error: error.message });
      }
    }
    
    return newParameters;
  }

  /**
   * Get nested parameter value
   */
  getNestedParameter(obj, path) {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current[key] === undefined) {
        return undefined;
      }
      current = current[key];
    }
    
    return current;
  }

  /**
   * Generate AI-guided variations
   */
  generateAIGuidedVariations(baseParameters, count) {
    const variations = [baseParameters];
    
    for (let i = 1; i < count; i++) {
      const variation = JSON.parse(JSON.stringify(baseParameters));
      
      // Apply small random variations
      for (const paramPath of Object.keys(this.config.parameterRanges)) {
        const currentValue = this.getNestedParameter(variation, paramPath);
        if (currentValue !== undefined) {
          const range = this.config.parameterRanges[paramPath];
          const variationPercent = (Math.random() - 0.5) * 0.2; // ±10% variation
          let newValue = currentValue * (1 + variationPercent);
          
          // Clamp to valid range
          newValue = Math.max(range.min, Math.min(range.max, newValue));
          newValue = Math.round(newValue / range.step) * range.step;
          
          this.setNestedParameter(variation, paramPath, newValue);
        }
      }
      
      variations.push(variation);
    }
    
    return variations;
  }

  /**
   * Calculate fitness score for optimization results
   */
  calculateFitness(backtestResult) {
    const weights = this.config.fitnessWeights;
    const metrics = backtestResult;
    
    let fitness = 0;
    
    // Win rate component (0-100 scale)
    fitness += (metrics.winRate / 100) * weights.winRate * 100;
    
    // Profit factor component (normalize to 0-100 scale)
    fitness += Math.min(metrics.profitFactor / 3, 1) * weights.profitFactor * 100;
    
    // Sharpe ratio component (normalize to 0-100 scale)
    fitness += Math.min(Math.max(metrics.sharpeRatio / 2, 0), 1) * weights.sharpeRatio * 100;
    
    // Max drawdown component (inverted - lower is better)
    fitness += (1 - Math.min(metrics.maxDrawdown / 30, 1)) * weights.maxDrawdown * 100;
    
    // Expectancy component
    fitness += Math.min(Math.max(metrics.expectancy / 2, 0), 1) * weights.expectancy * 100;
    
    return Math.round(fitness * 100) / 100;
  }

  /**
   * Check if results meet constraints
   */
  meetsConstraints(backtestResult) {
    const constraints = this.config.constraints;
    
    return (
      backtestResult.winRate >= constraints.minWinRate &&
      backtestResult.maxDrawdown <= constraints.maxDrawdown &&
      backtestResult.profitFactor >= constraints.minProfitFactor &&
      backtestResult.totalTrades >= constraints.minTrades
    );
  }

  /**
   * Calculate improvement over current performance
   */
  calculateImprovement(currentMetrics, optimizedMetrics) {
    const currentScore = this.calculateFitness(currentMetrics);
    const optimizedScore = this.calculateFitness(optimizedMetrics);
    
    return (optimizedScore - currentScore) / currentScore;
  }

  /**
   * Calculate confidence in optimization results
   */
  calculateConfidence(results, bestResult) {
    if (results.length < 2) return 0.5;
    
    const fitnessValues = results.map(r => r.fitness);
    const mean = fitnessValues.reduce((sum, f) => sum + f, 0) / fitnessValues.length;
    const stdDev = Math.sqrt(
      fitnessValues.reduce((sum, f) => sum + Math.pow(f - mean, 2), 0) / fitnessValues.length
    );
    
    // Confidence based on how much better the best result is compared to the distribution
    const zScore = stdDev > 0 ? (bestResult.fitness - mean) / stdDev : 0;
    const confidence = Math.min(Math.max(zScore / 3, 0), 1); // Normalize to 0-1
    
    return Math.round(confidence * 100) / 100;
  }

  /**
   * Calculate confidence for genetic algorithm
   */
  calculateGeneticConfidence(bestFitness, generations) {
    // Confidence based on fitness value and convergence
    const fitnessConfidence = Math.min(bestFitness / 100, 1);
    const convergenceConfidence = Math.min(generations / 20, 1);
    
    return Math.round((fitnessConfidence * 0.7 + convergenceConfidence * 0.3) * 100) / 100;
  }

  /**
   * Select optimal optimization method based on data
   */
  selectOptimalMethod(performanceData) {
    const tradeCount = performanceData.totalTrades;
    
    if (tradeCount < 50) {
      return 'ai_guided'; // Fast method for limited data
    } else if (tradeCount < 200) {
      return 'grid_search'; // Thorough search for moderate data
    } else {
      return 'genetic_algorithm'; // Efficient for large datasets
    }
  }

  /**
   * Initialize population for genetic algorithm
   */
  initializePopulation(baseParameters, aiSuggestions, populationSize) {
    const population = [];
    
    // Add base parameters
    population.push(baseParameters);
    
    // Add AI-suggested parameters if available
    if (Object.keys(aiSuggestions).length > 0) {
      population.push(this.applyAISuggestions(baseParameters, aiSuggestions));
    }
    
    // Fill rest with random variations
    while (population.length < populationSize) {
      const individual = this.generateRandomIndividual(baseParameters);
      population.push(individual);
    }
    
    return population;
  }

  /**
   * Generate random individual for genetic algorithm
   */
  generateRandomIndividual(baseParameters) {
    const individual = JSON.parse(JSON.stringify(baseParameters));
    
    for (const paramPath of Object.keys(this.config.parameterRanges)) {
      const range = this.config.parameterRanges[paramPath];
      const randomValue = range.min + Math.random() * (range.max - range.min);
      const steppedValue = Math.round(randomValue / range.step) * range.step;
      
      this.setNestedParameter(individual, paramPath, steppedValue);
    }
    
    return individual;
  }

  /**
   * Evaluate population fitness
   */
  async evaluatePopulation(population, performanceData) {
    const evaluatedPopulation = [];
    
    for (const individual of population) {
      try {
        const backtestResult = await this.backtestEngine.runBacktest(individual, performanceData);
        const fitness = this.meetsConstraints(backtestResult) ? this.calculateFitness(backtestResult) : 0;
        
        evaluatedPopulation.push({
          parameters: individual,
          performance: backtestResult,
          fitness: fitness
        });
      } catch (error) {
        // Add individual with zero fitness if backtest fails
        evaluatedPopulation.push({
          parameters: individual,
          performance: null,
          fitness: 0
        });
      }
    }
    
    return evaluatedPopulation;
  }

  /**
   * Selection for genetic algorithm
   */
  selection(population) {
    // Tournament selection
    const tournamentSize = 3;
    const selected = [];
    
    for (let i = 0; i < population.length; i++) {
      const tournament = [];
      for (let j = 0; j < tournamentSize; j++) {
        const randomIndex = Math.floor(Math.random() * population.length);
        tournament.push(population[randomIndex]);
      }
      
      const winner = tournament.reduce((best, current) => 
        current.fitness > best.fitness ? current : best
      );
      
      selected.push(winner.parameters);
    }
    
    return selected;
  }

  /**
   * Crossover for genetic algorithm
   */
  crossover(parents, crossoverRate) {
    const offspring = [];
    
    for (let i = 0; i < parents.length; i += 2) {
      const parent1 = parents[i];
      const parent2 = parents[i + 1] || parents[0];
      
      if (Math.random() < crossoverRate) {
        const [child1, child2] = this.performCrossover(parent1, parent2);
        offspring.push(child1, child2);
      } else {
        offspring.push(parent1, parent2);
      }
    }
    
    return offspring;
  }

  /**
   * Perform crossover between two parents
   */
  performCrossover(parent1, parent2) {
    const child1 = JSON.parse(JSON.stringify(parent1));
    const child2 = JSON.parse(JSON.stringify(parent2));
    
    const paramPaths = Object.keys(this.config.parameterRanges);
    const crossoverPoint = Math.floor(Math.random() * paramPaths.length);
    
    for (let i = crossoverPoint; i < paramPaths.length; i++) {
      const paramPath = paramPaths[i];
      const value1 = this.getNestedParameter(parent1, paramPath);
      const value2 = this.getNestedParameter(parent2, paramPath);
      
      if (value1 !== undefined && value2 !== undefined) {
        this.setNestedParameter(child1, paramPath, value2);
        this.setNestedParameter(child2, paramPath, value1);
      }
    }
    
    return [child1, child2];
  }

  /**
   * Mutation for genetic algorithm
   */
  mutation(offspring, mutationRate) {
    const mutated = [];
    
    for (const individual of offspring) {
      const mutatedIndividual = JSON.parse(JSON.stringify(individual));
      
      for (const paramPath of Object.keys(this.config.parameterRanges)) {
        if (Math.random() < mutationRate) {
          const range = this.config.parameterRanges[paramPath];
          const currentValue = this.getNestedParameter(mutatedIndividual, paramPath);
          
          if (currentValue !== undefined) {
            // Gaussian mutation
            const mutationStrength = (range.max - range.min) * 0.1;
            const mutation = (Math.random() - 0.5) * 2 * mutationStrength;
            let newValue = currentValue + mutation;
            
            // Clamp to valid range
            newValue = Math.max(range.min, Math.min(range.max, newValue));
            newValue = Math.round(newValue / range.step) * range.step;
            
            this.setNestedParameter(mutatedIndividual, paramPath, newValue);
          }
        }
      }
      
      mutated.push(mutatedIndividual);
    }
    
    return mutated;
  }

  /**
   * Validate optimization results
   */
  async validateOptimizationResults(results, currentParameters) {
    try {
      // Check if improvement is significant
      if (results.improvement < 0.05) { // Less than 5% improvement
        logger.warn('Optimization improvement below threshold', {
          improvement: results.improvement,
          threshold: 0.05
        });
      }

      // Check parameter changes are within reasonable bounds
      const maxChange = this.config.optimization?.maxParameterChange || 0.2;
      const parameterChanges = this.calculateParameterChanges(currentParameters, results.bestParameters);
      
      for (const [param, change] of Object.entries(parameterChanges)) {
        if (Math.abs(change) > maxChange) {
          logger.warn('Large parameter change detected', {
            parameter: param,
            change: change,
            maxAllowed: maxChange
          });
        }
      }

      return results;

    } catch (error) {
      logger.error('Error validating optimization results', { error: error.message });
      return results;
    }
  }

  /**
   * Calculate parameter changes
   */
  calculateParameterChanges(currentParams, optimizedParams) {
    const changes = {};
    
    for (const paramPath of Object.keys(this.config.parameterRanges)) {
      const currentValue = this.getNestedParameter(currentParams, paramPath);
      const optimizedValue = this.getNestedParameter(optimizedParams, paramPath);
      
      if (currentValue !== undefined && optimizedValue !== undefined && currentValue !== 0) {
        changes[paramPath] = (optimizedValue - currentValue) / currentValue;
      }
    }
    
    return changes;
  }

  /**
   * Record optimization for history
   */
  recordOptimization(record) {
    this.optimizationHistory.push(record);
    
    // Keep only last 100 records
    if (this.optimizationHistory.length > 100) {
      this.optimizationHistory.shift();
    }
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory() {
    return this.optimizationHistory;
  }
}

module.exports = ParameterOptimizer;
