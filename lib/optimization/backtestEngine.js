const logger = require('../logger');

/**
 * Backtest Engine
 * Simulates trading strategies with historical data for optimization
 */
class BacktestEngine {
  constructor(config = {}) {
    this.config = {
      // Backtest settings
      commission: config.commission || 0.001, // 0.1% commission
      slippage: config.slippage || 0.0005, // 0.05% slippage
      initialCapital: config.initialCapital || 10000,
      
      // Data settings
      dataSource: config.dataSource || 'historical_trades',
      timeframe: config.timeframe || '1h',
      
      // Performance calculation
      riskFreeRate: config.riskFreeRate || 0.02, // 2% annual
      tradingDaysPerYear: config.tradingDaysPerYear || 365,
      
      ...config
    };

    this.backtestCache = new Map();
  }

  /**
   * Run backtest with given parameters
   */
  async runBacktest(parameters, performanceData, options = {}) {
    try {
      const cacheKey = this.generateCacheKey(parameters, performanceData);
      
      // Check cache first
      if (this.backtestCache.has(cacheKey)) {
        return this.backtestCache.get(cacheKey);
      }

      logger.debug('Running backtest', {
        parameters: Object.keys(parameters).length,
        trades: performanceData.totalTrades
      });

      // Simulate trades with new parameters
      const simulatedTrades = await this.simulateTrades(parameters, performanceData);
      
      // Calculate performance metrics
      const metrics = this.calculateBacktestMetrics(simulatedTrades);
      
      // Add additional analysis
      const analysis = this.analyzeBacktestResults(simulatedTrades, parameters);
      
      const result = {
        ...metrics,
        ...analysis,
        parameters: parameters,
        totalTrades: simulatedTrades.length,
        simulatedTrades: simulatedTrades.slice(0, 10), // Keep only first 10 for reference
        backtestDate: new Date()
      };

      // Cache result
      this.backtestCache.set(cacheKey, result);
      
      return result;

    } catch (error) {
      logger.error('Error running backtest', { error: error.message });
      throw error;
    }
  }

  /**
   * Simulate trades with new parameters
   */
  async simulateTrades(parameters, performanceData) {
    try {
      const originalTrades = performanceData.trades || [];
      const simulatedTrades = [];

      for (const originalTrade of originalTrades) {
        const simulatedTrade = await this.simulateSingleTrade(originalTrade, parameters);
        if (simulatedTrade) {
          simulatedTrades.push(simulatedTrade);
        }
      }

      return simulatedTrades;

    } catch (error) {
      logger.error('Error simulating trades', { error: error.message });
      throw error;
    }
  }

  /**
   * Simulate a single trade with new parameters
   */
  async simulateSingleTrade(originalTrade, parameters) {
    try {
      // Extract relevant parameters
      const riskParams = parameters.riskManagement || {};
      const entryParams = parameters.entryFilters || {};
      const exitParams = parameters.exitConditions || {};

      // Simulate entry decision
      if (!this.shouldEnterTrade(originalTrade, entryParams)) {
        return null; // Trade would be filtered out
      }

      // Calculate new stop loss and take profit levels
      const entry = originalTrade.entry || originalTrade.price;
      const newStopLoss = this.calculateNewStopLoss(entry, originalTrade.type, riskParams);
      const newTakeProfit = this.calculateNewTakeProfit(entry, originalTrade.type, riskParams);

      // Simulate trade execution
      const simulatedResult = this.simulateTradeExecution(
        originalTrade,
        entry,
        newStopLoss,
        newTakeProfit,
        exitParams
      );

      // Apply commission and slippage
      const finalResult = this.applyTradingCosts(simulatedResult);

      return {
        ...originalTrade,
        simulatedEntry: entry,
        simulatedStopLoss: newStopLoss,
        simulatedTakeProfit: newTakeProfit,
        simulatedPnL: finalResult.pnl,
        simulatedPnLPercent: finalResult.pnlPercent,
        simulatedExitReason: finalResult.exitReason,
        simulatedExitPrice: finalResult.exitPrice,
        originalPnL: originalTrade.pnlPercent || 0,
        improvement: finalResult.pnlPercent - (originalTrade.pnlPercent || 0)
      };

    } catch (error) {
      logger.warn('Error simulating single trade', { 
        error: error.message, 
        tradeId: originalTrade._id 
      });
      return null;
    }
  }

  /**
   * Check if trade should be entered based on new filters
   */
  shouldEnterTrade(trade, entryParams) {
    // Simulate entry filters
    const strengthThreshold = entryParams.strengthThreshold || 0.7;
    const volumeMultiplier = entryParams.volumeMultiplier || 1.5;
    const confirmationRequired = entryParams.confirmationRequired || false;

    // Simulate signal strength (based on original trade quality)
    const signalStrength = this.estimateSignalStrength(trade);
    if (signalStrength < strengthThreshold) {
      return false;
    }

    // Simulate volume confirmation
    const volumeConfirmation = this.estimateVolumeConfirmation(trade);
    if (volumeConfirmation < volumeMultiplier) {
      return false;
    }

    // Additional confirmation checks
    if (confirmationRequired) {
      const hasConfirmation = this.estimateConfirmation(trade);
      if (!hasConfirmation) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate new stop loss level
   */
  calculateNewStopLoss(entry, tradeType, riskParams) {
    const stopLossPercent = riskParams.stopLossPercent || 0.5;
    
    if (tradeType === 'BUY' || tradeType === 'LONG') {
      return entry * (1 - stopLossPercent / 100);
    } else {
      return entry * (1 + stopLossPercent / 100);
    }
  }

  /**
   * Calculate new take profit level
   */
  calculateNewTakeProfit(entry, tradeType, riskParams) {
    const takeProfitMultiplier = riskParams.takeProfitMultiplier || 2.0;
    const stopLossPercent = riskParams.stopLossPercent || 0.5;
    const takeProfitPercent = stopLossPercent * takeProfitMultiplier;
    
    if (tradeType === 'BUY' || tradeType === 'LONG') {
      return entry * (1 + takeProfitPercent / 100);
    } else {
      return entry * (1 - takeProfitPercent / 100);
    }
  }

  /**
   * Simulate trade execution
   */
  simulateTradeExecution(originalTrade, entry, stopLoss, takeProfit, exitParams) {
    // Get historical price data or use original trade data
    const priceData = this.getTradeHistoricalData(originalTrade);
    
    // Simulate price movement and exit conditions
    const exitResult = this.simulateExit(
      priceData,
      entry,
      stopLoss,
      takeProfit,
      originalTrade.type,
      exitParams
    );

    return exitResult;
  }

  /**
   * Simulate exit conditions
   */
  simulateExit(priceData, entry, stopLoss, takeProfit, tradeType, exitParams) {
    const isLong = tradeType === 'BUY' || tradeType === 'LONG';
    
    // Use original trade outcome as basis for simulation
    const originalOutcome = priceData.outcome || 'unknown';
    const originalExitPrice = priceData.exitPrice || entry;
    
    let exitPrice = originalExitPrice;
    let exitReason = 'market_close';

    // Simulate exit based on new levels
    if (isLong) {
      if (originalExitPrice <= stopLoss) {
        exitPrice = stopLoss;
        exitReason = 'stop_loss';
      } else if (originalExitPrice >= takeProfit) {
        exitPrice = takeProfit;
        exitReason = 'take_profit';
      }
    } else {
      if (originalExitPrice >= stopLoss) {
        exitPrice = stopLoss;
        exitReason = 'stop_loss';
      } else if (originalExitPrice <= takeProfit) {
        exitPrice = takeProfit;
        exitReason = 'take_profit';
      }
    }

    // Calculate PnL
    const pnl = isLong 
      ? ((exitPrice - entry) / entry) * 100
      : ((entry - exitPrice) / entry) * 100;

    return {
      exitPrice: exitPrice,
      exitReason: exitReason,
      pnl: pnl * 100, // Absolute PnL
      pnlPercent: pnl
    };
  }

  /**
   * Apply trading costs (commission and slippage)
   */
  applyTradingCosts(tradeResult) {
    const commission = this.config.commission * 2; // Entry + exit
    const slippage = this.config.slippage * 2; // Entry + exit
    
    const totalCosts = (commission + slippage) * 100; // Convert to percentage
    
    return {
      ...tradeResult,
      pnl: tradeResult.pnl - totalCosts,
      pnlPercent: tradeResult.pnlPercent - (totalCosts / 100),
      tradingCosts: totalCosts
    };
  }

  /**
   * Get historical data for trade simulation
   */
  getTradeHistoricalData(trade) {
    // In a real implementation, this would fetch actual OHLC data
    // For simulation, we use the original trade outcome
    return {
      outcome: trade.status,
      exitPrice: trade.exitPrice || trade.entry || trade.price,
      highPrice: trade.highPrice || (trade.entry || trade.price) * 1.02,
      lowPrice: trade.lowPrice || (trade.entry || trade.price) * 0.98
    };
  }

  /**
   * Estimate signal strength for filtering
   */
  estimateSignalStrength(trade) {
    // Simulate signal strength based on trade characteristics
    let strength = 0.7; // Base strength
    
    // Adjust based on trade outcome
    if (trade.pnlPercent > 2) strength += 0.2;
    else if (trade.pnlPercent > 0) strength += 0.1;
    else if (trade.pnlPercent < -2) strength -= 0.2;
    else if (trade.pnlPercent < 0) strength -= 0.1;
    
    // Add some randomness to simulate market conditions
    strength += (Math.random() - 0.5) * 0.2;
    
    return Math.max(0, Math.min(1, strength));
  }

  /**
   * Estimate volume confirmation
   */
  estimateVolumeConfirmation(trade) {
    // Simulate volume multiplier
    const baseMultiplier = 1.5;
    const randomFactor = 0.5 + Math.random(); // 0.5 to 1.5
    
    return baseMultiplier * randomFactor;
  }

  /**
   * Estimate additional confirmation
   */
  estimateConfirmation(trade) {
    // Simulate confirmation based on trade quality
    const confirmationProbability = trade.pnlPercent > 0 ? 0.8 : 0.4;
    return Math.random() < confirmationProbability;
  }

  /**
   * Calculate comprehensive backtest metrics
   */
  calculateBacktestMetrics(trades) {
    if (trades.length === 0) {
      return this.getEmptyMetrics();
    }

    const winningTrades = trades.filter(t => t.simulatedPnLPercent > 0);
    const losingTrades = trades.filter(t => t.simulatedPnLPercent < 0);
    
    const totalTrades = trades.length;
    const winRate = (winningTrades.length / totalTrades) * 100;
    
    const totalPnL = trades.reduce((sum, t) => sum + t.simulatedPnLPercent, 0);
    const avgWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + t.simulatedPnLPercent, 0) / winningTrades.length 
      : 0;
    const avgLoss = losingTrades.length > 0 
      ? Math.abs(losingTrades.reduce((sum, t) => sum + t.simulatedPnLPercent, 0) / losingTrades.length)
      : 0;
    
    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0;
    const expectancy = (winRate / 100) * avgWin - ((100 - winRate) / 100) * avgLoss;

    // Calculate Sharpe ratio
    const returns = trades.map(t => t.simulatedPnLPercent / 100);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const stdDev = Math.sqrt(
      returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    );
    const annualizedReturn = avgReturn * this.config.tradingDaysPerYear;
    const annualizedStdDev = stdDev * Math.sqrt(this.config.tradingDaysPerYear);
    const sharpeRatio = annualizedStdDev > 0 
      ? (annualizedReturn - this.config.riskFreeRate) / annualizedStdDev 
      : 0;

    // Calculate max drawdown
    const maxDrawdown = this.calculateMaxDrawdown(trades);

    return {
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: Math.round(winRate * 100) / 100,
      totalPnL: Math.round(totalPnL * 100) / 100,
      avgWin: Math.round(avgWin * 100) / 100,
      avgLoss: Math.round(avgLoss * 100) / 100,
      profitFactor: Math.round(profitFactor * 100) / 100,
      expectancy: Math.round(expectancy * 100) / 100,
      sharpeRatio: Math.round(sharpeRatio * 1000) / 1000,
      maxDrawdown: Math.round(maxDrawdown * 100) / 100,
      largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.simulatedPnLPercent)) : 0,
      largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.simulatedPnLPercent)) : 0
    };
  }

  /**
   * Calculate maximum drawdown
   */
  calculateMaxDrawdown(trades) {
    if (trades.length === 0) return 0;

    let runningValue = this.config.initialCapital;
    let peak = runningValue;
    let maxDrawdown = 0;

    for (const trade of trades) {
      runningValue *= (1 + trade.simulatedPnLPercent / 100);
      
      if (runningValue > peak) {
        peak = runningValue;
      } else {
        const drawdown = ((peak - runningValue) / peak) * 100;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  /**
   * Analyze backtest results
   */
  analyzeBacktestResults(trades, parameters) {
    const analysis = {
      consistency: this.calculateConsistency(trades),
      riskMetrics: this.calculateRiskMetrics(trades),
      tradingPatterns: this.analyzeTradingPatterns(trades),
      parameterImpact: this.analyzeParameterImpact(trades, parameters)
    };

    return analysis;
  }

  /**
   * Calculate consistency metrics
   */
  calculateConsistency(trades) {
    if (trades.length === 0) return { consistencyRatio: 0, volatility: 0 };

    const returns = trades.map(t => t.simulatedPnLPercent);
    const positiveReturns = returns.filter(r => r > 0);
    const consistencyRatio = (positiveReturns.length / returns.length) * 100;

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance);

    return {
      consistencyRatio: Math.round(consistencyRatio * 100) / 100,
      volatility: Math.round(volatility * 100) / 100
    };
  }

  /**
   * Calculate risk metrics
   */
  calculateRiskMetrics(trades) {
    if (trades.length === 0) return { var95: 0, cvar95: 0 };

    const returns = trades.map(t => t.simulatedPnLPercent).sort((a, b) => a - b);
    const var95Index = Math.floor(returns.length * 0.05);
    const var95 = returns[var95Index] || 0;
    
    const tailReturns = returns.slice(0, var95Index + 1);
    const cvar95 = tailReturns.length > 0 
      ? tailReturns.reduce((sum, r) => sum + r, 0) / tailReturns.length 
      : 0;

    return {
      var95: Math.round(var95 * 100) / 100,
      cvar95: Math.round(cvar95 * 100) / 100
    };
  }

  /**
   * Analyze trading patterns
   */
  analyzeTradingPatterns(trades) {
    const patterns = {
      avgHoldingPeriod: 0,
      winStreaks: [],
      lossStreaks: [],
      exitReasons: {}
    };

    // Analyze exit reasons
    trades.forEach(trade => {
      const reason = trade.simulatedExitReason || 'unknown';
      patterns.exitReasons[reason] = (patterns.exitReasons[reason] || 0) + 1;
    });

    // Analyze streaks
    let currentStreak = 0;
    let streakType = null;
    
    trades.forEach(trade => {
      const isWin = trade.simulatedPnLPercent > 0;
      
      if (streakType === null || (isWin && streakType === 'win') || (!isWin && streakType === 'loss')) {
        currentStreak++;
        streakType = isWin ? 'win' : 'loss';
      } else {
        if (streakType === 'win') {
          patterns.winStreaks.push(currentStreak);
        } else {
          patterns.lossStreaks.push(currentStreak);
        }
        currentStreak = 1;
        streakType = isWin ? 'win' : 'loss';
      }
    });

    return patterns;
  }

  /**
   * Analyze parameter impact
   */
  analyzeParameterImpact(trades, parameters) {
    const impact = {
      improvementRate: 0,
      avgImprovement: 0,
      parameterEffectiveness: {}
    };

    const improvements = trades.map(t => t.improvement || 0);
    const positiveImprovements = improvements.filter(i => i > 0);
    
    impact.improvementRate = (positiveImprovements.length / improvements.length) * 100;
    impact.avgImprovement = improvements.reduce((sum, i) => sum + i, 0) / improvements.length;

    return impact;
  }

  /**
   * Validate parameters
   */
  async validateParameters(parameters, validationPeriod) {
    try {
      // Simulate validation with limited historical data
      const validationData = await this.getValidationData(validationPeriod);
      const backtestResult = await this.runBacktest(parameters, validationData);
      
      const improvement = this.calculateValidationImprovement(backtestResult);
      const confidence = this.calculateValidationConfidence(backtestResult);

      return {
        improvement: improvement,
        metrics: backtestResult,
        confidence: confidence,
        validationPeriod: validationPeriod
      };

    } catch (error) {
      logger.error('Error validating parameters', { error: error.message });
      throw error;
    }
  }

  /**
   * Validate strategy
   */
  async validateStrategy(strategy, validationPeriod) {
    try {
      // Simulate strategy validation
      const validationData = await this.getValidationData(validationPeriod);
      
      // For now, return simulated validation results
      const improvement = 0.03 + Math.random() * 0.1; // 3-13% improvement
      const confidence = 0.6 + Math.random() * 0.3; // 60-90% confidence

      return {
        improvement: improvement,
        metrics: {
          winRate: 55 + Math.random() * 20,
          profitFactor: 1.2 + Math.random() * 0.8,
          sharpeRatio: 0.5 + Math.random() * 1.0,
          maxDrawdown: 5 + Math.random() * 10
        },
        confidence: confidence,
        validationPeriod: validationPeriod
      };

    } catch (error) {
      logger.error('Error validating strategy', { error: error.message });
      throw error;
    }
  }

  /**
   * Validate combined optimization
   */
  async validateCombined(parameters, strategy, validationPeriod) {
    try {
      // Simulate combined validation
      const paramValidation = await this.validateParameters(parameters, validationPeriod);
      const strategyValidation = await this.validateStrategy(strategy, validationPeriod);
      
      // Combine results
      const combinedImprovement = (paramValidation.improvement + strategyValidation.improvement) * 0.8; // Synergy factor
      const combinedConfidence = Math.min(paramValidation.confidence, strategyValidation.confidence);

      return {
        improvement: combinedImprovement,
        metrics: {
          winRate: Math.max(paramValidation.metrics.winRate, strategyValidation.metrics.winRate),
          profitFactor: Math.max(paramValidation.metrics.profitFactor, strategyValidation.metrics.profitFactor),
          sharpeRatio: Math.max(paramValidation.metrics.sharpeRatio, strategyValidation.metrics.sharpeRatio),
          maxDrawdown: Math.min(paramValidation.metrics.maxDrawdown, strategyValidation.metrics.maxDrawdown)
        },
        confidence: combinedConfidence,
        validationPeriod: validationPeriod
      };

    } catch (error) {
      logger.error('Error validating combined optimization', { error: error.message });
      throw error;
    }
  }

  /**
   * Get validation data
   */
  async getValidationData(days) {
    try {
      // In a real implementation, this would fetch recent historical data
      // For simulation, return mock data
      return {
        totalTrades: Math.floor(days / 2), // Assume ~0.5 trades per day
        trades: [],
        metrics: {
          winRate: 50 + Math.random() * 20,
          profitFactor: 1.0 + Math.random() * 0.5,
          sharpeRatio: 0.3 + Math.random() * 0.7,
          maxDrawdown: 8 + Math.random() * 7
        }
      };
    } catch (error) {
      logger.error('Error getting validation data', { error: error.message });
      throw error;
    }
  }

  /**
   * Calculate validation improvement
   */
  calculateValidationImprovement(backtestResult) {
    // Simulate improvement calculation
    const baseScore = 50; // Baseline score
    const currentScore = this.calculateOverallScore(backtestResult);
    
    return (currentScore - baseScore) / baseScore;
  }

  /**
   * Calculate validation confidence
   */
  calculateValidationConfidence(backtestResult) {
    // Confidence based on trade count and performance consistency
    const tradeCountFactor = Math.min(backtestResult.totalTrades / 50, 1);
    const performanceFactor = backtestResult.winRate > 50 ? 0.8 : 0.6;
    
    return tradeCountFactor * performanceFactor;
  }

  /**
   * Calculate overall performance score
   */
  calculateOverallScore(metrics) {
    return (
      metrics.winRate * 0.3 +
      metrics.profitFactor * 20 +
      metrics.sharpeRatio * 30 +
      (100 - metrics.maxDrawdown) * 0.2
    );
  }

  /**
   * Generate cache key for backtest results
   */
  generateCacheKey(parameters, performanceData) {
    const paramString = JSON.stringify(parameters);
    const dataString = `${performanceData.totalTrades}_${performanceData.period}`;
    return `backtest_${Buffer.from(paramString).toString('base64').slice(0, 20)}_${dataString}`;
  }

  /**
   * Get empty metrics structure
   */
  getEmptyMetrics() {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalPnL: 0,
      avgWin: 0,
      avgLoss: 0,
      profitFactor: 0,
      expectancy: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      largestWin: 0,
      largestLoss: 0
    };
  }

  /**
   * Clear backtest cache
   */
  clearCache() {
    this.backtestCache.clear();
    logger.info('Backtest cache cleared');
  }
}

module.exports = BacktestEngine;
