const EventEmitter = require('events');
const logger = require('../logger');
const AIClient = require('../ai/aiClient');
const ParameterOptimizer = require('./parameterOptimizer');
const StrategyOptimizer = require('./strategyOptimizer');
const BacktestEngine = require('./backtestEngine');

/**
 * AI-Powered Optimization Engine
 * Coordinates AI-driven optimization of trading parameters and strategies
 */
class AIOptimizationEngine extends EventEmitter {
  constructor(config = {}) {
    super();

    this.config = {
      // AI settings
      ai: {
        enabled: config.ai?.enabled || true,
        confidenceThreshold: config.ai?.confidenceThreshold || 70,
        autoImplementThreshold: config.ai?.autoImplementThreshold || 85,
        maxOptimizationsPerDay: config.ai?.maxOptimizationsPerDay || 3,
        ...config.ai
      },

      // Optimization settings
      optimization: {
        minTradesForOptimization: config.optimization?.minTradesForOptimization || 100,
        optimizationInterval: config.optimization?.optimizationInterval || 7 * 24 * 60 * 60 * 1000, // 7 days
        backtestPeriod: config.optimization?.backtestPeriod || 90, // 90 days
        validationPeriod: config.optimization?.validationPeriod || 30, // 30 days
        maxParameterChange: config.optimization?.maxParameterChange || 0.2, // 20% max change
        ...config.optimization
      },

      // Safety settings
      safety: {
        enableRollback: config.safety?.enableRollback || true,
        rollbackThreshold: config.safety?.rollbackThreshold || -0.15, // -15% performance drop
        maxConsecutiveOptimizations: config.safety?.maxConsecutiveOptimizations || 3,
        cooldownPeriod: config.safety?.cooldownPeriod || 24 * 60 * 60 * 1000, // 24 hours
        ...config.safety
      },

      // Performance tracking
      tracking: {
        baselineWindow: config.tracking?.baselineWindow || 30, // 30 days
        performanceMetrics: config.tracking?.performanceMetrics || [
          'winRate', 'profitFactor', 'sharpeRatio', 'maxDrawdown', 'expectancy'
        ],
        ...config.tracking
      },

      ...config
    };

    // Initialize components
    this.aiClient = new AIClient(this.config.ai);
    this.parameterOptimizer = new ParameterOptimizer(this.config.optimization);
    this.strategyOptimizer = new StrategyOptimizer(this.config.optimization);
    this.backtestEngine = new BacktestEngine(this.config.optimization);

    // State management
    this.isOptimizing = false;
    this.optimizationHistory = [];
    this.currentBaseline = null;
    this.lastOptimizationTime = null;
    this.consecutiveOptimizations = 0;
    this.rollbackStack = [];

    // Performance tracking
    this.performanceBaseline = null;
    this.currentPerformance = null;
    this.optimizationResults = new Map();

    // Timers
    this.optimizationTimer = null;
    this.performanceMonitorTimer = null;
  }

  /**
   * Start the optimization engine
   */
  async startOptimization() {
    try {
      if (this.isOptimizing) {
        logger.warn('Optimization engine is already running');
        return;
      }

      this.isOptimizing = true;

      // Initialize baseline performance
      await this.initializeBaseline();

      // Start periodic optimization
      this.scheduleOptimization();

      // Start performance monitoring
      this.startPerformanceMonitoring();

      logger.info('AI optimization engine started', {
        optimizationInterval: this.config.optimization.optimizationInterval,
        confidenceThreshold: this.config.ai.confidenceThreshold
      });

      this.emit('optimization_started');

    } catch (error) {
      logger.error('Error starting optimization engine', { error: error.message });
      this.isOptimizing = false;
      throw error;
    }
  }

  /**
   * Stop the optimization engine
   */
  stopOptimization() {
    try {
      this.isOptimizing = false;

      if (this.optimizationTimer) {
        clearTimeout(this.optimizationTimer);
        this.optimizationTimer = null;
      }

      if (this.performanceMonitorTimer) {
        clearInterval(this.performanceMonitorTimer);
        this.performanceMonitorTimer = null;
      }

      logger.info('AI optimization engine stopped');
      this.emit('optimization_stopped');

    } catch (error) {
      logger.error('Error stopping optimization engine', { error: error.message });
    }
  }

  /**
   * Run comprehensive optimization
   */
  async runOptimization(options = {}) {
    try {
      if (!this.isOptimizing) {
        throw new Error('Optimization engine is not running');
      }

      // Check cooldown period
      if (this.isInCooldown()) {
        logger.info('Optimization skipped due to cooldown period');
        return { skipped: true, reason: 'cooldown' };
      }

      // Check consecutive optimization limit
      if (this.consecutiveOptimizations >= this.config.safety.maxConsecutiveOptimizations) {
        logger.warn('Maximum consecutive optimizations reached, entering cooldown');
        this.enterCooldown();
        return { skipped: true, reason: 'max_consecutive' };
      }

      logger.info('Starting comprehensive optimization', {
        type: options.type || 'scheduled',
        consecutiveCount: this.consecutiveOptimizations
      });

      const optimizationId = this.generateOptimizationId();
      const startTime = new Date();

      // 1. Gather performance data
      const performanceData = await this.gatherPerformanceData();

      if (performanceData.totalTrades < this.config.optimization.minTradesForOptimization) {
        logger.info('Insufficient trades for optimization', {
          trades: performanceData.totalTrades,
          required: this.config.optimization.minTradesForOptimization
        });
        return { skipped: true, reason: 'insufficient_data' };
      }

      // 2. AI analysis and recommendations
      const aiAnalysis = await this.performAIAnalysis(performanceData);

      // 3. Parameter optimization
      const parameterResults = await this.optimizeParameters(performanceData, aiAnalysis);

      // 4. Strategy optimization
      const strategyResults = await this.optimizeStrategy(performanceData, aiAnalysis);

      // 5. Validate optimizations through backtesting
      const validationResults = await this.validateOptimizations(
        parameterResults,
        strategyResults,
        performanceData
      );

      // 6. Implement optimizations if validation passes
      const implementationResults = await this.implementOptimizations(
        validationResults,
        aiAnalysis
      );

      // 7. Record optimization results
      const optimizationRecord = {
        id: optimizationId,
        timestamp: startTime,
        duration: Date.now() - startTime.getTime(),
        type: options.type || 'scheduled',
        performanceData,
        aiAnalysis,
        parameterResults,
        strategyResults,
        validationResults,
        implementationResults,
        success: implementationResults.implemented > 0
      };

      this.optimizationHistory.push(optimizationRecord);
      this.lastOptimizationTime = new Date();

      if (implementationResults.implemented > 0) {
        this.consecutiveOptimizations++;
        this.addToRollbackStack(optimizationRecord);
      } else {
        this.consecutiveOptimizations = 0;
      }

      logger.info('Optimization completed', {
        id: optimizationId,
        implemented: implementationResults.implemented,
        skipped: implementationResults.skipped,
        failed: implementationResults.failed,
        duration: optimizationRecord.duration
      });

      this.emit('optimization_completed', optimizationRecord);

      return optimizationRecord;

    } catch (error) {
      logger.error('Error during optimization', { error: error.message });
      this.emit('optimization_error', error);
      throw error;
    }
  }

  /**
   * Perform AI analysis of performance data
   */
  async performAIAnalysis(performanceData) {
    try {
      if (!this.config.ai.enabled) {
        return this.generateFallbackAnalysis(performanceData);
      }

      const prompt = this.buildOptimizationPrompt(performanceData);
      const aiResponse = await this.aiClient.analyze(prompt);

      // Validate AI response
      if (!aiResponse.confidenceScore || aiResponse.confidenceScore < 50) {
        logger.warn('AI analysis confidence too low, using fallback', {
          confidence: aiResponse.confidenceScore
        });
        return this.generateFallbackAnalysis(performanceData);
      }

      return {
        source: 'ai',
        confidence: aiResponse.confidenceScore,
        insights: aiResponse.insights || [],
        recommendations: aiResponse.recommendations || [],
        parameterSuggestions: aiResponse.parameterSuggestions || {},
        strategySuggestions: aiResponse.strategySuggestions || {},
        riskAssessment: aiResponse.riskAssessment || {},
        expectedImprovement: aiResponse.expectedImprovement || 0,
        implementationPriority: aiResponse.implementationPriority || 'medium'
      };

    } catch (error) {
      logger.error('Error in AI analysis', { error: error.message });
      return this.generateFallbackAnalysis(performanceData);
    }
  }

  /**
   * Optimize parameters using AI recommendations and backtesting
   */
  async optimizeParameters(performanceData, aiAnalysis) {
    try {
      const suggestions = aiAnalysis.parameterSuggestions || {};
      const currentParams = await this.getCurrentParameters();

      const optimizationResults = await this.parameterOptimizer.optimize({
        currentParameters: currentParams,
        performanceData: performanceData,
        aiSuggestions: suggestions,
        backtestPeriod: this.config.optimization.backtestPeriod
      });

      return {
        success: true,
        currentParameters: currentParams,
        optimizedParameters: optimizationResults.bestParameters,
        improvement: optimizationResults.improvement,
        backtestResults: optimizationResults.backtestResults,
        confidence: optimizationResults.confidence
      };

    } catch (error) {
      logger.error('Error optimizing parameters', { error: error.message });
      return {
        success: false,
        error: error.message,
        currentParameters: await this.getCurrentParameters()
      };
    }
  }

  /**
   * Optimize strategy using AI recommendations
   */
  async optimizeStrategy(performanceData, aiAnalysis) {
    try {
      const suggestions = aiAnalysis.strategySuggestions || {};
      const currentStrategy = await this.getCurrentStrategy();

      const optimizationResults = await this.strategyOptimizer.optimize({
        currentStrategy: currentStrategy,
        performanceData: performanceData,
        aiSuggestions: suggestions,
        backtestPeriod: this.config.optimization.backtestPeriod
      });

      return {
        success: true,
        currentStrategy: currentStrategy,
        optimizedStrategy: optimizationResults.bestStrategy,
        improvement: optimizationResults.improvement,
        backtestResults: optimizationResults.backtestResults,
        confidence: optimizationResults.confidence
      };

    } catch (error) {
      logger.error('Error optimizing strategy', { error: error.message });
      return {
        success: false,
        error: error.message,
        currentStrategy: await this.getCurrentStrategy()
      };
    }
  }

  /**
   * Validate optimizations through comprehensive backtesting
   */
  async validateOptimizations(parameterResults, strategyResults, performanceData) {
    try {
      const validationResults = {
        parameters: { valid: false, improvement: 0 },
        strategy: { valid: false, improvement: 0 },
        combined: { valid: false, improvement: 0 }
      };

      // Validate parameter optimization
      if (parameterResults.success && parameterResults.optimizedParameters) {
        const paramValidation = await this.backtestEngine.validateParameters(
          parameterResults.optimizedParameters,
          this.config.optimization.validationPeriod
        );

        validationResults.parameters = {
          valid: paramValidation.improvement > 0.05, // 5% minimum improvement
          improvement: paramValidation.improvement,
          metrics: paramValidation.metrics,
          confidence: paramValidation.confidence
        };
      }

      // Validate strategy optimization
      if (strategyResults.success && strategyResults.optimizedStrategy) {
        const strategyValidation = await this.backtestEngine.validateStrategy(
          strategyResults.optimizedStrategy,
          this.config.optimization.validationPeriod
        );

        validationResults.strategy = {
          valid: strategyValidation.improvement > 0.05,
          improvement: strategyValidation.improvement,
          metrics: strategyValidation.metrics,
          confidence: strategyValidation.confidence
        };
      }

      // Validate combined optimization
      if (validationResults.parameters.valid || validationResults.strategy.valid) {
        const combinedValidation = await this.backtestEngine.validateCombined(
          parameterResults.optimizedParameters,
          strategyResults.optimizedStrategy,
          this.config.optimization.validationPeriod
        );

        validationResults.combined = {
          valid: combinedValidation.improvement > 0.03, // 3% minimum for combined
          improvement: combinedValidation.improvement,
          metrics: combinedValidation.metrics,
          confidence: combinedValidation.confidence
        };
      }

      return validationResults;

    } catch (error) {
      logger.error('Error validating optimizations', { error: error.message });
      return {
        parameters: { valid: false, error: error.message },
        strategy: { valid: false, error: error.message },
        combined: { valid: false, error: error.message }
      };
    }
  }

  /**
   * Implement validated optimizations
   */
  async implementOptimizations(validationResults, aiAnalysis) {
    try {
      const results = {
        implemented: 0,
        skipped: 0,
        failed: 0,
        changes: []
      };

      // Check AI confidence for auto-implementation
      const autoImplement = aiAnalysis.confidence >= this.config.ai.autoImplementThreshold;

      if (!autoImplement) {
        logger.info('AI confidence too low for auto-implementation', {
          confidence: aiAnalysis.confidence,
          threshold: this.config.ai.autoImplementThreshold
        });
        results.skipped = 1;
        return results;
      }

      // Implement parameter changes
      if (validationResults.parameters.valid) {
        try {
          await this.implementParameterChanges(validationResults.parameters);
          results.implemented++;
          results.changes.push({
            type: 'parameters',
            improvement: validationResults.parameters.improvement,
            confidence: validationResults.parameters.confidence
          });
        } catch (error) {
          logger.error('Failed to implement parameter changes', { error: error.message });
          results.failed++;
        }
      }

      // Implement strategy changes
      if (validationResults.strategy.valid) {
        try {
          await this.implementStrategyChanges(validationResults.strategy);
          results.implemented++;
          results.changes.push({
            type: 'strategy',
            improvement: validationResults.strategy.improvement,
            confidence: validationResults.strategy.confidence
          });
        } catch (error) {
          logger.error('Failed to implement strategy changes', { error: error.message });
          results.failed++;
        }
      }

      return results;

    } catch (error) {
      logger.error('Error implementing optimizations', { error: error.message });
      return {
        implemented: 0,
        skipped: 0,
        failed: 1,
        changes: [],
        error: error.message
      };
    }
  }

  /**
   * Initialize performance baseline
   */
  async initializeBaseline() {
    try {
      const performanceData = await this.gatherPerformanceData(this.config.tracking.baselineWindow);

      this.performanceBaseline = {
        timestamp: new Date(),
        period: this.config.tracking.baselineWindow,
        metrics: this.extractKeyMetrics(performanceData),
        totalTrades: performanceData.totalTrades
      };

      logger.info('Performance baseline initialized', {
        period: this.config.tracking.baselineWindow,
        trades: this.performanceBaseline.totalTrades,
        winRate: this.performanceBaseline.metrics.winRate
      });

    } catch (error) {
      logger.error('Error initializing baseline', { error: error.message });
      throw error;
    }
  }

  /**
   * Gather comprehensive performance data
   */
  async gatherPerformanceData(days = 30) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const trades = await TradingSignal.find({
        status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
        createdAt: { $gte: cutoffDate },
        isConflictNotification: false
      }).sort({ createdAt: -1 });

      // Calculate comprehensive metrics
      const performanceMetrics = this.calculatePerformanceMetrics(trades);

      return {
        period: days,
        totalTrades: trades.length,
        trades: trades,
        metrics: performanceMetrics,
        symbols: this.analyzeSymbolPerformance(trades),
        timeframes: this.analyzeTimeframePerformance(trades),
        patterns: this.analyzePatterns(trades)
      };

    } catch (error) {
      logger.error('Error gathering performance data', { error: error.message });
      throw error;
    }
  }

  /**
   * Calculate comprehensive performance metrics
   */
  calculatePerformanceMetrics(trades) {
    if (trades.length === 0) {
      return {
        winRate: 0,
        profitFactor: 0,
        sharpeRatio: 0,
        maxDrawdown: 0,
        expectancy: 0,
        totalPnL: 0,
        avgWin: 0,
        avgLoss: 0
      };
    }

    const winningTrades = trades.filter(t => (t.pnlPercent || 0) > 0);
    const losingTrades = trades.filter(t => (t.pnlPercent || 0) < 0);

    const winRate = (winningTrades.length / trades.length) * 100;
    const totalPnL = trades.reduce((sum, t) => sum + (t.pnlPercent || 0), 0);
    const avgWin = winningTrades.length > 0
      ? winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / winningTrades.length
      : 0;
    const avgLoss = losingTrades.length > 0
      ? Math.abs(losingTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / losingTrades.length)
      : 0;

    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0;
    const expectancy = (winRate / 100) * avgWin - ((100 - winRate) / 100) * avgLoss;

    // Calculate Sharpe ratio (simplified)
    const returns = trades.map(t => (t.pnlPercent || 0) / 100);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const stdDev = Math.sqrt(
      returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    );
    const sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;

    // Calculate max drawdown
    const maxDrawdown = this.calculateMaxDrawdown(trades);

    return {
      winRate: Math.round(winRate * 100) / 100,
      profitFactor: Math.round(profitFactor * 100) / 100,
      sharpeRatio: Math.round(sharpeRatio * 1000) / 1000,
      maxDrawdown: Math.round(maxDrawdown * 100) / 100,
      expectancy: Math.round(expectancy * 100) / 100,
      totalPnL: Math.round(totalPnL * 100) / 100,
      avgWin: Math.round(avgWin * 100) / 100,
      avgLoss: Math.round(avgLoss * 100) / 100
    };
  }

  /**
   * Calculate maximum drawdown
   */
  calculateMaxDrawdown(trades) {
    if (trades.length === 0) return 0;

    let runningValue = 100;
    let peak = runningValue;
    let maxDrawdown = 0;

    for (const trade of trades) {
      runningValue *= (1 + (trade.pnlPercent || 0) / 100);

      if (runningValue > peak) {
        peak = runningValue;
      } else {
        const drawdown = ((peak - runningValue) / peak) * 100;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  /**
   * Extract key metrics for comparison
   */
  extractKeyMetrics(performanceData) {
    const metrics = performanceData.metrics;
    return {
      winRate: metrics.winRate,
      profitFactor: metrics.profitFactor,
      sharpeRatio: metrics.sharpeRatio,
      maxDrawdown: metrics.maxDrawdown,
      expectancy: metrics.expectancy
    };
  }

  /**
   * Build optimization prompt for AI
   */
  buildOptimizationPrompt(performanceData) {
    return {
      type: 'optimization_analysis',
      data: {
        performance: performanceData.metrics,
        trades: performanceData.totalTrades,
        period: performanceData.period,
        symbols: performanceData.symbols,
        timeframes: performanceData.timeframes,
        baseline: this.performanceBaseline?.metrics
      },
      request: 'Analyze performance and suggest optimizations for parameters and strategy'
    };
  }

  /**
   * Generate fallback analysis when AI is unavailable
   */
  generateFallbackAnalysis(performanceData) {
    const metrics = performanceData.metrics;
    const recommendations = [];

    // Rule-based recommendations
    if (metrics.winRate < 50) {
      recommendations.push({
        type: 'parameter_adjustment',
        description: 'Improve entry criteria to increase win rate',
        priority: 8,
        riskLevel: 'medium'
      });
    }

    if (metrics.profitFactor < 1.5) {
      recommendations.push({
        type: 'risk_reward_adjustment',
        description: 'Optimize risk-reward ratio',
        priority: 7,
        riskLevel: 'low'
      });
    }

    if (metrics.maxDrawdown > 15) {
      recommendations.push({
        type: 'risk_management',
        description: 'Implement stricter risk management',
        priority: 9,
        riskLevel: 'low'
      });
    }

    return {
      source: 'fallback',
      confidence: 60,
      insights: ['Rule-based analysis due to AI unavailability'],
      recommendations: recommendations,
      parameterSuggestions: this.generateFallbackParameterSuggestions(metrics),
      strategySuggestions: {},
      riskAssessment: { level: 'medium' },
      expectedImprovement: 0.05,
      implementationPriority: 'low'
    };
  }

  /**
   * Generate fallback parameter suggestions
   */
  generateFallbackParameterSuggestions(metrics) {
    const suggestions = {};

    if (metrics.winRate < 50) {
      suggestions.entryFilters = {
        strengthThreshold: 0.1, // Increase by 10%
        volumeMultiplier: 0.1   // Increase by 10%
      };
    }

    if (metrics.profitFactor < 1.5) {
      suggestions.riskReward = {
        takeProfitMultiplier: 0.1, // Increase TP
        stopLossMultiplier: -0.05  // Tighten SL slightly
      };
    }

    return suggestions;
  }

  /**
   * Check if optimization is in cooldown period
   */
  isInCooldown() {
    if (!this.lastOptimizationTime) return false;

    const timeSinceLastOptimization = Date.now() - this.lastOptimizationTime.getTime();
    return timeSinceLastOptimization < this.config.safety.cooldownPeriod;
  }

  /**
   * Enter cooldown period
   */
  enterCooldown() {
    this.consecutiveOptimizations = 0;
    this.lastOptimizationTime = new Date();
    logger.info('Entered optimization cooldown period', {
      duration: this.config.safety.cooldownPeriod / (60 * 60 * 1000) + ' hours'
    });
  }

  /**
   * Schedule next optimization
   */
  scheduleOptimization() {
    if (!this.isOptimizing) return;

    this.optimizationTimer = setTimeout(async () => {
      try {
        await this.runOptimization({ type: 'scheduled' });
      } catch (error) {
        logger.error('Scheduled optimization failed', { error: error.message });
      } finally {
        this.scheduleOptimization(); // Schedule next optimization
      }
    }, this.config.optimization.optimizationInterval);
  }

  /**
   * Start performance monitoring
   */
  startPerformanceMonitoring() {
    this.performanceMonitorTimer = setInterval(async () => {
      try {
        await this.monitorPerformance();
      } catch (error) {
        logger.error('Performance monitoring failed', { error: error.message });
      }
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Monitor performance for rollback conditions
   */
  async monitorPerformance() {
    try {
      if (!this.performanceBaseline) return;

      const currentData = await this.gatherPerformanceData(7); // Last 7 days
      this.currentPerformance = this.extractKeyMetrics(currentData);

      // Check for performance degradation
      const performanceChange = this.calculatePerformanceChange(
        this.performanceBaseline.metrics,
        this.currentPerformance
      );

      if (performanceChange < this.config.safety.rollbackThreshold) {
        logger.warn('Performance degradation detected, considering rollback', {
          change: performanceChange,
          threshold: this.config.safety.rollbackThreshold
        });

        if (this.config.safety.enableRollback && this.rollbackStack.length > 0) {
          await this.performRollback();
        }
      }

    } catch (error) {
      logger.error('Error monitoring performance', { error: error.message });
    }
  }

  /**
   * Calculate performance change percentage
   */
  calculatePerformanceChange(baseline, current) {
    const baselineScore = this.calculatePerformanceScore(baseline);
    const currentScore = this.calculatePerformanceScore(current);

    return (currentScore - baselineScore) / baselineScore;
  }

  /**
   * Calculate composite performance score
   */
  calculatePerformanceScore(metrics) {
    return (
      metrics.winRate * 0.3 +
      metrics.profitFactor * 20 +
      metrics.sharpeRatio * 30 +
      (100 - metrics.maxDrawdown) * 0.2
    );
  }

  /**
   * Perform rollback to previous configuration
   */
  async performRollback() {
    try {
      if (this.rollbackStack.length === 0) {
        logger.warn('No rollback configuration available');
        return;
      }

      const rollbackConfig = this.rollbackStack.pop();

      // Implement rollback
      await this.implementRollback(rollbackConfig);

      logger.info('Rollback performed successfully', {
        rollbackId: rollbackConfig.id,
        timestamp: rollbackConfig.timestamp
      });

      this.emit('rollback_performed', rollbackConfig);

    } catch (error) {
      logger.error('Error performing rollback', { error: error.message });
      throw error;
    }
  }

  /**
   * Add configuration to rollback stack
   */
  addToRollbackStack(optimizationRecord) {
    this.rollbackStack.push({
      id: optimizationRecord.id,
      timestamp: optimizationRecord.timestamp,
      previousParameters: optimizationRecord.parameterResults?.currentParameters,
      previousStrategy: optimizationRecord.strategyResults?.currentStrategy
    });

    // Keep only last 5 rollback points
    if (this.rollbackStack.length > 5) {
      this.rollbackStack.shift();
    }
  }

  /**
   * Generate unique optimization ID
   */
  generateOptimizationId() {
    return `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get optimization status
   */
  getOptimizationStatus() {
    return {
      isOptimizing: this.isOptimizing,
      lastOptimizationTime: this.lastOptimizationTime,
      consecutiveOptimizations: this.consecutiveOptimizations,
      isInCooldown: this.isInCooldown(),
      optimizationHistory: this.optimizationHistory.slice(-10), // Last 10
      performanceBaseline: this.performanceBaseline,
      currentPerformance: this.currentPerformance,
      rollbackStackSize: this.rollbackStack.length
    };
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory(limit = 50) {
    return this.optimizationHistory.slice(-limit);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    logger.info('Optimization engine configuration updated');
  }

  /**
   * Get current trading parameters
   */
  async getCurrentParameters() {
    try {
      // This would typically fetch from configuration or database
      return {
        riskManagement: {
          stopLossPercent: 0.5,
          takeProfitMultiplier: 2.0,
          maxRiskPerTrade: 1.0
        },
        entryFilters: {
          strengthThreshold: 0.7,
          volumeMultiplier: 1.5,
          confirmationRequired: true
        },
        exitConditions: {
          trailingStopPercent: 0.3,
          breakEvenThreshold: 1.0,
          partialExitLevels: [1.5, 2.0]
        }
      };
    } catch (error) {
      logger.error('Error getting current parameters', { error: error.message });
      return {};
    }
  }

  /**
   * Get current trading strategy
   */
  async getCurrentStrategy() {
    try {
      return {
        name: 'enhanced_scalping',
        version: '1.0',
        indicators: {
          ema: { fast: 50, slow: 200 },
          macd: { fast: 12, slow: 26, signal: 9 },
          rsi: { period: 14, buyZone: [50, 70], sellZone: [30, 50] }
        },
        entryConditions: {
          trendAlignment: true,
          momentumConfirmation: true,
          volumeConfirmation: true
        },
        exitConditions: {
          oppositeSignal: true,
          profitTarget: true,
          stopLoss: true
        }
      };
    } catch (error) {
      logger.error('Error getting current strategy', { error: error.message });
      return {};
    }
  }

  /**
   * Implement parameter changes
   */
  async implementParameterChanges(parameterResults) {
    try {
      // This would typically update configuration files or database
      logger.info('Implementing parameter changes', {
        improvement: parameterResults.improvement,
        confidence: parameterResults.confidence
      });

      // Simulate parameter implementation
      await new Promise(resolve => setTimeout(resolve, 100));

      return { success: true, timestamp: new Date() };
    } catch (error) {
      logger.error('Error implementing parameter changes', { error: error.message });
      throw error;
    }
  }

  /**
   * Implement strategy changes
   */
  async implementStrategyChanges(strategyResults) {
    try {
      logger.info('Implementing strategy changes', {
        improvement: strategyResults.improvement,
        confidence: strategyResults.confidence
      });

      // Simulate strategy implementation
      await new Promise(resolve => setTimeout(resolve, 100));

      return { success: true, timestamp: new Date() };
    } catch (error) {
      logger.error('Error implementing strategy changes', { error: error.message });
      throw error;
    }
  }

  /**
   * Implement rollback configuration
   */
  async implementRollback(rollbackConfig) {
    try {
      logger.info('Implementing rollback', {
        rollbackId: rollbackConfig.id,
        timestamp: rollbackConfig.timestamp
      });

      // Restore previous parameters
      if (rollbackConfig.previousParameters) {
        await this.implementParameterChanges({
          parameters: rollbackConfig.previousParameters,
          improvement: 0,
          confidence: 100
        });
      }

      // Restore previous strategy
      if (rollbackConfig.previousStrategy) {
        await this.implementStrategyChanges({
          strategy: rollbackConfig.previousStrategy,
          improvement: 0,
          confidence: 100
        });
      }

      return { success: true, timestamp: new Date() };
    } catch (error) {
      logger.error('Error implementing rollback', { error: error.message });
      throw error;
    }
  }

  /**
   * Analyze symbol performance
   */
  analyzeSymbolPerformance(trades) {
    const symbolStats = {};

    trades.forEach(trade => {
      const symbol = trade.symbol;
      if (!symbolStats[symbol]) {
        symbolStats[symbol] = {
          trades: 0,
          wins: 0,
          totalPnL: 0
        };
      }

      symbolStats[symbol].trades++;
      if ((trade.pnlPercent || 0) > 0) {
        symbolStats[symbol].wins++;
      }
      symbolStats[symbol].totalPnL += (trade.pnlPercent || 0);
    });

    // Calculate win rates and sort by performance
    const symbolPerformance = Object.entries(symbolStats).map(([symbol, stats]) => ({
      symbol,
      trades: stats.trades,
      winRate: (stats.wins / stats.trades) * 100,
      totalPnL: stats.totalPnL,
      avgPnL: stats.totalPnL / stats.trades
    })).sort((a, b) => b.avgPnL - a.avgPnL);

    return symbolPerformance;
  }

  /**
   * Analyze timeframe performance
   */
  analyzeTimeframePerformance(trades) {
    const timeframeStats = {};

    trades.forEach(trade => {
      const timeframe = trade.timeframe || '1h';
      if (!timeframeStats[timeframe]) {
        timeframeStats[timeframe] = {
          trades: 0,
          wins: 0,
          totalPnL: 0
        };
      }

      timeframeStats[timeframe].trades++;
      if ((trade.pnlPercent || 0) > 0) {
        timeframeStats[timeframe].wins++;
      }
      timeframeStats[timeframe].totalPnL += (trade.pnlPercent || 0);
    });

    // Calculate performance metrics
    const timeframePerformance = Object.entries(timeframeStats).map(([timeframe, stats]) => ({
      timeframe,
      trades: stats.trades,
      winRate: (stats.wins / stats.trades) * 100,
      totalPnL: stats.totalPnL,
      avgPnL: stats.totalPnL / stats.trades
    })).sort((a, b) => b.avgPnL - a.avgPnL);

    return timeframePerformance;
  }

  /**
   * Analyze trading patterns
   */
  analyzePatterns(trades) {
    const patterns = {
      hourlyDistribution: {},
      dayOfWeekDistribution: {},
      consecutiveWins: 0,
      consecutiveLosses: 0,
      maxConsecutiveWins: 0,
      maxConsecutiveLosses: 0
    };

    let currentWinStreak = 0;
    let currentLossStreak = 0;

    trades.forEach(trade => {
      const date = new Date(trade.createdAt);
      const hour = date.getUTCHours();
      const dayOfWeek = date.getDay();

      // Hourly distribution
      if (!patterns.hourlyDistribution[hour]) {
        patterns.hourlyDistribution[hour] = { trades: 0, wins: 0, totalPnL: 0 };
      }
      patterns.hourlyDistribution[hour].trades++;
      patterns.hourlyDistribution[hour].totalPnL += (trade.pnlPercent || 0);

      // Day of week distribution
      if (!patterns.dayOfWeekDistribution[dayOfWeek]) {
        patterns.dayOfWeekDistribution[dayOfWeek] = { trades: 0, wins: 0, totalPnL: 0 };
      }
      patterns.dayOfWeekDistribution[dayOfWeek].trades++;
      patterns.dayOfWeekDistribution[dayOfWeek].totalPnL += (trade.pnlPercent || 0);

      // Win/loss streaks
      if ((trade.pnlPercent || 0) > 0) {
        patterns.hourlyDistribution[hour].wins++;
        patterns.dayOfWeekDistribution[dayOfWeek].wins++;

        currentWinStreak++;
        currentLossStreak = 0;
        patterns.maxConsecutiveWins = Math.max(patterns.maxConsecutiveWins, currentWinStreak);
      } else if ((trade.pnlPercent || 0) < 0) {
        currentLossStreak++;
        currentWinStreak = 0;
        patterns.maxConsecutiveLosses = Math.max(patterns.maxConsecutiveLosses, currentLossStreak);
      }
    });

    patterns.consecutiveWins = currentWinStreak;
    patterns.consecutiveLosses = currentLossStreak;

    return patterns;
  }
}

module.exports = AIOptimizationEngine;
