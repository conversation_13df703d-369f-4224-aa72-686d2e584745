const fs = require('fs');
const path = require('path');

// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class ReportGenerator {
  constructor(config = {}) {
    this.config = config;
  }

  /**
   * Generate comprehensive backtest report
   */
  async generateReport(analysis, config, results) {
    try {
      const report = {
        metadata: this.generateMetadata(config, results),
        executive_summary: this.generateExecutiveSummary(analysis),
        performance_metrics: this.generatePerformanceSection(analysis),
        risk_analysis: this.generateRiskSection(analysis),
        trade_analysis: this.generateTradeSection(analysis),
        market_condition_analysis: this.generateMarketConditionSection(analysis),
        time_analysis: this.generateTimeSection(analysis),
        symbol_analysis: this.generateSymbolSection(analysis),
        recommendations: this.generateRecommendations(analysis),
        detailed_trades: this.generateTradeDetails(results)
      };

      return report;

    } catch (error) {
      logger.logError('Error generating report:', error.message);
      throw error;
    }
  }

  /**
   * Generate report metadata
   */
  generateMetadata(config, results) {
    return {
      generated_at: new Date().toISOString(),
      backtest_period: {
        start: config.startDate,
        end: config.endDate,
        duration_days: this.calculateDurationDays(config.startDate, config.endDate)
      },
      configuration: {
        symbols: config.symbols,
        timeframes: config.timeframes,
        initial_capital: config.initialCapital,
        position_size_percent: config.positionSizePercent,
        max_concurrent_positions: config.maxConcurrentPositions,
        market_condition_filter: config.useMarketConditionFilter,
        slippage: config.slippage,
        commission: config.commission
      },
      execution_stats: {
        total_signals: results?.signals || 0,
        total_trades: results?.trades || 0,
        execution_time_seconds: results?.executionTime ? (results.executionTime / 1000).toFixed(2) : '0',
        data_quality: results?.historicalData ? this.assessDataQuality(results.historicalData) : { total_symbols: 0, total_candles: 0, quality: 'Unknown' }
      }
    };
  }

  /**
   * Generate executive summary
   */
  generateExecutiveSummary(analysis) {
    const performance_grade = this.calculatePerformanceGrade(analysis);
    const key_strengths = this.identifyKeyStrengths(analysis);
    const key_weaknesses = this.identifyKeyWeaknesses(analysis);
    const overall_assessment = this.generateOverallAssessment(analysis);

    return {
      performance_grade,
      key_metrics: {
        total_return: `${analysis.totalReturn?.toFixed(2) || 'N/A'}%`,
        win_rate: `${analysis.winRate?.toFixed(2) || 'N/A'}%`,
        profit_factor: analysis.profitFactor?.toFixed(2) || 'N/A',
        max_drawdown: `${analysis.maxDrawdown?.toFixed(2) || 'N/A'}%`,
        sharpe_ratio: analysis.sharpeRatio?.toFixed(2) || 'N/A'
      },
      key_strengths,
      key_weaknesses,
      overall_assessment
    };
  }

  /**
   * Generate performance metrics section
   */
  generatePerformanceSection(analysis) {
    return {
      profitability: {
        total_return: analysis.totalReturn,
        total_pnl: analysis.totalPnL,
        annualized_return: this.calculateAnnualizedReturn(analysis),
        monthly_return: this.calculateMonthlyReturn(analysis),
        cagr: this.calculateCAGR(analysis)
      },
      trading_statistics: {
        total_trades: analysis.totalTrades,
        winning_trades: analysis.winningTrades,
        losing_trades: analysis.losingTrades,
        win_rate: analysis.winRate,
        profit_factor: analysis.profitFactor,
        expectancy: analysis.expectancy,
        kelly_criterion: analysis.kelly_criterion
      },
      trade_metrics: {
        average_win: analysis.avgWin,
        average_loss: analysis.avgLoss,
        largest_win: analysis.largestWin,
        largest_loss: analysis.largestLoss,
        average_trade: analysis.avgTrade,
        average_trade_percent: analysis.avgTradePercent
      }
    };
  }

  /**
   * Generate risk analysis section
   */
  generateRiskSection(analysis) {
    return {
      drawdown_analysis: {
        maximum_drawdown: analysis.maxDrawdown,
        recovery_factor: analysis.recovery_factor,
        consecutive_losses: analysis.consecutive_losses,
        consecutive_wins: analysis.consecutive_wins
      },
      risk_ratios: {
        sharpe_ratio: analysis.sharpeRatio,
        sortino_ratio: analysis.sortinoRatio,
        calmar_ratio: analysis.calmarRatio,
        information_ratio: analysis.information_ratio,
        treynor_ratio: analysis.treynor_ratio
      },
      risk_metrics: {
        volatility: analysis.volatility,
        downside_deviation: analysis.downside_deviation,
        var_95: analysis.var95,
        var_99: analysis.var99
      },
      risk_assessment: this.assessRiskLevel(analysis)
    };
  }

  /**
   * Generate trade analysis section
   */
  generateTradeSection(analysis) {
    return {
      distribution_analysis: analysis.trade_distribution,
      timing_analysis: {
        average_trade_duration_minutes: analysis.avg_trade_duration,
        best_trading_hour: analysis.best_trading_hour,
        best_trading_day: analysis.best_trading_day
      },
      performance_consistency: this.analyzePerformanceConsistency(analysis)
    };
  }

  /**
   * Generate market condition analysis section
   */
  generateMarketConditionSection(analysis) {
    if (!analysis.marketConditionPerformance) {
      return { message: 'Market condition analysis not available' };
    }

    const conditions = analysis.marketConditionPerformance;
    const best_condition = this.findBestMarketCondition(conditions);
    const worst_condition = this.findWorstMarketCondition(conditions);

    return {
      performance_by_condition: conditions,
      best_performing_condition: best_condition,
      worst_performing_condition: worst_condition,
      condition_insights: this.generateConditionInsights(conditions)
    };
  }

  /**
   * Generate time-based analysis section
   */
  generateTimeSection(analysis) {
    return {
      monthly_performance: analysis.monthlyPerformance,
      weekly_performance: analysis.weeklyPerformance,
      seasonal_patterns: this.identifySeasonalPatterns(analysis),
      time_based_insights: this.generateTimeInsights(analysis)
    };
  }

  /**
   * Generate symbol analysis section
   */
  generateSymbolSection(analysis) {
    if (!analysis.symbolPerformance) {
      return { message: 'Symbol analysis not available' };
    }

    const symbols = analysis.symbolPerformance;
    const best_symbol = this.findBestSymbol(symbols);
    const worst_symbol = this.findWorstSymbol(symbols);

    return {
      performance_by_symbol: symbols,
      best_performing_symbol: best_symbol,
      worst_performing_symbol: worst_symbol,
      symbol_insights: this.generateSymbolInsights(symbols)
    };
  }

  /**
   * Generate recommendations
   */
  generateRecommendations(analysis) {
    const recommendations = [];

    // Performance recommendations
    if (analysis.totalReturn < 0) {
      recommendations.push({
        category: 'Performance',
        priority: 'High',
        recommendation: 'Strategy is currently unprofitable. Consider revising entry/exit criteria or risk management parameters.',
        impact: 'Critical'
      });
    }

    // Win rate recommendations
    if (analysis.winRate < 50) {
      recommendations.push({
        category: 'Win Rate',
        priority: 'High',
        recommendation: 'Low win rate detected. Consider tightening entry conditions or improving signal quality.',
        impact: 'High'
      });
    }

    // Risk recommendations
    if (analysis.maxDrawdown > 20) {
      recommendations.push({
        category: 'Risk Management',
        priority: 'High',
        recommendation: 'High maximum drawdown detected. Consider reducing position sizes or implementing stricter stop losses.',
        impact: 'High'
      });
    }

    // Profit factor recommendations
    if (analysis.profitFactor < 1.5) {
      recommendations.push({
        category: 'Profitability',
        priority: 'Medium',
        recommendation: 'Low profit factor. Consider optimizing take profit levels or reducing average loss per trade.',
        impact: 'Medium'
      });
    }

    // Market condition recommendations
    if (analysis.marketConditionPerformance) {
      const worst_condition = this.findWorstMarketCondition(analysis.marketConditionPerformance);
      if (worst_condition && worst_condition.winRate < 40) {
        recommendations.push({
          category: 'Market Conditions',
          priority: 'Medium',
          recommendation: `Poor performance in ${worst_condition.condition} market conditions. Consider avoiding trading during these periods.`,
          impact: 'Medium'
        });
      }
    }

    // Diversification recommendations
    if (analysis.symbolPerformance) {
      const symbols = Object.keys(analysis.symbolPerformance);
      if (symbols.length < 3) {
        recommendations.push({
          category: 'Diversification',
          priority: 'Low',
          recommendation: 'Limited symbol diversification. Consider adding more symbols to reduce concentration risk.',
          impact: 'Low'
        });
      }
    }

    return {
      total_recommendations: recommendations.length,
      recommendations,
      priority_summary: this.summarizePriorities(recommendations)
    };
  }

  /**
   * Generate detailed trade information
   */
  generateTradeDetails(results) {
    // Return summary instead of full trade details to keep report manageable
    return {
      total_trades: results.trades,
      trade_summary: 'Detailed trade data available in separate export',
      note: 'Full trade details can be exported separately for detailed analysis'
    };
  }

  /**
   * Calculate performance grade
   */
  calculatePerformanceGrade(analysis) {
    let score = 0;
    let maxScore = 0;

    // Total return (30 points)
    maxScore += 30;
    if (analysis.totalReturn > 20) score += 30;
    else if (analysis.totalReturn > 10) score += 20;
    else if (analysis.totalReturn > 0) score += 10;

    // Win rate (25 points)
    maxScore += 25;
    if (analysis.winRate > 70) score += 25;
    else if (analysis.winRate > 60) score += 20;
    else if (analysis.winRate > 50) score += 15;
    else if (analysis.winRate > 40) score += 10;

    // Profit factor (20 points)
    maxScore += 20;
    if (analysis.profitFactor > 2.0) score += 20;
    else if (analysis.profitFactor > 1.5) score += 15;
    else if (analysis.profitFactor > 1.0) score += 10;

    // Max drawdown (15 points)
    maxScore += 15;
    if (analysis.maxDrawdown < 5) score += 15;
    else if (analysis.maxDrawdown < 10) score += 12;
    else if (analysis.maxDrawdown < 20) score += 8;
    else if (analysis.maxDrawdown < 30) score += 4;

    // Sharpe ratio (10 points)
    maxScore += 10;
    if (analysis.sharpeRatio > 2.0) score += 10;
    else if (analysis.sharpeRatio > 1.0) score += 7;
    else if (analysis.sharpeRatio > 0.5) score += 4;

    const percentage = (score / maxScore) * 100;

    let grade;
    if (percentage >= 90) grade = 'A+';
    else if (percentage >= 85) grade = 'A';
    else if (percentage >= 80) grade = 'A-';
    else if (percentage >= 75) grade = 'B+';
    else if (percentage >= 70) grade = 'B';
    else if (percentage >= 65) grade = 'B-';
    else if (percentage >= 60) grade = 'C+';
    else if (percentage >= 55) grade = 'C';
    else if (percentage >= 50) grade = 'C-';
    else if (percentage >= 40) grade = 'D';
    else grade = 'F';

    return {
      grade,
      score: percentage.toFixed(1),
      breakdown: {
        total_return: analysis.totalReturn > 20 ? 'Excellent' : analysis.totalReturn > 10 ? 'Good' : analysis.totalReturn > 0 ? 'Fair' : 'Poor',
        win_rate: analysis.winRate > 70 ? 'Excellent' : analysis.winRate > 60 ? 'Good' : analysis.winRate > 50 ? 'Fair' : 'Poor',
        profit_factor: analysis.profitFactor > 2.0 ? 'Excellent' : analysis.profitFactor > 1.5 ? 'Good' : analysis.profitFactor > 1.0 ? 'Fair' : 'Poor',
        max_drawdown: analysis.maxDrawdown < 5 ? 'Excellent' : analysis.maxDrawdown < 10 ? 'Good' : analysis.maxDrawdown < 20 ? 'Fair' : 'Poor',
        sharpe_ratio: analysis.sharpeRatio > 2.0 ? 'Excellent' : analysis.sharpeRatio > 1.0 ? 'Good' : analysis.sharpeRatio > 0.5 ? 'Fair' : 'Poor'
      }
    };
  }

  /**
   * Identify key strengths
   */
  identifyKeyStrengths(analysis) {
    const strengths = [];

    if (analysis.totalReturn > 15) strengths.push('High profitability');
    if (analysis.winRate > 65) strengths.push('High win rate');
    if (analysis.profitFactor > 2.0) strengths.push('Excellent profit factor');
    if (analysis.maxDrawdown < 10) strengths.push('Low drawdown');
    if (analysis.sharpeRatio > 1.5) strengths.push('Good risk-adjusted returns');
    if (analysis.consecutive_losses < 5) strengths.push('Good loss control');

    return strengths.length > 0 ? strengths : ['Strategy shows potential for optimization'];
  }

  /**
   * Identify key weaknesses
   */
  identifyKeyWeaknesses(analysis) {
    const weaknesses = [];

    if (analysis.totalReturn < 0) weaknesses.push('Negative returns');
    if (analysis.winRate < 50) weaknesses.push('Low win rate');
    if (analysis.profitFactor < 1.2) weaknesses.push('Poor profit factor');
    if (analysis.maxDrawdown > 20) weaknesses.push('High drawdown');
    if (analysis.sharpeRatio < 0.5) weaknesses.push('Poor risk-adjusted returns');
    if (analysis.consecutive_losses > 8) weaknesses.push('Poor loss streaks');

    return weaknesses.length > 0 ? weaknesses : ['No major weaknesses identified'];
  }

  /**
   * Generate overall assessment
   */
  generateOverallAssessment(analysis) {
    if (analysis.totalReturn > 15 && analysis.winRate > 60 && analysis.maxDrawdown < 15) {
      return 'Excellent strategy with strong performance across all key metrics. Ready for live trading with proper risk management.';
    } else if (analysis.totalReturn > 5 && analysis.winRate > 50 && analysis.maxDrawdown < 25) {
      return 'Good strategy with solid performance. Consider minor optimizations before live trading.';
    } else if (analysis.totalReturn > 0 && analysis.winRate > 45) {
      return 'Moderate strategy performance. Requires optimization before live trading.';
    } else {
      return 'Strategy needs significant improvement. Not recommended for live trading without major revisions.';
    }
  }

  /**
   * Helper methods for calculations
   */
  calculateDurationDays(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  }

  calculateAnnualizedReturn(analysis) {
    // Simplified calculation
    return analysis.totalReturn * (365 / 90); // Assuming 90-day backtest
  }

  calculateMonthlyReturn(analysis) {
    return analysis.totalReturn / 3; // Assuming 3-month backtest
  }

  calculateCAGR(analysis) {
    return this.calculateAnnualizedReturn(analysis);
  }

  assessDataQuality(historicalData) {
    // Simplified data quality assessment
    let totalCandles = 0;
    let totalSymbols = 0;

    for (const symbol of Object.keys(historicalData)) {
      totalSymbols++;
      for (const timeframe of Object.keys(historicalData[symbol])) {
        totalCandles += historicalData[symbol][timeframe].candleCount || 0;
      }
    }

    return {
      total_symbols: totalSymbols,
      total_candles: totalCandles,
      quality: totalCandles > 10000 ? 'High' : totalCandles > 5000 ? 'Medium' : 'Low'
    };
  }

  assessRiskLevel(analysis) {
    if (analysis.maxDrawdown > 25 || analysis.sharpeRatio < 0.5) {
      return 'High Risk';
    } else if (analysis.maxDrawdown > 15 || analysis.sharpeRatio < 1.0) {
      return 'Medium Risk';
    } else {
      return 'Low Risk';
    }
  }

  analyzePerformanceConsistency(analysis) {
    return {
      volatility_assessment: analysis.volatility > 5 ? 'High' : analysis.volatility > 2 ? 'Medium' : 'Low',
      consistency_score: analysis.sharpeRatio > 1.5 ? 'Consistent' : analysis.sharpeRatio > 0.8 ? 'Moderate' : 'Inconsistent'
    };
  }

  findBestMarketCondition(conditions) {
    let best = null;
    let bestReturn = -Infinity;

    for (const [condition, data] of Object.entries(conditions)) {
      if (data.totalPnL > bestReturn) {
        bestReturn = data.totalPnL;
        best = { condition, ...data };
      }
    }

    return best;
  }

  findWorstMarketCondition(conditions) {
    let worst = null;
    let worstReturn = Infinity;

    for (const [condition, data] of Object.entries(conditions)) {
      if (data.totalPnL < worstReturn) {
        worstReturn = data.totalPnL;
        worst = { condition, ...data };
      }
    }

    return worst;
  }

  generateConditionInsights(conditions) {
    const insights = [];

    for (const [condition, data] of Object.entries(conditions)) {
      if (data.winRate > 70) {
        insights.push(`${condition} market shows excellent performance with ${data.winRate.toFixed(1)}% win rate`);
      } else if (data.winRate < 40) {
        insights.push(`${condition} market shows poor performance with ${data.winRate.toFixed(1)}% win rate`);
      }
    }

    return insights;
  }

  identifySeasonalPatterns(analysis) {
    // Simplified seasonal analysis
    return {
      note: 'Seasonal pattern analysis requires longer historical data',
      recommendation: 'Extend backtest period to identify seasonal trends'
    };
  }

  generateTimeInsights(analysis) {
    const insights = [];

    if (analysis.best_trading_hour) {
      insights.push(`Best trading hour: ${analysis.best_trading_hour.hour}:00 with $${analysis.best_trading_hour.pnl.toFixed(2)} P&L`);
    }

    if (analysis.best_trading_day) {
      insights.push(`Best trading day: ${analysis.best_trading_day.dayName} with $${analysis.best_trading_day.pnl.toFixed(2)} P&L`);
    }

    return insights;
  }

  findBestSymbol(symbols) {
    let best = null;
    let bestReturn = -Infinity;

    for (const [symbol, data] of Object.entries(symbols)) {
      if (data.totalPnL > bestReturn) {
        bestReturn = data.totalPnL;
        best = { symbol, ...data };
      }
    }

    return best;
  }

  findWorstSymbol(symbols) {
    let worst = null;
    let worstReturn = Infinity;

    for (const [symbol, data] of Object.entries(symbols)) {
      if (data.totalPnL < worstReturn) {
        worstReturn = data.totalPnL;
        worst = { symbol, ...data };
      }
    }

    return worst;
  }

  generateSymbolInsights(symbols) {
    const insights = [];

    for (const [symbol, data] of Object.entries(symbols)) {
      if (data.winRate > 70) {
        insights.push(`${symbol} shows excellent performance with ${data.winRate.toFixed(1)}% win rate`);
      } else if (data.winRate < 40) {
        insights.push(`${symbol} shows poor performance with ${data.winRate.toFixed(1)}% win rate - consider removing`);
      }
    }

    return insights;
  }

  summarizePriorities(recommendations) {
    const priorities = { High: 0, Medium: 0, Low: 0 };

    recommendations.forEach(rec => {
      priorities[rec.priority]++;
    });

    return priorities;
  }

  /**
   * Export report to file
   */
  async exportReport(report, format = 'json', filename = null) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const defaultFilename = `backtest-report-${timestamp}`;
      const finalFilename = filename || defaultFilename;

      if (format === 'json') {
        const filepath = `./data/${finalFilename}.json`;
        fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
        logger.logInfo(`Report exported to: ${filepath}`);
        return filepath;
      }

      // Add other formats (HTML, PDF) in future
      throw new Error(`Format ${format} not supported yet`);

    } catch (error) {
      logger.logError('Error exporting report:', error.message);
      throw error;
    }
  }
}

module.exports = ReportGenerator;