const fs = require('fs');
const path = require('path');
const axios = require('axios');
const zlib = require('zlib');

// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class DataManager {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data');
    this.rawDir = path.join(this.dataDir, 'raw');
    this.processedDir = path.join(this.dataDir, 'processed');
    this.cacheDir = path.join(this.dataDir, 'cache');

    this.ensureDirectories();
  }

  /**
   * Ensure data directories exist
   */
  ensureDirectories() {
    [this.dataDir, this.rawDir, this.processedDir, this.cacheDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  /**
   * Download historical data from Binance
   */
  async downloadHistoricalData(symbol, interval, startTime, endTime, limit = 1000) {
    try {
      const url = 'https://fapi.binance.com/fapi/v1/klines';
      const params = {
        symbol,
        interval,
        startTime,
        endTime,
        limit
      };

      logger.logInfo(`Downloading ${symbol} ${interval} data from ${new Date(startTime)} to ${new Date(endTime)}`);

      const response = await axios.get(url, { params });

      if (!response.data || response.data.length === 0) {
        logger.logInfo(`No data received for ${symbol} ${interval}`);
        return [];
      }

      // Convert to standard format
      const candles = response.data.map(candle => ({
        openTime: parseInt(candle[0]),
        open: parseFloat(candle[1]),
        high: parseFloat(candle[2]),
        low: parseFloat(candle[3]),
        close: parseFloat(candle[4]),
        volume: parseFloat(candle[5]),
        closeTime: parseInt(candle[6]),
        quoteVolume: parseFloat(candle[7]),
        trades: parseInt(candle[8]),
        buyBaseVolume: parseFloat(candle[9]),
        buyQuoteVolume: parseFloat(candle[10])
      }));

      logger.logInfo(`Downloaded ${candles.length} candles for ${symbol} ${interval}`);
      return candles;

    } catch (error) {
      logger.logError(`Error downloading data for ${symbol} ${interval}:`, error.message);

      // Rate limiting handling
      if (error.response?.status === 429 || error.response?.status === 418) {
        logger.logInfo('Rate limited, waiting 60 seconds...');
        await this.sleep(60000);
        return this.downloadHistoricalData(symbol, interval, startTime, endTime, limit);
      }

      throw error;
    }
  }

  /**
   * Download data for multiple time periods (chunked)
   */
  async downloadDataRange(symbol, interval, startDate, endDate) {
    const startTime = new Date(startDate).getTime();
    const endTime = new Date(endDate).getTime();

    // Calculate interval in milliseconds
    const intervalMs = this.getIntervalMs(interval);
    const maxCandlesPerRequest = 1000;
    const chunkSize = intervalMs * maxCandlesPerRequest;

    let allCandles = [];
    let currentStart = startTime;

    while (currentStart < endTime) {
      const currentEnd = Math.min(currentStart + chunkSize, endTime);

      try {
        const candles = await this.downloadHistoricalData(
          symbol,
          interval,
          currentStart,
          currentEnd
        );

        if (candles.length > 0) {
          allCandles = allCandles.concat(candles);
          currentStart = candles[candles.length - 1].closeTime + 1;
        } else {
          currentStart = currentEnd;
        }

        // Rate limiting delay
        await this.sleep(100);

      } catch (error) {
        logger.logError(`Error downloading chunk for ${symbol}:`, error.message);
        currentStart = currentEnd;
      }
    }

    // Remove duplicates and sort
    allCandles = this.removeDuplicates(allCandles);
    allCandles.sort((a, b) => a.openTime - b.openTime);

    logger.logInfo(`Downloaded total ${allCandles.length} candles for ${symbol} ${interval}`);
    return allCandles;
  }

  /**
   * Save data to compressed file
   */
  async saveData(symbol, interval, data, type = 'raw') {
    try {
      const dir = type === 'raw' ? this.rawDir : this.processedDir;
      const filename = `${symbol}_${interval}_${type}.json.gz`;
      const filepath = path.join(dir, filename);

      const jsonData = JSON.stringify(data, null, 2);
      const compressed = zlib.gzipSync(jsonData);

      fs.writeFileSync(filepath, compressed);

      logger.logInfo(`Saved ${data.length} records to ${filename} (${(compressed.length / 1024).toFixed(2)}KB)`);
      return filepath;

    } catch (error) {
      logger.logError(`Error saving data for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Load data from compressed file
   */
  async loadData(symbol, interval, type = 'raw') {
    try {
      const dir = type === 'raw' ? this.rawDir : this.processedDir;
      const filename = `${symbol}_${interval}_${type}.json.gz`;
      const filepath = path.join(dir, filename);

      if (!fs.existsSync(filepath)) {
        logger.logInfo(`Data file not found: ${filename}`);
        return null;
      }

      const compressed = fs.readFileSync(filepath);
      const jsonData = zlib.gunzipSync(compressed).toString();
      const data = JSON.parse(jsonData);

      logger.logInfo(`Loaded ${data.length} records from ${filename}`);
      return data;

    } catch (error) {
      logger.logError(`Error loading data for ${symbol}:`, error.message);
      return null;
    }
  }

  /**
   * Check if data exists and is up to date
   */
  hasData(symbol, interval, type = 'raw') {
    const dir = type === 'raw' ? this.rawDir : this.processedDir;
    const filename = `${symbol}_${interval}_${type}.json.gz`;
    const filepath = path.join(dir, filename);

    return fs.existsSync(filepath);
  }

  /**
   * Get data file info
   */
  getDataInfo(symbol, interval, type = 'raw') {
    const dir = type === 'raw' ? this.rawDir : this.processedDir;
    const filename = `${symbol}_${interval}_${type}.json.gz`;
    const filepath = path.join(dir, filename);

    if (!fs.existsSync(filepath)) {
      return null;
    }

    const stats = fs.statSync(filepath);
    return {
      filename,
      size: stats.size,
      modified: stats.mtime,
      sizeKB: (stats.size / 1024).toFixed(2)
    };
  }

  /**
   * Validate data quality
   */
  validateData(candles) {
    if (!candles || candles.length === 0) {
      return { valid: false, errors: ['No data'] };
    }

    const errors = [];
    let validCandles = 0;

    for (let i = 0; i < candles.length; i++) {
      const candle = candles[i];

      // Check required fields
      if (!candle.openTime || !candle.open || !candle.high || !candle.low || !candle.close) {
        errors.push(`Missing required fields at index ${i}`);
        continue;
      }

      // Check price logic
      if (candle.high < candle.low) {
        errors.push(`Invalid OHLC at index ${i}: high < low`);
        continue;
      }

      if (candle.high < candle.open || candle.high < candle.close) {
        errors.push(`Invalid OHLC at index ${i}: high < open/close`);
        continue;
      }

      if (candle.low > candle.open || candle.low > candle.close) {
        errors.push(`Invalid OHLC at index ${i}: low > open/close`);
        continue;
      }

      // Check for extreme price movements (>50% in one candle)
      const priceChange = Math.abs(candle.close - candle.open) / candle.open;
      if (priceChange > 0.5) {
        errors.push(`Extreme price movement at index ${i}: ${(priceChange * 100).toFixed(2)}%`);
      }

      validCandles++;
    }

    // Check for time gaps
    for (let i = 1; i < candles.length; i++) {
      const timeDiff = candles[i].openTime - candles[i-1].openTime;
      const expectedDiff = this.getIntervalMs('1h'); // Assuming 1h default

      if (timeDiff > expectedDiff * 2) {
        errors.push(`Time gap detected between index ${i-1} and ${i}: ${timeDiff}ms`);
      }
    }

    const validPercentage = (validCandles / candles.length) * 100;

    return {
      valid: errors.length === 0,
      errors,
      totalCandles: candles.length,
      validCandles,
      validPercentage: validPercentage.toFixed(2),
      dataQuality: validPercentage > 95 ? 'excellent' : validPercentage > 90 ? 'good' : 'poor'
    };
  }

  /**
   * Remove duplicate candles
   */
  removeDuplicates(candles) {
    const seen = new Set();
    return candles.filter(candle => {
      const key = `${candle.openTime}_${candle.closeTime}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Get interval in milliseconds
   */
  getIntervalMs(interval) {
    const intervals = {
      '1m': 60 * 1000,
      '3m': 3 * 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '8h': 8 * 60 * 60 * 1000,
      '12h': 12 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    };

    return intervals[interval] || intervals['1h'];
  }

  /**
   * List available data files
   */
  listAvailableData() {
    const files = [];

    [this.rawDir, this.processedDir].forEach((dir, index) => {
      const type = index === 0 ? 'raw' : 'processed';

      if (fs.existsSync(dir)) {
        const dirFiles = fs.readdirSync(dir);

        dirFiles.forEach(filename => {
          if (filename.endsWith('.json.gz')) {
            const filepath = path.join(dir, filename);
            const stats = fs.statSync(filepath);

            // Parse filename: SYMBOL_INTERVAL_TYPE.json.gz
            const parts = filename.replace('.json.gz', '').split('_');
            if (parts.length >= 2) {
              files.push({
                symbol: parts[0],
                interval: parts[1],
                type,
                filename,
                size: stats.size,
                sizeKB: (stats.size / 1024).toFixed(2),
                modified: stats.mtime
              });
            }
          }
        });
      }
    });

    return files.sort((a, b) => b.modified - a.modified);
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clean old cache files
   */
  cleanCache(maxAgeHours = 24) {
    try {
      if (!fs.existsSync(this.cacheDir)) return;

      const files = fs.readdirSync(this.cacheDir);
      const maxAge = maxAgeHours * 60 * 60 * 1000;
      let cleaned = 0;

      files.forEach(filename => {
        const filepath = path.join(this.cacheDir, filename);
        const stats = fs.statSync(filepath);

        if (Date.now() - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filepath);
          cleaned++;
        }
      });

      if (cleaned > 0) {
        logger.logInfo(`Cleaned ${cleaned} old cache files`);
      }
    } catch (error) {
      logger.logError('Error cleaning cache:', error.message);
    }
  }
}

module.exports = new DataManager();