const DataManager = require('./DataManager');
const SignalSimulator = require('./SignalSimulator');
const OrderSimulator = require('./OrderSimulator');
const PortfolioManager = require('./PortfolioManager');
const RiskAnalyzer = require('./RiskAnalyzer');
const ReportGenerator = require('./ReportGenerator');

// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class BacktestEngine {
  constructor(config = {}) {
    this.config = {
      // Default configuration
      startDate: '2024-01-01',
      endDate: '2024-10-31',
      symbols: ['BTCUSDT', 'ETHUSDT'],
      timeframes: ['1h'],
      initialCapital: 10000,
      positionSizePercent: 2,
      maxConcurrentPositions: 5,
      useMarketConditionFilter: true,
      trailingStopEnabled: true,
      slippage: 0.1,
      commission: 0.1,
      latency: 100,
      maxDrawdown: 20,
      dailyLossLimit: 5,
      ...config
    };

    this.dataManager = DataManager;
    this.signalSimulator = null;
    this.orderSimulator = null;
    this.portfolioManager = null;
    this.riskAnalyzer = null;
    this.reportGenerator = null;

    this.isRunning = false;
    this.progress = 0;
    this.startTime = null;
    this.results = null;
  }

  /**
   * Initialize backtest components
   */
  async initialize() {
    try {
      logger.logInfo('Initializing backtest engine...');

      // Initialize components
      this.signalSimulator = new SignalSimulator(this.config);
      this.orderSimulator = new OrderSimulator(this.config);
      this.portfolioManager = new PortfolioManager(this.config);
      this.riskAnalyzer = new RiskAnalyzer(this.config);
      this.reportGenerator = new ReportGenerator(this.config);

      // Validate configuration
      this.validateConfig();

      // Ensure data availability
      await this.ensureDataAvailability();

      logger.logInfo('Backtest engine initialized successfully');
      return true;

    } catch (error) {
      logger.logError('Error initializing backtest engine:', error.message);
      throw error;
    }
  }

  /**
   * Run complete backtest
   */
  async runBacktest() {
    try {
      if (this.isRunning) {
        throw new Error('Backtest is already running');
      }

      this.isRunning = true;
      this.startTime = Date.now();
      this.progress = 0;

      logger.logInfo('Starting backtest...');
      logger.logInfo(`Period: ${this.config.startDate} to ${this.config.endDate}`);
      logger.logInfo(`Symbols: ${this.config.symbols.join(', ')}`);
      logger.logInfo(`Initial Capital: $${this.config.initialCapital}`);

      // Initialize components
      await this.initialize();

      // Phase 1: Load and prepare data
      logger.logInfo('Phase 1: Loading historical data...');
      const historicalData = await this.loadHistoricalData();
      this.progress = 20;

      // Phase 2: Generate signals
      logger.logInfo('Phase 2: Generating signals...');
      const signals = await this.generateSignals(historicalData);
      this.progress = 40;

      // Phase 3: Simulate trading
      logger.logInfo('Phase 3: Simulating trades...');
      const trades = await this.simulateTrading(signals, historicalData);
      this.progress = 70;

      // Phase 4: Analyze results
      logger.logInfo('Phase 4: Analyzing results...');
      const analysis = await this.analyzeResults(trades);
      this.progress = 90;

      // Phase 5: Generate report
      logger.logInfo('Phase 5: Generating report...');
      const report = await this.generateReport(analysis, signals, trades);
      this.progress = 100;

      this.results = {
        config: this.config,
        historicalData: this.summarizeData(historicalData),
        signals: signals.length,
        trades: trades.length,
        analysis,
        report,
        executionTime: Date.now() - this.startTime
      };

      logger.logInfo(`Backtest completed in ${(this.results.executionTime / 1000).toFixed(2)}s`);
      logger.logInfo(`Generated ${signals.length} signals, executed ${trades.length} trades`);

      return this.results;

    } catch (error) {
      logger.logError('Backtest failed:', error.message);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Load historical data for all symbols and timeframes
   */
  async loadHistoricalData() {
    const data = {};
    const totalSymbols = this.config.symbols.length * this.config.timeframes.length;
    let loaded = 0;

    for (const symbol of this.config.symbols) {
      data[symbol] = {};

      for (const timeframe of this.config.timeframes) {
        try {
          // Try to load existing data first
          let candles = await this.dataManager.loadData(symbol, timeframe, 'raw');

          if (!candles) {
            // Download if not available
            logger.logInfo(`Downloading data for ${symbol} ${timeframe}...`);
            candles = await this.dataManager.downloadDataRange(
              symbol,
              timeframe,
              this.config.startDate,
              this.config.endDate
            );

            // Save for future use
            await this.dataManager.saveData(symbol, timeframe, candles, 'raw');
          }

          // Validate data quality
          const validation = this.dataManager.validateData(candles);
          if (!validation.valid) {
            logger.logInfo(`Data quality issues for ${symbol} ${timeframe}:`, validation.errors.slice(0, 5));
          }

          // Filter data to backtest period
          const startTime = new Date(this.config.startDate).getTime();
          const endTime = new Date(this.config.endDate).getTime();

          candles = candles.filter(candle =>
            candle.openTime >= startTime && candle.openTime <= endTime
          );

          data[symbol][timeframe] = candles;
          loaded++;

          logger.logInfo(`Loaded ${candles.length} candles for ${symbol} ${timeframe} (${validation.dataQuality} quality)`);

        } catch (error) {
          logger.logError(`Error loading data for ${symbol} ${timeframe}:`, error.message);
          data[symbol][timeframe] = [];
        }
      }
    }

    logger.logInfo(`Loaded data for ${loaded}/${totalSymbols} symbol-timeframe combinations`);
    return data;
  }

  /**
   * Generate signals using historical data
   */
  async generateSignals(historicalData) {
    const allSignals = [];

    for (const symbol of this.config.symbols) {
      for (const timeframe of this.config.timeframes) {
        const candles = historicalData[symbol][timeframe];

        if (!candles || candles.length < 220) {
          logger.logInfo(`Insufficient data for ${symbol} ${timeframe}: ${candles?.length || 0} candles`);
          continue;
        }

        logger.logInfo(`Generating signals for ${symbol} ${timeframe}...`);

        const signals = await this.signalSimulator.generateSignals(
          symbol,
          timeframe,
          candles,
          this.config
        );

        allSignals.push(...signals);
        logger.logInfo(`Generated ${signals.length} signals for ${symbol} ${timeframe}`);
      }
    }

    // Sort signals by timestamp
    allSignals.sort((a, b) => a.timestamp - b.timestamp);

    logger.logInfo(`Total signals generated: ${allSignals.length}`);
    return allSignals;
  }

  /**
   * Simulate trading execution
   */
  async simulateTrading(signals, historicalData) {
    logger.logInfo('Starting trade simulation...');

    // Initialize portfolio
    this.portfolioManager.initialize(this.config.initialCapital);

    const trades = [];
    let processedSignals = 0;

    for (const signal of signals) {
      try {
        // Check portfolio constraints
        if (!this.portfolioManager.canOpenPosition(signal)) {
          continue;
        }

        // Get market data for execution
        const marketData = this.getMarketDataAtTime(
          historicalData,
          signal.symbol,
          signal.timeframe,
          signal.timestamp
        );

        if (!marketData) {
          continue;
        }

        // Simulate order execution
        const trade = await this.orderSimulator.executeSignal(
          signal,
          marketData,
          this.portfolioManager
        );

        if (trade) {
          trades.push(trade);
          this.portfolioManager.addTrade(trade);
        }

        processedSignals++;

        // Update progress
        if (processedSignals % 100 === 0) {
          const progress = (processedSignals / signals.length) * 30 + 40; // 40-70% range
          this.progress = Math.min(progress, 70);
        }

      } catch (error) {
        logger.logError(`Error simulating trade for signal ${signal.id}:`, error.message);
      }
    }

    logger.logInfo(`Simulated ${trades.length} trades from ${signals.length} signals`);
    return trades;
  }

  /**
   * Analyze backtest results
   */
  async analyzeResults(trades) {
    logger.logInfo('Analyzing backtest results...');

    const analysis = await this.riskAnalyzer.analyzePerformance(
      trades,
      this.portfolioManager.getPortfolioHistory(),
      this.config
    );

    return analysis;
  }

  /**
   * Generate comprehensive report
   */
  async generateReport(analysis, signals, trades) {
    logger.logInfo('Generating backtest report...');

    // Create temporary results object for report generation
    const tempResults = {
      config: this.config,
      signals: signals.length,
      trades: trades.length,
      analysis,
      executionTime: Date.now() - this.startTime
    };

    const report = await this.reportGenerator.generateReport(
      analysis,
      this.config,
      tempResults
    );

    return report;
  }

  /**
   * Get market data at specific timestamp
   */
  getMarketDataAtTime(historicalData, symbol, timeframe, timestamp) {
    const candles = historicalData[symbol]?.[timeframe];
    if (!candles) return null;

    // Find candle that contains this timestamp
    const candle = candles.find(c =>
      c.openTime <= timestamp && c.closeTime >= timestamp
    );

    return candle;
  }

  /**
   * Validate configuration
   */
  validateConfig() {
    const errors = [];

    // Date validation
    const startDate = new Date(this.config.startDate);
    const endDate = new Date(this.config.endDate);

    if (isNaN(startDate.getTime())) {
      errors.push('Invalid start date');
    }

    if (isNaN(endDate.getTime())) {
      errors.push('Invalid end date');
    }

    if (startDate >= endDate) {
      errors.push('Start date must be before end date');
    }

    // Capital validation
    if (this.config.initialCapital <= 0) {
      errors.push('Initial capital must be positive');
    }

    // Position size validation
    if (this.config.positionSizePercent <= 0 || this.config.positionSizePercent > 100) {
      errors.push('Position size percent must be between 0 and 100');
    }

    // Symbols validation
    if (!Array.isArray(this.config.symbols) || this.config.symbols.length === 0) {
      errors.push('At least one symbol must be specified');
    }

    if (errors.length > 0) {
      throw new Error(`Configuration errors: ${errors.join(', ')}`);
    }
  }

  /**
   * Ensure required data is available
   */
  async ensureDataAvailability() {
    const missing = [];

    for (const symbol of this.config.symbols) {
      for (const timeframe of this.config.timeframes) {
        if (!this.dataManager.hasData(symbol, timeframe, 'raw')) {
          missing.push(`${symbol}_${timeframe}`);
        }
      }
    }

    if (missing.length > 0) {
      logger.logInfo(`Missing data for: ${missing.join(', ')}`);
      logger.logInfo('Data will be downloaded during backtest execution');
    }
  }

  /**
   * Summarize data for results
   */
  summarizeData(historicalData) {
    const summary = {};

    for (const symbol of Object.keys(historicalData)) {
      summary[symbol] = {};

      for (const timeframe of Object.keys(historicalData[symbol])) {
        const candles = historicalData[symbol][timeframe];
        summary[symbol][timeframe] = {
          candleCount: candles.length,
          startTime: candles[0]?.openTime,
          endTime: candles[candles.length - 1]?.closeTime,
          priceRange: {
            min: Math.min(...candles.map(c => c.low)),
            max: Math.max(...candles.map(c => c.high))
          }
        };
      }
    }

    return summary;
  }

  /**
   * Get current progress
   */
  getProgress() {
    return {
      isRunning: this.isRunning,
      progress: this.progress,
      startTime: this.startTime,
      elapsedTime: this.startTime ? Date.now() - this.startTime : 0
    };
  }

  /**
   * Stop backtest (if running)
   */
  stop() {
    if (this.isRunning) {
      logger.logInfo('Stopping backtest...');
      this.isRunning = false;
    }
  }

  /**
   * Get results
   */
  getResults() {
    return this.results;
  }
}

module.exports = BacktestEngine;