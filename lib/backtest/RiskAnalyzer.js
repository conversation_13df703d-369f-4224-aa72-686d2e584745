// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class RiskAnalyzer {
  constructor(config = {}) {
    this.config = config;
  }

  /**
   * Analyze complete performance metrics
   */
  async analyzePerformance(trades, portfolioHistory, config) {
    try {
      const analysis = {
        // Basic metrics
        ...this.calculateBasicMetrics(trades, config),

        // Risk metrics
        ...this.calculateRiskMetrics(trades, portfolioHistory, config),

        // Advanced metrics
        ...this.calculateAdvancedMetrics(trades, portfolioHistory),

        // Market condition analysis
        ...this.analyzeMarketConditionPerformance(trades),

        // Time-based analysis
        ...this.analyzeTimeBasedPerformance(trades),

        // Symbol analysis
        ...this.analyzeSymbolPerformance(trades)
      };

      return analysis;

    } catch (error) {
      logger.logError('Error analyzing performance:', error.message);
      throw error;
    }
  }

  /**
   * Calculate basic performance metrics
   */
  calculateBasicMetrics(trades, config) {
    if (trades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalPnL: 0,
        totalReturn: 0,
        avgWin: 0,
        avgLoss: 0,
        profitFactor: 0,
        initialCapital: config.initialCapital,
        currentCapital: config.initialCapital
      };
    }

    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);

    const totalPnL = trades.reduce((sum, t) => sum + t.pnl, 0);
    const totalWin = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const totalLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));

    const avgWin = winningTrades.length > 0 ? totalWin / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? totalLoss / losingTrades.length : 0;

    const profitFactor = totalLoss > 0 ? totalWin / totalLoss : totalWin > 0 ? Infinity : 0;
    const winRate = (winningTrades.length / trades.length) * 100;

    const currentCapital = config.initialCapital + totalPnL;
    const totalReturn = (totalPnL / config.initialCapital) * 100;

    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      totalPnL,
      totalReturn,
      avgWin,
      avgLoss,
      profitFactor,
      initialCapital: config.initialCapital,
      currentCapital,

      // Additional metrics
      largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0,
      largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0,
      avgTrade: totalPnL / trades.length,
      avgTradePercent: trades.reduce((sum, t) => sum + t.pnlPercent, 0) / trades.length
    };
  }

  /**
   * Calculate risk metrics
   */
  calculateRiskMetrics(trades, portfolioHistory, config) {
    const maxDrawdown = this.calculateMaxDrawdown(portfolioHistory);
    const sharpeRatio = this.calculateSharpeRatio(trades);
    const sortinoRatio = this.calculateSortinoRatio(trades);
    const calmarRatio = this.calculateCalmarRatio(trades, maxDrawdown);
    const var95 = this.calculateVaR(trades, 0.95);
    const var99 = this.calculateVaR(trades, 0.99);

    return {
      maxDrawdown,
      sharpeRatio,
      sortinoRatio,
      calmarRatio,
      var95,
      var99,

      // Additional risk metrics
      volatility: this.calculateVolatility(trades),
      downside_deviation: this.calculateDownsideDeviation(trades),
      recovery_factor: this.calculateRecoveryFactor(trades, maxDrawdown),
      consecutive_losses: this.calculateMaxConsecutiveLosses(trades),
      consecutive_wins: this.calculateMaxConsecutiveWins(trades)
    };
  }

  /**
   * Calculate advanced metrics
   */
  calculateAdvancedMetrics(trades, portfolioHistory) {
    return {
      expectancy: this.calculateExpectancy(trades),
      kelly_criterion: this.calculateKellyCriterion(trades),
      information_ratio: this.calculateInformationRatio(trades),
      treynor_ratio: this.calculateTreynorRatio(trades),

      // Trade distribution
      trade_distribution: this.analyzeTradeDistribution(trades),

      // Timing metrics
      avg_trade_duration: this.calculateAverageTradeTime(trades),
      best_trading_hour: this.findBestTradingHour(trades),
      best_trading_day: this.findBestTradingDay(trades)
    };
  }

  /**
   * Calculate maximum drawdown
   */
  calculateMaxDrawdown(portfolioHistory) {
    if (portfolioHistory.length === 0) return 0;

    let maxDrawdown = 0;
    let peak = portfolioHistory[0].totalValue;

    for (const snapshot of portfolioHistory) {
      if (snapshot.totalValue > peak) {
        peak = snapshot.totalValue;
      }

      const drawdown = ((peak - snapshot.totalValue) / peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return maxDrawdown;
  }

  /**
   * Calculate Sharpe ratio
   */
  calculateSharpeRatio(trades) {
    if (trades.length < 2) return 0;

    const returns = trades.map(t => t.pnlPercent);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    // Assuming risk-free rate of 2% annually
    const riskFreeRate = 0.02 / 252; // Daily risk-free rate

    return stdDev > 0 ? (avgReturn - riskFreeRate) / stdDev : 0;
  }

  /**
   * Calculate Sortino ratio
   */
  calculateSortinoRatio(trades) {
    if (trades.length < 2) return 0;

    const returns = trades.map(t => t.pnlPercent);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

    const downside_deviation = this.calculateDownsideDeviation(trades);
    const riskFreeRate = 0.02 / 252;

    return downside_deviation > 0 ? (avgReturn - riskFreeRate) / downside_deviation : 0;
  }

  /**
   * Calculate Calmar ratio
   */
  calculateCalmarRatio(trades, maxDrawdown) {
    if (trades.length === 0 || maxDrawdown === 0) return 0;

    const totalReturn = trades.reduce((sum, t) => sum + t.pnlPercent, 0);
    const annualizedReturn = (totalReturn / trades.length) * 252; // Assuming daily trades

    return annualizedReturn / maxDrawdown;
  }

  /**
   * Calculate Value at Risk (VaR)
   */
  calculateVaR(trades, confidence) {
    if (trades.length === 0) return 0;

    const returns = trades.map(t => t.pnlPercent).sort((a, b) => a - b);
    const index = Math.floor((1 - confidence) * returns.length);

    return returns[index] || 0;
  }

  /**
   * Calculate volatility
   */
  calculateVolatility(trades) {
    if (trades.length < 2) return 0;

    const returns = trades.map(t => t.pnlPercent);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  /**
   * Calculate downside deviation
   */
  calculateDownsideDeviation(trades) {
    if (trades.length === 0) return 0;

    const returns = trades.map(t => t.pnlPercent);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

    const downsideReturns = returns.filter(r => r < avgReturn);
    if (downsideReturns.length === 0) return 0;

    const downsideVariance = downsideReturns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / downsideReturns.length;

    return Math.sqrt(downsideVariance);
  }

  /**
   * Calculate recovery factor
   */
  calculateRecoveryFactor(trades, maxDrawdown) {
    if (maxDrawdown === 0) return Infinity;

    const totalReturn = trades.reduce((sum, t) => sum + t.pnlPercent, 0);
    return totalReturn / maxDrawdown;
  }

  /**
   * Calculate maximum consecutive losses
   */
  calculateMaxConsecutiveLosses(trades) {
    let maxConsecutive = 0;
    let currentConsecutive = 0;

    for (const trade of trades) {
      if (trade.pnl < 0) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 0;
      }
    }

    return maxConsecutive;
  }

  /**
   * Calculate maximum consecutive wins
   */
  calculateMaxConsecutiveWins(trades) {
    let maxConsecutive = 0;
    let currentConsecutive = 0;

    for (const trade of trades) {
      if (trade.pnl > 0) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 0;
      }
    }

    return maxConsecutive;
  }

  /**
   * Calculate expectancy
   */
  calculateExpectancy(trades) {
    if (trades.length === 0) return 0;

    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);

    const winRate = winningTrades.length / trades.length;
    const lossRate = losingTrades.length / trades.length;

    const avgWin = winningTrades.length > 0 ?
      winningTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ?
      Math.abs(losingTrades.reduce((sum, t) => sum + t.pnlPercent, 0) / losingTrades.length) : 0;

    return (winRate * avgWin) - (lossRate * avgLoss);
  }

  /**
   * Calculate Kelly Criterion
   */
  calculateKellyCriterion(trades) {
    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);

    if (winningTrades.length === 0 || losingTrades.length === 0) return 0;

    const winRate = winningTrades.length / trades.length;
    const avgWin = winningTrades.reduce((sum, t) => sum + Math.abs(t.pnlPercent), 0) / winningTrades.length;
    const avgLoss = losingTrades.reduce((sum, t) => sum + Math.abs(t.pnlPercent), 0) / losingTrades.length;

    const winLossRatio = avgWin / avgLoss;

    return (winRate * winLossRatio - (1 - winRate)) / winLossRatio;
  }

  /**
   * Calculate Information Ratio
   */
  calculateInformationRatio(trades) {
    // Simplified calculation - would need benchmark returns for proper calculation
    return this.calculateSharpeRatio(trades);
  }

  /**
   * Calculate Treynor Ratio
   */
  calculateTreynorRatio(trades) {
    // Simplified calculation - would need beta for proper calculation
    return this.calculateSharpeRatio(trades);
  }

  /**
   * Analyze trade distribution
   */
  analyzeTradeDistribution(trades) {
    const returns = trades.map(t => t.pnlPercent);
    returns.sort((a, b) => a - b);

    const percentiles = [10, 25, 50, 75, 90, 95, 99].map(p => ({
      percentile: p,
      value: this.calculatePercentile(returns, p / 100)
    }));

    return {
      percentiles,
      skewness: this.calculateSkewness(returns),
      kurtosis: this.calculateKurtosis(returns)
    };
  }

  /**
   * Calculate percentile
   */
  calculatePercentile(sortedArray, percentile) {
    const index = percentile * (sortedArray.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    const weight = index % 1;

    if (upper >= sortedArray.length) return sortedArray[sortedArray.length - 1];

    return sortedArray[lower] * (1 - weight) + sortedArray[upper] * weight;
  }

  /**
   * Calculate skewness
   */
  calculateSkewness(returns) {
    if (returns.length < 3) return 0;

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return 0;

    const skewness = returns.reduce((sum, r) => sum + Math.pow((r - mean) / stdDev, 3), 0) / returns.length;

    return skewness;
  }

  /**
   * Calculate kurtosis
   */
  calculateKurtosis(returns) {
    if (returns.length < 4) return 0;

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return 0;

    const kurtosis = returns.reduce((sum, r) => sum + Math.pow((r - mean) / stdDev, 4), 0) / returns.length;

    return kurtosis - 3; // Excess kurtosis
  }

  /**
   * Calculate average trade duration
   */
  calculateAverageTradeTime(trades) {
    if (trades.length === 0) return 0;

    const durations = trades.map(t => t.exitTime - t.entryTime);
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;

    return avgDuration / (1000 * 60); // Convert to minutes
  }

  /**
   * Find best trading hour
   */
  findBestTradingHour(trades) {
    const hourlyPnL = {};

    trades.forEach(trade => {
      const hour = new Date(trade.entryTime).getHours();
      if (!hourlyPnL[hour]) {
        hourlyPnL[hour] = { trades: 0, pnl: 0 };
      }
      hourlyPnL[hour].trades++;
      hourlyPnL[hour].pnl += trade.pnl;
    });

    let bestHour = 0;
    let bestPnL = -Infinity;

    for (const [hour, data] of Object.entries(hourlyPnL)) {
      if (data.pnl > bestPnL) {
        bestPnL = data.pnl;
        bestHour = parseInt(hour);
      }
    }

    return { hour: bestHour, pnl: bestPnL, hourlyBreakdown: hourlyPnL };
  }

  /**
   * Find best trading day
   */
  findBestTradingDay(trades) {
    const dailyPnL = {};

    trades.forEach(trade => {
      const day = new Date(trade.entryTime).getDay(); // 0 = Sunday, 1 = Monday, etc.
      if (!dailyPnL[day]) {
        dailyPnL[day] = { trades: 0, pnl: 0 };
      }
      dailyPnL[day].trades++;
      dailyPnL[day].pnl += trade.pnl;
    });

    let bestDay = 0;
    let bestPnL = -Infinity;

    for (const [day, data] of Object.entries(dailyPnL)) {
      if (data.pnl > bestPnL) {
        bestPnL = data.pnl;
        bestDay = parseInt(day);
      }
    }

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    return {
      day: bestDay,
      dayName: dayNames[bestDay],
      pnl: bestPnL,
      dailyBreakdown: dailyPnL
    };
  }

  /**
   * Analyze market condition performance
   */
  analyzeMarketConditionPerformance(trades) {
    const conditionPerformance = {};

    trades.forEach(trade => {
      const condition = trade.marketCondition?.condition || 'unknown';

      if (!conditionPerformance[condition]) {
        conditionPerformance[condition] = {
          trades: 0,
          wins: 0,
          losses: 0,
          totalPnL: 0,
          winRate: 0,
          avgPnL: 0
        };
      }

      const perf = conditionPerformance[condition];
      perf.trades++;
      perf.totalPnL += trade.pnl;

      if (trade.pnl > 0) {
        perf.wins++;
      } else {
        perf.losses++;
      }

      perf.winRate = (perf.wins / perf.trades) * 100;
      perf.avgPnL = perf.totalPnL / perf.trades;
    });

    return { marketConditionPerformance: conditionPerformance };
  }

  /**
   * Analyze time-based performance
   */
  analyzeTimeBasedPerformance(trades) {
    const monthlyPerformance = {};
    const weeklyPerformance = {};

    trades.forEach(trade => {
      const date = new Date(trade.entryTime);
      const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const week = this.getWeekNumber(date);

      // Monthly performance
      if (!monthlyPerformance[month]) {
        monthlyPerformance[month] = { trades: 0, pnl: 0, wins: 0 };
      }
      monthlyPerformance[month].trades++;
      monthlyPerformance[month].pnl += trade.pnl;
      if (trade.pnl > 0) monthlyPerformance[month].wins++;

      // Weekly performance
      if (!weeklyPerformance[week]) {
        weeklyPerformance[week] = { trades: 0, pnl: 0, wins: 0 };
      }
      weeklyPerformance[week].trades++;
      weeklyPerformance[week].pnl += trade.pnl;
      if (trade.pnl > 0) weeklyPerformance[week].wins++;
    });

    return {
      monthlyPerformance,
      weeklyPerformance
    };
  }

  /**
   * Analyze symbol performance
   */
  analyzeSymbolPerformance(trades) {
    const symbolPerformance = {};

    trades.forEach(trade => {
      if (!symbolPerformance[trade.symbol]) {
        symbolPerformance[trade.symbol] = {
          trades: 0,
          wins: 0,
          losses: 0,
          totalPnL: 0,
          winRate: 0,
          avgPnL: 0,
          bestTrade: 0,
          worstTrade: 0
        };
      }

      const perf = symbolPerformance[trade.symbol];
      perf.trades++;
      perf.totalPnL += trade.pnl;

      if (trade.pnl > 0) {
        perf.wins++;
        perf.bestTrade = Math.max(perf.bestTrade, trade.pnl);
      } else {
        perf.losses++;
        perf.worstTrade = Math.min(perf.worstTrade, trade.pnl);
      }

      perf.winRate = (perf.wins / perf.trades) * 100;
      perf.avgPnL = perf.totalPnL / perf.trades;
    });

    return { symbolPerformance };
  }

  /**
   * Get week number
   */
  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }
}

module.exports = RiskAnalyzer;