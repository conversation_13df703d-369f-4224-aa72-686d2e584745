const SignalAnalyzer = require('../trading/signalAnalyzer');
const MarketConditionAnalyzer = require('../trading/marketConditionAnalyzer');

// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class SignalSimulator {
  constructor(config = {}) {
    this.config = config;
    this.signalAnalyzer = SignalAnalyzer;
    this.marketConditionAnalyzer = MarketConditionAnalyzer;
    this.signalCounter = 0;
  }

  /**
   * Generate signals for historical data
   */
  async generateSignals(symbol, timeframe, candles, config) {
    const signals = [];
    const minCandles = 220; // Minimum candles needed for indicators

    if (candles.length < minCandles) {
      logger.logWarning(`Insufficient candles for ${symbol} ${timeframe}: ${candles.length}`);
      return signals;
    }

    logger.logInfo(`Processing ${candles.length} candles for ${symbol} ${timeframe}...`);

    // Process candles in chronological order
    for (let i = minCandles; i < candles.length; i++) {
      try {
        // Get candle window for analysis (current + previous candles)
        const candleWindow = candles.slice(i - minCandles + 1, i + 1);
        const currentCandle = candles[i];

        // Skip if we don't have enough data
        if (candleWindow.length < minCandles) {
          continue;
        }

        // Convert to format expected by signal analyzer
        const formattedCandles = this.formatCandlesForAnalysis(candleWindow);

        // Generate signal using existing signal analyzer
        const signal = await this.generateSignalAtTime(
          symbol,
          timeframe,
          formattedCandles,
          currentCandle.openTime,
          config
        );

        if (signal) {
          signals.push(signal);

          // Log signal generation
          if (signals.length % 10 === 0) {
            logger.logInfo(`Generated ${signals.length} signals for ${symbol} ${timeframe}...`);
          }
        }

      } catch (error) {
        logger.logError(`Error generating signal at index ${i} for ${symbol}:`, error.message);
      }
    }

    logger.logInfo(`Completed signal generation for ${symbol} ${timeframe}: ${signals.length} signals`);
    return signals;
  }

  /**
   * Generate signal at specific time point
   */
  async generateSignalAtTime(symbol, timeframe, candles, timestamp, config) {
    try {
      // Use existing signal analyzer logic
      const signal = await this.signalAnalyzer.analyzeSignal(symbol, timeframe, candles);

      if (!signal) {
        return null;
      }

      // Add backtest-specific metadata
      const backtestSignal = {
        id: `backtest_${++this.signalCounter}`,
        timestamp,
        symbol: signal.symbol,
        timeframe: signal.timeframe,
        type: signal.type,
        entry: signal.entry,
        stopLoss: signal.stopLoss,
        takeProfit: signal.takeProfit,
        riskReward: signal.riskReward,
        tpMethod: signal.tpMethod,
        conditions: signal.conditions,
        indicators: signal.indicators,
        marketCondition: signal.marketCondition,

        // Backtest metadata
        backtestMetadata: {
          candleIndex: candles.length - 1,
          generatedAt: new Date(timestamp),
          marketData: {
            open: candles[candles.length - 1].open,
            high: candles[candles.length - 1].high,
            low: candles[candles.length - 1].low,
            close: candles[candles.length - 1].close,
            volume: candles[candles.length - 1].volume
          }
        }
      };

      return backtestSignal;

    } catch (error) {
      logger.logError(`Error in signal generation for ${symbol} at ${timestamp}:`, error.message);
      return null;
    }
  }

  /**
   * Format candles for signal analyzer
   */
  formatCandlesForAnalysis(candles) {
    return candles.map(candle => ({
      open: parseFloat(candle.open),
      high: parseFloat(candle.high),
      low: parseFloat(candle.low),
      close: parseFloat(candle.close),
      volume: parseFloat(candle.volume),
      openTime: candle.openTime,
      closeTime: candle.closeTime
    }));
  }

  /**
   * Apply market condition filtering (if enabled)
   */
  async applyMarketConditionFilter(signal, candles, config) {
    if (!config.useMarketConditionFilter) {
      return signal;
    }

    try {
      // Analyze market condition at signal time
      const marketCondition = await this.marketConditionAnalyzer.analyzeMarketCondition(
        signal.symbol,
        signal.timeframe
      );

      const recommendations = this.marketConditionAnalyzer.getTradingRecommendations(marketCondition);

      // Apply filtering logic
      if (!recommendations.shouldTrade) {
        logger.logInfo(`Signal filtered out by market condition: ${marketCondition.condition} (${marketCondition.confidence.toFixed(2)})`);
        return null;
      }

      // Add market condition data to signal
      signal.marketCondition = {
        condition: marketCondition.condition,
        confidence: marketCondition.confidence,
        riskLevel: marketCondition.riskLevel,
        recommendations: marketCondition.recommendations,
        positionSizeMultiplier: recommendations.positionSizeMultiplier
      };

      return signal;

    } catch (error) {
      logger.logError(`Error applying market condition filter:`, error.message);
      return signal; // Return original signal if filter fails
    }
  }

  /**
   * Validate signal quality
   */
  validateSignal(signal) {
    const errors = [];

    // Required fields
    if (!signal.symbol) errors.push('Missing symbol');
    if (!signal.type) errors.push('Missing type');
    if (!signal.entry) errors.push('Missing entry price');
    if (!signal.stopLoss) errors.push('Missing stop loss');
    if (!signal.takeProfit) errors.push('Missing take profit');

    // Logical validation
    if (signal.type === 'BUY') {
      if (signal.stopLoss >= signal.entry) {
        errors.push('BUY signal: Stop loss must be below entry');
      }
      if (signal.takeProfit <= signal.entry) {
        errors.push('BUY signal: Take profit must be above entry');
      }
    } else if (signal.type === 'SELL') {
      if (signal.stopLoss <= signal.entry) {
        errors.push('SELL signal: Stop loss must be above entry');
      }
      if (signal.takeProfit >= signal.entry) {
        errors.push('SELL signal: Take profit must be below entry');
      }
    }

    // Risk/reward validation
    if (signal.riskReward && signal.riskReward < 1) {
      errors.push('Risk/reward ratio too low');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get signal statistics
   */
  getSignalStatistics(signals) {
    if (signals.length === 0) {
      return {
        total: 0,
        byType: { BUY: 0, SELL: 0 },
        bySymbol: {},
        byTimeframe: {},
        byMarketCondition: {},
        avgRiskReward: 0
      };
    }

    const stats = {
      total: signals.length,
      byType: { BUY: 0, SELL: 0 },
      bySymbol: {},
      byTimeframe: {},
      byMarketCondition: {},
      avgRiskReward: 0
    };

    let totalRiskReward = 0;

    signals.forEach(signal => {
      // By type
      stats.byType[signal.type] = (stats.byType[signal.type] || 0) + 1;

      // By symbol
      stats.bySymbol[signal.symbol] = (stats.bySymbol[signal.symbol] || 0) + 1;

      // By timeframe
      stats.byTimeframe[signal.timeframe] = (stats.byTimeframe[signal.timeframe] || 0) + 1;

      // By market condition
      if (signal.marketCondition) {
        const condition = signal.marketCondition.condition;
        stats.byMarketCondition[condition] = (stats.byMarketCondition[condition] || 0) + 1;
      }

      // Risk reward
      if (signal.riskReward) {
        totalRiskReward += signal.riskReward;
      }
    });

    stats.avgRiskReward = totalRiskReward / signals.length;

    return stats;
  }

  /**
   * Filter signals by criteria
   */
  filterSignals(signals, criteria = {}) {
    return signals.filter(signal => {
      // Filter by symbol
      if (criteria.symbols && !criteria.symbols.includes(signal.symbol)) {
        return false;
      }

      // Filter by type
      if (criteria.types && !criteria.types.includes(signal.type)) {
        return false;
      }

      // Filter by timeframe
      if (criteria.timeframes && !criteria.timeframes.includes(signal.timeframe)) {
        return false;
      }

      // Filter by market condition
      if (criteria.marketConditions && signal.marketCondition) {
        if (!criteria.marketConditions.includes(signal.marketCondition.condition)) {
          return false;
        }
      }

      // Filter by risk/reward ratio
      if (criteria.minRiskReward && signal.riskReward < criteria.minRiskReward) {
        return false;
      }

      // Filter by date range
      if (criteria.startTime && signal.timestamp < criteria.startTime) {
        return false;
      }

      if (criteria.endTime && signal.timestamp > criteria.endTime) {
        return false;
      }

      return true;
    });
  }

  /**
   * Export signals to JSON
   */
  exportSignals(signals, filepath) {
    try {
      const exportData = {
        metadata: {
          exportTime: new Date().toISOString(),
          totalSignals: signals.length,
          statistics: this.getSignalStatistics(signals)
        },
        signals: signals.map(signal => ({
          ...signal,
          // Convert timestamp to readable date
          generatedAt: new Date(signal.timestamp).toISOString()
        }))
      };

      require('fs').writeFileSync(filepath, JSON.stringify(exportData, null, 2));
      logger.logInfo(`Exported ${signals.length} signals to ${filepath}`);

      return true;
    } catch (error) {
      logger.logError(`Error exporting signals:`, error.message);
      return false;
    }
  }
}

module.exports = SignalSimulator;