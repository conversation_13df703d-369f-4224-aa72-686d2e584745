// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class PortfolioManager {
  constructor(config = {}) {
    this.config = config;
    this.initialCapital = 0;
    this.currentCapital = 0;
    this.trades = [];
    this.openPositions = [];
    this.portfolioHistory = [];
    this.dailyStats = new Map();
  }

  /**
   * Initialize portfolio with starting capital
   */
  initialize(initialCapital) {
    this.initialCapital = initialCapital;
    this.currentCapital = initialCapital;
    this.trades = [];
    this.openPositions = [];
    this.portfolioHistory = [];
    this.dailyStats.clear();

    // Record initial state
    this.recordPortfolioSnapshot(Date.now(), 'initialization');

    logger.logInfo(`Portfolio initialized with $${initialCapital}`);
  }

  /**
   * Add completed trade to portfolio
   */
  addTrade(trade) {
    try {
      // Add to trades history
      this.trades.push(trade);

      // Update capital
      this.currentCapital += trade.pnl;

      // Update daily stats
      this.updateDailyStats(trade);

      // Record portfolio snapshot
      this.recordPortfolioSnapshot(trade.exitTime, 'trade_completed', trade);

      // Remove from open positions if it was there
      this.openPositions = this.openPositions.filter(pos => pos.id !== trade.id);

      logger.logInfo(`Trade ${trade.id} added: ${trade.type} ${trade.symbol} - P&L: $${trade.pnl.toFixed(2)} (${trade.pnlPercent.toFixed(2)}%)`);

    } catch (error) {
      logger.logError(`Error adding trade ${trade.id}:`, error.message);
    }
  }

  /**
   * Add open position
   */
  addOpenPosition(trade) {
    this.openPositions.push({
      id: trade.id,
      symbol: trade.symbol,
      type: trade.type,
      entryTime: trade.entryTime,
      entryPrice: trade.entryPrice,
      positionSize: trade.positionSize,
      positionValue: trade.positionValue,
      stopLoss: trade.stopLoss,
      takeProfit: trade.takeProfit
    });

    // Update capital (subtract used capital)
    this.currentCapital -= trade.positionValue;

    logger.logInfo(`Open position added: ${trade.type} ${trade.symbol} - Size: $${trade.positionValue.toFixed(2)}`);
  }

  /**
   * Check if can open new position
   */
  canOpenPosition(signal) {
    // Check maximum concurrent positions
    const maxPositions = this.config.maxConcurrentPositions || 5;
    if (this.openPositions.length >= maxPositions) {
      return false;
    }

    // Check available capital
    const requiredCapital = this.calculateRequiredCapital(signal);
    if (requiredCapital > this.getAvailableCapital()) {
      return false;
    }

    // Check daily loss limit
    const dailyLossLimit = this.config.dailyLossLimit || 5;
    const todayLossPercent = this.getTodayLossPercent();
    if (todayLossPercent >= dailyLossLimit) {
      return false;
    }

    // Check maximum drawdown
    const maxDrawdown = this.config.maxDrawdown || 20;
    const currentDrawdown = this.getCurrentDrawdown();
    if (currentDrawdown >= maxDrawdown) {
      return false;
    }

    return true;
  }

  /**
   * Calculate required capital for position
   */
  calculateRequiredCapital(signal) {
    const positionSizePercent = this.config.positionSizePercent || 2;
    let requiredCapital = this.currentCapital * (positionSizePercent / 100);

    // Apply market condition multiplier
    if (signal.marketCondition?.positionSizeMultiplier) {
      requiredCapital *= signal.marketCondition.positionSizeMultiplier;
    }

    return requiredCapital;
  }

  /**
   * Get available capital
   */
  getAvailableCapital() {
    // Available capital = current capital - capital tied up in open positions
    const tiedUpCapital = this.openPositions.reduce((sum, pos) => sum + pos.positionValue, 0);
    return this.currentCapital - tiedUpCapital;
  }

  /**
   * Get open positions count
   */
  getOpenPositionsCount() {
    return this.openPositions.length;
  }

  /**
   * Get today's loss percentage
   */
  getTodayLossPercent() {
    const today = new Date().toDateString();
    const todayStats = this.dailyStats.get(today);

    if (!todayStats) return 0;

    return (todayStats.totalLoss / this.initialCapital) * 100;
  }

  /**
   * Get today's loss amount
   */
  getTodayLoss() {
    return this.getTodayLossPercent();
  }

  /**
   * Get current drawdown percentage
   */
  getCurrentDrawdown() {
    if (this.portfolioHistory.length === 0) return 0;

    const peak = Math.max(...this.portfolioHistory.map(h => h.totalValue));
    const current = this.getTotalPortfolioValue();

    return ((peak - current) / peak) * 100;
  }

  /**
   * Get total portfolio value
   */
  getTotalPortfolioValue() {
    // Current capital + value of open positions (at current market price)
    // For simplicity, we'll use entry value of open positions
    const openPositionsValue = this.openPositions.reduce((sum, pos) => sum + pos.positionValue, 0);
    return this.currentCapital + openPositionsValue;
  }

  /**
   * Update daily statistics
   */
  updateDailyStats(trade) {
    const tradeDate = new Date(trade.exitTime).toDateString();

    if (!this.dailyStats.has(tradeDate)) {
      this.dailyStats.set(tradeDate, {
        trades: 0,
        wins: 0,
        losses: 0,
        totalPnL: 0,
        totalWin: 0,
        totalLoss: 0,
        winRate: 0
      });
    }

    const dayStats = this.dailyStats.get(tradeDate);
    dayStats.trades++;
    dayStats.totalPnL += trade.pnl;

    if (trade.pnl > 0) {
      dayStats.wins++;
      dayStats.totalWin += trade.pnl;
    } else {
      dayStats.losses++;
      dayStats.totalLoss += Math.abs(trade.pnl);
    }

    dayStats.winRate = (dayStats.wins / dayStats.trades) * 100;
  }

  /**
   * Record portfolio snapshot
   */
  recordPortfolioSnapshot(timestamp, event, trade = null) {
    const snapshot = {
      timestamp,
      event,
      totalValue: this.getTotalPortfolioValue(),
      currentCapital: this.currentCapital,
      openPositions: this.openPositions.length,
      totalTrades: this.trades.length,
      totalReturn: ((this.getTotalPortfolioValue() - this.initialCapital) / this.initialCapital) * 100,
      drawdown: this.getCurrentDrawdown(),
      trade: trade ? {
        id: trade.id,
        symbol: trade.symbol,
        type: trade.type,
        pnl: trade.pnl,
        pnlPercent: trade.pnlPercent
      } : null
    };

    this.portfolioHistory.push(snapshot);
  }

  /**
   * Get portfolio performance metrics
   */
  getPerformanceMetrics() {
    if (this.trades.length === 0) {
      return {
        totalTrades: 0,
        winRate: 0,
        totalReturn: 0,
        totalPnL: 0,
        avgWin: 0,
        avgLoss: 0,
        profitFactor: 0,
        maxDrawdown: 0,
        sharpeRatio: 0
      };
    }

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl < 0);

    const totalPnL = this.trades.reduce((sum, t) => sum + t.pnl, 0);
    const totalWin = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const totalLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));

    const avgWin = winningTrades.length > 0 ? totalWin / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? totalLoss / losingTrades.length : 0;

    const profitFactor = totalLoss > 0 ? totalWin / totalLoss : totalWin > 0 ? Infinity : 0;

    const totalReturn = ((this.getTotalPortfolioValue() - this.initialCapital) / this.initialCapital) * 100;

    const maxDrawdown = this.getMaxDrawdown();

    const sharpeRatio = this.calculateSharpeRatio();

    return {
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: (winningTrades.length / this.trades.length) * 100,
      totalReturn,
      totalPnL,
      avgWin,
      avgLoss,
      profitFactor,
      maxDrawdown,
      sharpeRatio,
      currentCapital: this.currentCapital,
      initialCapital: this.initialCapital
    };
  }

  /**
   * Get maximum drawdown from history
   */
  getMaxDrawdown() {
    if (this.portfolioHistory.length === 0) return 0;

    let maxDrawdown = 0;
    let peak = this.portfolioHistory[0].totalValue;

    for (const snapshot of this.portfolioHistory) {
      if (snapshot.totalValue > peak) {
        peak = snapshot.totalValue;
      }

      const drawdown = ((peak - snapshot.totalValue) / peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return maxDrawdown;
  }

  /**
   * Calculate Sharpe ratio (simplified)
   */
  calculateSharpeRatio() {
    if (this.trades.length < 2) return 0;

    const returns = this.trades.map(t => t.pnlPercent);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    // Assuming risk-free rate of 2% annually, converted to per-trade basis
    const riskFreeRate = 0.02 / 252; // Daily risk-free rate

    return stdDev > 0 ? (avgReturn - riskFreeRate) / stdDev : 0;
  }

  /**
   * Get portfolio history
   */
  getPortfolioHistory() {
    return this.portfolioHistory;
  }

  /**
   * Get daily statistics
   */
  getDailyStats() {
    return Array.from(this.dailyStats.entries()).map(([date, stats]) => ({
      date,
      ...stats
    }));
  }

  /**
   * Get trades by symbol
   */
  getTradesBySymbol() {
    const tradesBySymbol = {};

    this.trades.forEach(trade => {
      if (!tradesBySymbol[trade.symbol]) {
        tradesBySymbol[trade.symbol] = [];
      }
      tradesBySymbol[trade.symbol].push(trade);
    });

    return tradesBySymbol;
  }

  /**
   * Get trades by market condition
   */
  getTradesByMarketCondition() {
    const tradesByCondition = {};

    this.trades.forEach(trade => {
      const condition = trade.marketCondition?.condition || 'unknown';
      if (!tradesByCondition[condition]) {
        tradesByCondition[condition] = [];
      }
      tradesByCondition[condition].push(trade);
    });

    return tradesByCondition;
  }

  /**
   * Export portfolio data
   */
  exportData() {
    return {
      config: this.config,
      initialCapital: this.initialCapital,
      currentCapital: this.currentCapital,
      trades: this.trades,
      portfolioHistory: this.portfolioHistory,
      dailyStats: this.getDailyStats(),
      performanceMetrics: this.getPerformanceMetrics(),
      tradesBySymbol: this.getTradesBySymbol(),
      tradesByMarketCondition: this.getTradesByMarketCondition()
    };
  }

  /**
   * Reset portfolio
   */
  reset() {
    this.initialCapital = 0;
    this.currentCapital = 0;
    this.trades = [];
    this.openPositions = [];
    this.portfolioHistory = [];
    this.dailyStats.clear();
  }
}

module.exports = PortfolioManager;