// Import logger if not available globally
if (typeof logger === 'undefined') {
  global.logger = require('../logger')(`${__dirname}/../../logs`);
}

class OrderSimulator {
  constructor(config = {}) {
    this.config = config;
    this.tradeCounter = 0;
    this.executionHistory = [];
  }

  /**
   * Execute a signal and simulate the complete trade lifecycle
   */
  async executeSignal(signal, marketData, portfolioManager) {
    try {
      // Check if we can open this position
      if (!this.canExecuteSignal(signal, portfolioManager)) {
        return null;
      }

      // Simulate order entry
      const entryExecution = this.simulateOrderEntry(signal, marketData);
      if (!entryExecution.success) {
        return null;
      }

      // Create trade object
      const trade = {
        id: `trade_${++this.tradeCounter}`,
        signalId: signal.id,
        symbol: signal.symbol,
        timeframe: signal.timeframe,
        type: signal.type,

        // Entry details
        entryTime: signal.timestamp,
        entryPrice: entryExecution.price,
        entrySlippage: entryExecution.slippage,

        // Target levels
        stopLoss: signal.stopLoss,
        takeProfit: signal.takeProfit,
        riskReward: signal.riskReward,

        // Position details
        positionSize: entryExecution.positionSize,
        positionValue: entryExecution.positionValue,
        commission: entryExecution.commission,

        // Status
        status: 'open',
        exitTime: null,
        exitPrice: null,
        exitReason: null,
        pnl: 0,
        pnlPercent: 0,

        // Metadata
        marketCondition: signal.marketCondition,
        backtestMetadata: {
          ...signal.backtestMetadata,
          entryExecution
        }
      };

      // Simulate trade monitoring and exit
      await this.simulateTradeLifecycle(trade, marketData);

      return trade;

    } catch (error) {
      logger.logError(`Error executing signal ${signal.id}:`, error.message);
      return null;
    }
  }

  /**
   * Simulate order entry execution
   */
  simulateOrderEntry(signal, marketData) {
    try {
      // Calculate slippage
      const slippagePercent = this.config.slippage || 0.1;
      const slippageMultiplier = signal.type === 'BUY' ?
        (1 + slippagePercent / 100) :
        (1 - slippagePercent / 100);

      const executionPrice = signal.entry * slippageMultiplier;

      // Calculate position size
      const positionSizePercent = this.config.positionSizePercent || 2;
      const availableCapital = this.config.initialCapital || 10000; // Use initial capital for simplicity
      const positionValue = availableCapital * (positionSizePercent / 100);

      // Apply market condition position size multiplier
      let adjustedPositionValue = positionValue;
      if (signal.marketCondition?.positionSizeMultiplier) {
        adjustedPositionValue *= signal.marketCondition.positionSizeMultiplier;
      }

      const positionSize = adjustedPositionValue / executionPrice;

      // Calculate commission
      const commissionPercent = this.config.commission || 0.1;
      const commission = adjustedPositionValue * (commissionPercent / 100);

      // Simulate execution delay
      const latency = this.config.latency || 100;

      return {
        success: true,
        price: executionPrice,
        slippage: Math.abs(executionPrice - signal.entry) / signal.entry * 100,
        positionSize,
        positionValue: adjustedPositionValue,
        commission,
        latency,
        executionTime: signal.timestamp + latency
      };

    } catch (error) {
      logger.logError('Error simulating order entry:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Simulate complete trade lifecycle until exit
   */
  async simulateTradeLifecycle(trade, initialMarketData) {
    try {
      // For backtest, we need to simulate monitoring the trade
      // This would typically involve checking subsequent price data

      // Simplified simulation: assume trade hits SL or TP based on probability
      // In real implementation, this would check actual price movements

      const exitSimulation = this.simulateTradeExit(trade);

      // Update trade with exit details
      trade.exitTime = exitSimulation.exitTime;
      trade.exitPrice = exitSimulation.exitPrice;
      trade.exitReason = exitSimulation.exitReason;
      trade.status = 'closed';

      // Calculate P&L
      this.calculateTradePnL(trade);

      return trade;

    } catch (error) {
      logger.logError(`Error simulating trade lifecycle for ${trade.id}:`, error.message);
      trade.status = 'error';
      return trade;
    }
  }

  /**
   * Simulate trade exit (simplified for now)
   */
  simulateTradeExit(trade) {
    // This is a simplified simulation
    // In real backtest, we would check actual price movements against SL/TP

    // For now, simulate based on risk/reward and market conditions
    const winProbability = this.calculateWinProbability(trade);
    const isWin = Math.random() < winProbability;

    let exitPrice, exitReason;

    if (isWin) {
      exitPrice = trade.takeProfit;
      exitReason = 'take_profit';
    } else {
      exitPrice = trade.stopLoss;
      exitReason = 'stop_loss';
    }

    // Simulate exit time (random between 5 minutes to 4 hours)
    const minExitTime = 5 * 60 * 1000; // 5 minutes
    const maxExitTime = 4 * 60 * 60 * 1000; // 4 hours
    const exitDelay = Math.random() * (maxExitTime - minExitTime) + minExitTime;
    const exitTime = trade.entryTime + exitDelay;

    return {
      exitTime,
      exitPrice,
      exitReason
    };
  }

  /**
   * Calculate win probability based on various factors
   */
  calculateWinProbability(trade) {
    let baseProbability = 0.6; // Base 60% win rate

    // Adjust based on market condition
    if (trade.marketCondition) {
      switch (trade.marketCondition.condition) {
        case 'strong_uptrend':
          baseProbability += trade.type === 'BUY' ? 0.15 : -0.1;
          break;
        case 'strong_downtrend':
          baseProbability += trade.type === 'SELL' ? 0.15 : -0.1;
          break;
        case 'sideways':
          baseProbability -= 0.2; // Lower win rate in sideways
          break;
        case 'high_volatility':
          baseProbability -= 0.15; // Lower win rate in high volatility
          break;
        case 'false_breakout_prone':
          baseProbability -= 0.1;
          break;
      }
    }

    // Adjust based on risk/reward ratio
    if (trade.riskReward) {
      // Higher R:R typically means lower win rate
      if (trade.riskReward > 2.5) {
        baseProbability -= 0.1;
      } else if (trade.riskReward < 1.5) {
        baseProbability += 0.05;
      }
    }

    // Ensure probability stays within bounds
    return Math.max(0.2, Math.min(0.8, baseProbability));
  }

  /**
   * Calculate trade P&L
   */
  calculateTradePnL(trade) {
    const entryPrice = trade.entryPrice;
    const exitPrice = trade.exitPrice;
    const positionSize = trade.positionSize;

    let pnl = 0;

    if (trade.type === 'BUY') {
      pnl = (exitPrice - entryPrice) * positionSize;
    } else { // SELL
      pnl = (entryPrice - exitPrice) * positionSize;
    }

    // Subtract commission (both entry and exit)
    pnl -= trade.commission * 2;

    // Calculate percentage
    const pnlPercent = (pnl / trade.positionValue) * 100;

    trade.pnl = pnl;
    trade.pnlPercent = pnlPercent;

    return { pnl, pnlPercent };
  }

  /**
   * Check if signal can be executed
   */
  canExecuteSignal(signal, portfolioManager) {
    // Check maximum concurrent positions
    const maxPositions = this.config.maxConcurrentPositions || 5;
    const currentPositions = portfolioManager.getOpenPositionsCount();

    if (currentPositions >= maxPositions) {
      return false;
    }

    // Check available capital (simplified for now)
    const requiredCapital = this.calculateRequiredCapital(signal);
    const availableCapital = portfolioManager ? portfolioManager.getAvailableCapital() : this.config.initialCapital;

    if (requiredCapital > availableCapital) {
      return false;
    }

    // Check daily loss limit
    const dailyLossLimit = this.config.dailyLossLimit || 5;
    const todayLoss = portfolioManager.getTodayLoss();

    if (todayLoss >= dailyLossLimit) {
      return false;
    }

    return true;
  }

  /**
   * Calculate required capital for signal
   */
  calculateRequiredCapital(signal) {
    const positionSizePercent = this.config.positionSizePercent || 2;
    const availableCapital = this.getAvailableCapital();
    let requiredCapital = availableCapital * (positionSizePercent / 100);

    // Apply market condition multiplier
    if (signal.marketCondition?.positionSizeMultiplier) {
      requiredCapital *= signal.marketCondition.positionSizeMultiplier;
    }

    return requiredCapital;
  }

  /**
   * Get available capital from portfolio manager
   */
  getAvailableCapital(portfolioManager = null) {
    if (portfolioManager) {
      return portfolioManager.getAvailableCapital();
    }
    // Fallback to config
    return this.config.initialCapital || 10000;
  }

  /**
   * Get execution statistics
   */
  getExecutionStatistics() {
    return {
      totalExecutions: this.executionHistory.length,
      successfulExecutions: this.executionHistory.filter(e => e.success).length,
      averageSlippage: this.calculateAverageSlippage(),
      averageLatency: this.calculateAverageLatency()
    };
  }

  /**
   * Calculate average slippage
   */
  calculateAverageSlippage() {
    const successfulExecutions = this.executionHistory.filter(e => e.success);
    if (successfulExecutions.length === 0) return 0;

    const totalSlippage = successfulExecutions.reduce((sum, e) => sum + e.slippage, 0);
    return totalSlippage / successfulExecutions.length;
  }

  /**
   * Calculate average latency
   */
  calculateAverageLatency() {
    const successfulExecutions = this.executionHistory.filter(e => e.success);
    if (successfulExecutions.length === 0) return 0;

    const totalLatency = successfulExecutions.reduce((sum, e) => sum + e.latency, 0);
    return totalLatency / successfulExecutions.length;
  }

  /**
   * Reset simulator state
   */
  reset() {
    this.tradeCounter = 0;
    this.executionHistory = [];
  }
}

module.exports = OrderSimulator;