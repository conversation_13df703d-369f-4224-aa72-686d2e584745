const EventEmitter = require('events');
const logger = require('../logger');
const PerformanceMetricsCalculator = require('../trading/performanceMetricsCalculator');

/**
 * Real-Time Monitor
 * Monitors system performance in real-time and emits events for alerts
 */
class RealTimeMonitor extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      // Monitoring intervals
      metricsUpdateInterval: config.metricsUpdateInterval || 30000, // 30 seconds
      performanceCheckInterval: config.performanceCheckInterval || 60000, // 1 minute
      systemHealthInterval: config.systemHealthInterval || 120000, // 2 minutes
      
      // Performance thresholds
      thresholds: {
        winRateWarning: config.thresholds?.winRateWarning || 40, // Below 40%
        winRateCritical: config.thresholds?.winRateCritical || 30, // Below 30%
        drawdownWarning: config.thresholds?.drawdownWarning || 10, // Above 10%
        drawdownCritical: config.thresholds?.drawdownCritical || 15, // Above 15%
        consecutiveLossesWarning: config.thresholds?.consecutiveLossesWarning || 5,
        consecutiveLossesCritical: config.thresholds?.consecutiveLossesCritical || 8,
        profitFactorWarning: config.thresholds?.profitFactorWarning || 1.2, // Below 1.2
        profitFactorCritical: config.thresholds?.profitFactorCritical || 1.0, // Below 1.0
        sharpeRatioWarning: config.thresholds?.sharpeRatioWarning || 0.5, // Below 0.5
        sharpeRatioCritical: config.thresholds?.sharpeRatioCritical || 0.0, // Below 0.0
        ...config.thresholds
      },

      // System health thresholds
      systemThresholds: {
        memoryUsageWarning: config.systemThresholds?.memoryUsageWarning || 80, // 80%
        memoryUsageCritical: config.systemThresholds?.memoryUsageCritical || 90, // 90%
        cpuUsageWarning: config.systemThresholds?.cpuUsageWarning || 70, // 70%
        cpuUsageCritical: config.systemThresholds?.cpuUsageCritical || 85, // 85%
        diskUsageWarning: config.systemThresholds?.diskUsageWarning || 80, // 80%
        diskUsageCritical: config.systemThresholds?.diskUsageCritical || 90, // 90%
        ...config.systemThresholds
      },

      // Data retention
      dataRetention: {
        realtimeMetrics: config.dataRetention?.realtimeMetrics || 24, // 24 hours
        performanceHistory: config.dataRetention?.performanceHistory || 168, // 7 days
        alertHistory: config.dataRetention?.alertHistory || 720, // 30 days
        ...config.dataRetention
      },

      ...config
    };

    // State management
    this.isMonitoring = false;
    this.currentMetrics = {};
    this.performanceHistory = [];
    this.alertHistory = [];
    this.systemHealth = {};
    this.consecutiveLosses = 0;
    this.lastTradeResult = null;

    // Timers
    this.metricsTimer = null;
    this.performanceTimer = null;
    this.systemHealthTimer = null;

    // Performance calculator
    this.metricsCalculator = new PerformanceMetricsCalculator();

    // Bind methods
    this.updateMetrics = this.updateMetrics.bind(this);
    this.checkPerformance = this.checkPerformance.bind(this);
    this.checkSystemHealth = this.checkSystemHealth.bind(this);
  }

  /**
   * Start real-time monitoring
   */
  async startMonitoring() {
    try {
      if (this.isMonitoring) {
        logger.warn('Real-time monitoring is already running');
        return;
      }

      this.isMonitoring = true;
      
      // Initialize current metrics
      await this.updateMetrics();
      
      // Start monitoring timers
      this.metricsTimer = setInterval(this.updateMetrics, this.config.metricsUpdateInterval);
      this.performanceTimer = setInterval(this.checkPerformance, this.config.performanceCheckInterval);
      this.systemHealthTimer = setInterval(this.checkSystemHealth, this.config.systemHealthInterval);

      logger.info('Real-time monitoring started', {
        metricsInterval: this.config.metricsUpdateInterval,
        performanceInterval: this.config.performanceCheckInterval,
        systemHealthInterval: this.config.systemHealthInterval
      });

      this.emit('monitoring_started');

    } catch (error) {
      logger.error('Error starting real-time monitoring', { error: error.message });
      throw error;
    }
  }

  /**
   * Stop real-time monitoring
   */
  stopMonitoring() {
    try {
      if (!this.isMonitoring) {
        return;
      }

      this.isMonitoring = false;

      // Clear timers
      if (this.metricsTimer) {
        clearInterval(this.metricsTimer);
        this.metricsTimer = null;
      }

      if (this.performanceTimer) {
        clearInterval(this.performanceTimer);
        this.performanceTimer = null;
      }

      if (this.systemHealthTimer) {
        clearInterval(this.systemHealthTimer);
        this.systemHealthTimer = null;
      }

      logger.info('Real-time monitoring stopped');
      this.emit('monitoring_stopped');

    } catch (error) {
      logger.error('Error stopping real-time monitoring', { error: error.message });
    }
  }

  /**
   * Update current metrics
   */
  async updateMetrics() {
    try {
      // Get recent trades (last 24 hours)
      const recentTrades = await this.getRecentTrades(24);
      
      if (recentTrades.length === 0) {
        this.currentMetrics = {
          timestamp: new Date(),
          totalTrades: 0,
          winRate: 0,
          profitFactor: 0,
          totalPnL: 0,
          sharpeRatio: 0,
          maxDrawdown: 0,
          consecutiveLosses: this.consecutiveLosses
        };
        return;
      }

      // Calculate comprehensive metrics
      const metrics = this.metricsCalculator.calculateComprehensiveMetrics(recentTrades);
      
      // Add real-time specific metrics
      this.currentMetrics = {
        ...metrics,
        timestamp: new Date(),
        consecutiveLosses: this.consecutiveLosses,
        lastTradeResult: this.lastTradeResult,
        tradesLast1Hour: this.getTradesInTimeframe(recentTrades, 1),
        tradesLast6Hours: this.getTradesInTimeframe(recentTrades, 6),
        tradesLast24Hours: recentTrades.length
      };

      // Add to performance history
      this.addToPerformanceHistory(this.currentMetrics);

      // Emit metrics update event
      this.emit('metrics_updated', this.currentMetrics);

    } catch (error) {
      logger.error('Error updating metrics', { error: error.message });
    }
  }

  /**
   * Check performance against thresholds
   */
  async checkPerformance() {
    try {
      if (!this.currentMetrics || this.currentMetrics.totalTrades === 0) {
        return;
      }

      const alerts = [];
      const metrics = this.currentMetrics;
      const thresholds = this.config.thresholds;

      // Check win rate
      if (metrics.winRate <= thresholds.winRateCritical) {
        alerts.push({
          type: 'performance',
          severity: 'critical',
          metric: 'win_rate',
          value: metrics.winRate,
          threshold: thresholds.winRateCritical,
          message: `Critical: Win rate dropped to ${metrics.winRate.toFixed(1)}%`
        });
      } else if (metrics.winRate <= thresholds.winRateWarning) {
        alerts.push({
          type: 'performance',
          severity: 'warning',
          metric: 'win_rate',
          value: metrics.winRate,
          threshold: thresholds.winRateWarning,
          message: `Warning: Win rate dropped to ${metrics.winRate.toFixed(1)}%`
        });
      }

      // Check drawdown
      if (metrics.maxDrawdown >= thresholds.drawdownCritical) {
        alerts.push({
          type: 'performance',
          severity: 'critical',
          metric: 'max_drawdown',
          value: metrics.maxDrawdown,
          threshold: thresholds.drawdownCritical,
          message: `Critical: Maximum drawdown reached ${metrics.maxDrawdown.toFixed(1)}%`
        });
      } else if (metrics.maxDrawdown >= thresholds.drawdownWarning) {
        alerts.push({
          type: 'performance',
          severity: 'warning',
          metric: 'max_drawdown',
          value: metrics.maxDrawdown,
          threshold: thresholds.drawdownWarning,
          message: `Warning: Maximum drawdown at ${metrics.maxDrawdown.toFixed(1)}%`
        });
      }

      // Check consecutive losses
      if (this.consecutiveLosses >= thresholds.consecutiveLossesCritical) {
        alerts.push({
          type: 'performance',
          severity: 'critical',
          metric: 'consecutive_losses',
          value: this.consecutiveLosses,
          threshold: thresholds.consecutiveLossesCritical,
          message: `Critical: ${this.consecutiveLosses} consecutive losses`
        });
      } else if (this.consecutiveLosses >= thresholds.consecutiveLossesWarning) {
        alerts.push({
          type: 'performance',
          severity: 'warning',
          metric: 'consecutive_losses',
          value: this.consecutiveLosses,
          threshold: thresholds.consecutiveLossesWarning,
          message: `Warning: ${this.consecutiveLosses} consecutive losses`
        });
      }

      // Check profit factor
      if (metrics.profitFactor <= thresholds.profitFactorCritical) {
        alerts.push({
          type: 'performance',
          severity: 'critical',
          metric: 'profit_factor',
          value: metrics.profitFactor,
          threshold: thresholds.profitFactorCritical,
          message: `Critical: Profit factor dropped to ${metrics.profitFactor.toFixed(2)}`
        });
      } else if (metrics.profitFactor <= thresholds.profitFactorWarning) {
        alerts.push({
          type: 'performance',
          severity: 'warning',
          metric: 'profit_factor',
          value: metrics.profitFactor,
          threshold: thresholds.profitFactorWarning,
          message: `Warning: Profit factor at ${metrics.profitFactor.toFixed(2)}`
        });
      }

      // Check Sharpe ratio
      if (metrics.sharpeRatio <= thresholds.sharpeRatioCritical) {
        alerts.push({
          type: 'performance',
          severity: 'critical',
          metric: 'sharpe_ratio',
          value: metrics.sharpeRatio,
          threshold: thresholds.sharpeRatioCritical,
          message: `Critical: Sharpe ratio dropped to ${metrics.sharpeRatio.toFixed(2)}`
        });
      } else if (metrics.sharpeRatio <= thresholds.sharpeRatioWarning) {
        alerts.push({
          type: 'performance',
          severity: 'warning',
          metric: 'sharpe_ratio',
          value: metrics.sharpeRatio,
          threshold: thresholds.sharpeRatioWarning,
          message: `Warning: Sharpe ratio at ${metrics.sharpeRatio.toFixed(2)}`
        });
      }

      // Process alerts
      for (const alert of alerts) {
        await this.processAlert(alert);
      }

    } catch (error) {
      logger.error('Error checking performance', { error: error.message });
    }
  }

  /**
   * Check system health
   */
  async checkSystemHealth() {
    try {
      const systemHealth = await this.getSystemHealth();
      this.systemHealth = systemHealth;

      const alerts = [];
      const thresholds = this.config.systemThresholds;

      // Check memory usage
      if (systemHealth.memoryUsage >= thresholds.memoryUsageCritical) {
        alerts.push({
          type: 'system',
          severity: 'critical',
          metric: 'memory_usage',
          value: systemHealth.memoryUsage,
          threshold: thresholds.memoryUsageCritical,
          message: `Critical: Memory usage at ${systemHealth.memoryUsage.toFixed(1)}%`
        });
      } else if (systemHealth.memoryUsage >= thresholds.memoryUsageWarning) {
        alerts.push({
          type: 'system',
          severity: 'warning',
          metric: 'memory_usage',
          value: systemHealth.memoryUsage,
          threshold: thresholds.memoryUsageWarning,
          message: `Warning: Memory usage at ${systemHealth.memoryUsage.toFixed(1)}%`
        });
      }

      // Check CPU usage
      if (systemHealth.cpuUsage >= thresholds.cpuUsageCritical) {
        alerts.push({
          type: 'system',
          severity: 'critical',
          metric: 'cpu_usage',
          value: systemHealth.cpuUsage,
          threshold: thresholds.cpuUsageCritical,
          message: `Critical: CPU usage at ${systemHealth.cpuUsage.toFixed(1)}%`
        });
      } else if (systemHealth.cpuUsage >= thresholds.cpuUsageWarning) {
        alerts.push({
          type: 'system',
          severity: 'warning',
          metric: 'cpu_usage',
          value: systemHealth.cpuUsage,
          threshold: thresholds.cpuUsageWarning,
          message: `Warning: CPU usage at ${systemHealth.cpuUsage.toFixed(1)}%`
        });
      }

      // Process alerts
      for (const alert of alerts) {
        await this.processAlert(alert);
      }

      this.emit('system_health_updated', systemHealth);

    } catch (error) {
      logger.error('Error checking system health', { error: error.message });
    }
  }

  /**
   * Process an alert
   */
  async processAlert(alert) {
    try {
      // Add timestamp and ID
      alert.id = this.generateAlertId();
      alert.timestamp = new Date();

      // Check if this is a duplicate alert (same type and metric within last 5 minutes)
      const isDuplicate = this.alertHistory.some(existingAlert => 
        existingAlert.type === alert.type &&
        existingAlert.metric === alert.metric &&
        existingAlert.severity === alert.severity &&
        (Date.now() - existingAlert.timestamp.getTime()) < 5 * 60 * 1000
      );

      if (isDuplicate) {
        return; // Skip duplicate alerts
      }

      // Add to alert history
      this.alertHistory.push(alert);

      // Emit alert event
      this.emit('alert', alert);

      logger.warn('Performance alert triggered', {
        type: alert.type,
        severity: alert.severity,
        metric: alert.metric,
        value: alert.value,
        message: alert.message
      });

    } catch (error) {
      logger.error('Error processing alert', { error: error.message });
    }
  }

  /**
   * Update trade result for consecutive loss tracking
   */
  updateTradeResult(tradeResult) {
    try {
      this.lastTradeResult = tradeResult;

      if (tradeResult.pnlPercent > 0) {
        // Winning trade - reset consecutive losses
        this.consecutiveLosses = 0;
      } else if (tradeResult.pnlPercent < 0) {
        // Losing trade - increment consecutive losses
        this.consecutiveLosses++;
      }

      // Emit trade result event
      this.emit('trade_result', {
        result: tradeResult,
        consecutiveLosses: this.consecutiveLosses
      });

    } catch (error) {
      logger.error('Error updating trade result', { error: error.message });
    }
  }

  /**
   * Get recent trades from database
   */
  async getRecentTrades(hours) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      const trades = await TradingSignal.find({
        status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] },
        updatedAt: { $gte: cutoffTime },
        isConflictNotification: false
      }).sort({ updatedAt: -1 });

      return trades.map(trade => ({
        pnlPercent: trade.pnlPercent || 0,
        createdAt: trade.createdAt,
        updatedAt: trade.updatedAt,
        symbol: trade.symbol,
        exitReason: trade.status
      }));

    } catch (error) {
      logger.error('Error getting recent trades', { error: error.message });
      return [];
    }
  }

  /**
   * Get trades in specific timeframe
   */
  getTradesInTimeframe(trades, hours) {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return trades.filter(trade => new Date(trade.updatedAt) >= cutoffTime).length;
  }

  /**
   * Add metrics to performance history
   */
  addToPerformanceHistory(metrics) {
    this.performanceHistory.push({
      timestamp: metrics.timestamp,
      winRate: metrics.winRate,
      profitFactor: metrics.profitFactor,
      totalPnL: metrics.totalPnL,
      maxDrawdown: metrics.maxDrawdown,
      sharpeRatio: metrics.sharpeRatio,
      consecutiveLosses: metrics.consecutiveLosses
    });

    // Keep only recent history
    const cutoffTime = new Date(Date.now() - this.config.dataRetention.performanceHistory * 60 * 60 * 1000);
    this.performanceHistory = this.performanceHistory.filter(
      entry => entry.timestamp >= cutoffTime
    );
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth() {
    try {
      const process = require('process');
      const os = require('os');

      // Memory usage
      const memUsage = process.memoryUsage();
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      const memoryUsage = (usedMemory / totalMemory) * 100;

      // CPU usage (simplified)
      const cpuUsage = os.loadavg()[0] * 10; // Rough approximation

      return {
        timestamp: new Date(),
        memoryUsage: Math.round(memoryUsage * 100) / 100,
        cpuUsage: Math.min(Math.round(cpuUsage * 100) / 100, 100),
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: os.platform(),
        architecture: os.arch()
      };

    } catch (error) {
      logger.error('Error getting system health', { error: error.message });
      return {
        timestamp: new Date(),
        memoryUsage: 0,
        cpuUsage: 0,
        uptime: 0,
        error: 'Unable to get system health'
      };
    }
  }

  /**
   * Generate unique alert ID
   */
  generateAlertId() {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current monitoring status
   */
  getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      currentMetrics: this.currentMetrics,
      systemHealth: this.systemHealth,
      consecutiveLosses: this.consecutiveLosses,
      lastTradeResult: this.lastTradeResult,
      alertCount: this.alertHistory.length,
      performanceHistoryLength: this.performanceHistory.length
    };
  }

  /**
   * Get performance history
   */
  getPerformanceHistory(hours = 24) {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.performanceHistory.filter(entry => entry.timestamp >= cutoffTime);
  }

  /**
   * Get alert history
   */
  getAlertHistory(hours = 24) {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alertHistory.filter(alert => alert.timestamp >= cutoffTime);
  }

  /**
   * Clear alert history
   */
  clearAlertHistory() {
    this.alertHistory = [];
    logger.info('Alert history cleared');
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    logger.info('Real-time monitor configuration updated');
  }
}

module.exports = RealTimeMonitor;
