const express = require('express');
const logger = require('../logger');

/**
 * Dashboard API
 * Provides REST API endpoints for real-time monitoring dashboard
 */
class DashboardAPI {
  constructor(realTimeMonitor, alertSystem, config = {}) {
    this.realTimeMonitor = realTimeMonitor;
    this.alertSystem = alertSystem;
    this.config = {
      basePath: config.basePath || '/api/v1/dashboard',
      enableCORS: config.enableCORS || true,
      rateLimit: config.rateLimit || {
        windowMs: 60000, // 1 minute
        max: 100 // 100 requests per minute
      },
      ...config
    };

    this.router = express.Router();
    this.setupRoutes();
    this.setupMiddleware();
  }

  /**
   * Setup middleware
   */
  setupMiddleware() {
    // CORS middleware
    if (this.config.enableCORS) {
      this.router.use((req, res, next) => {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
        
        if (req.method === 'OPTIONS') {
          res.sendStatus(200);
        } else {
          next();
        }
      });
    }

    // JSON middleware
    this.router.use(express.json());

    // Request logging
    this.router.use((req, res, next) => {
      logger.debug(`Dashboard API request: ${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });

    // Error handling middleware
    this.router.use((error, req, res, next) => {
      logger.error('Dashboard API error', {
        error: error.message,
        path: req.path,
        method: req.method
      });

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Dashboard overview
    this.router.get('/overview', this.handleOverview.bind(this));

    // Real-time metrics
    this.router.get('/metrics', this.handleMetrics.bind(this));
    this.router.get('/metrics/history', this.handleMetricsHistory.bind(this));

    // System health
    this.router.get('/health', this.handleSystemHealth.bind(this));

    // Alerts
    this.router.get('/alerts', this.handleAlerts.bind(this));
    this.router.get('/alerts/active', this.handleActiveAlerts.bind(this));
    this.router.get('/alerts/history', this.handleAlertHistory.bind(this));
    this.router.get('/alerts/statistics', this.handleAlertStatistics.bind(this));
    this.router.post('/alerts/:alertId/resolve', this.handleResolveAlert.bind(this));

    // Performance analytics
    this.router.get('/performance', this.handlePerformance.bind(this));
    this.router.get('/performance/symbols', this.handleSymbolPerformance.bind(this));
    this.router.get('/performance/timeframes', this.handleTimeframePerformance.bind(this));

    // Trading signals
    this.router.get('/signals/active', this.handleActiveSignals.bind(this));
    this.router.get('/signals/recent', this.handleRecentSignals.bind(this));

    // Configuration
    this.router.get('/config', this.handleGetConfig.bind(this));
    this.router.put('/config', this.handleUpdateConfig.bind(this));

    // Status and monitoring
    this.router.get('/status', this.handleStatus.bind(this));
    this.router.post('/monitoring/start', this.handleStartMonitoring.bind(this));
    this.router.post('/monitoring/stop', this.handleStopMonitoring.bind(this));
  }

  /**
   * Handle dashboard overview
   */
  async handleOverview(req, res) {
    try {
      const monitoringStatus = this.realTimeMonitor.getMonitoringStatus();
      const alertStats = this.alertSystem.getAlertStatistics(24);
      const activeAlerts = this.alertSystem.getActiveAlerts();

      const overview = {
        timestamp: new Date().toISOString(),
        monitoring: {
          isActive: monitoringStatus.isMonitoring,
          consecutiveLosses: monitoringStatus.consecutiveLosses,
          lastTradeResult: monitoringStatus.lastTradeResult
        },
        metrics: monitoringStatus.currentMetrics || {},
        systemHealth: monitoringStatus.systemHealth || {},
        alerts: {
          active: activeAlerts.length,
          critical: activeAlerts.filter(a => a.severity === 'critical').length,
          warning: activeAlerts.filter(a => a.severity === 'warning').length,
          last24h: alertStats.total
        },
        performance: {
          winRate: monitoringStatus.currentMetrics?.winRate || 0,
          profitFactor: monitoringStatus.currentMetrics?.profitFactor || 0,
          totalPnL: monitoringStatus.currentMetrics?.totalPnL || 0,
          maxDrawdown: monitoringStatus.currentMetrics?.maxDrawdown || 0
        }
      };

      res.json({
        success: true,
        data: overview
      });

    } catch (error) {
      logger.error('Error handling overview request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get dashboard overview'
      });
    }
  }

  /**
   * Handle current metrics
   */
  async handleMetrics(req, res) {
    try {
      const monitoringStatus = this.realTimeMonitor.getMonitoringStatus();
      
      res.json({
        success: true,
        data: {
          timestamp: new Date().toISOString(),
          metrics: monitoringStatus.currentMetrics || {},
          isMonitoring: monitoringStatus.isMonitoring
        }
      });

    } catch (error) {
      logger.error('Error handling metrics request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get current metrics'
      });
    }
  }

  /**
   * Handle metrics history
   */
  async handleMetricsHistory(req, res) {
    try {
      const hours = parseInt(req.query.hours) || 24;
      const history = this.realTimeMonitor.getPerformanceHistory(hours);

      res.json({
        success: true,
        data: {
          history: history,
          period: `${hours} hours`,
          count: history.length
        }
      });

    } catch (error) {
      logger.error('Error handling metrics history request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get metrics history'
      });
    }
  }

  /**
   * Handle system health
   */
  async handleSystemHealth(req, res) {
    try {
      const monitoringStatus = this.realTimeMonitor.getMonitoringStatus();
      
      res.json({
        success: true,
        data: {
          timestamp: new Date().toISOString(),
          systemHealth: monitoringStatus.systemHealth || {},
          monitoring: {
            isActive: monitoringStatus.isMonitoring,
            uptime: process.uptime()
          }
        }
      });

    } catch (error) {
      logger.error('Error handling system health request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get system health'
      });
    }
  }

  /**
   * Handle alerts overview
   */
  async handleAlerts(req, res) {
    try {
      const hours = parseInt(req.query.hours) || 24;
      const statistics = this.alertSystem.getAlertStatistics(hours);
      const activeAlerts = this.alertSystem.getActiveAlerts();

      res.json({
        success: true,
        data: {
          statistics: statistics,
          activeAlerts: activeAlerts,
          period: `${hours} hours`
        }
      });

    } catch (error) {
      logger.error('Error handling alerts request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get alerts'
      });
    }
  }

  /**
   * Handle active alerts
   */
  async handleActiveAlerts(req, res) {
    try {
      const activeAlerts = this.alertSystem.getActiveAlerts();

      res.json({
        success: true,
        data: {
          alerts: activeAlerts,
          count: activeAlerts.length
        }
      });

    } catch (error) {
      logger.error('Error handling active alerts request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get active alerts'
      });
    }
  }

  /**
   * Handle alert history
   */
  async handleAlertHistory(req, res) {
    try {
      const hours = parseInt(req.query.hours) || 24;
      const limit = parseInt(req.query.limit) || 100;
      const severity = req.query.severity;

      let history = this.alertSystem.getAlertHistory(hours);

      // Filter by severity if specified
      if (severity) {
        history = history.filter(alert => alert.severity === severity);
      }

      // Limit results
      history = history.slice(0, limit);

      res.json({
        success: true,
        data: {
          alerts: history,
          count: history.length,
          period: `${hours} hours`,
          filters: { severity, limit }
        }
      });

    } catch (error) {
      logger.error('Error handling alert history request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get alert history'
      });
    }
  }

  /**
   * Handle alert statistics
   */
  async handleAlertStatistics(req, res) {
    try {
      const hours = parseInt(req.query.hours) || 24;
      const statistics = this.alertSystem.getAlertStatistics(hours);

      res.json({
        success: true,
        data: {
          statistics: statistics,
          period: `${hours} hours`
        }
      });

    } catch (error) {
      logger.error('Error handling alert statistics request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get alert statistics'
      });
    }
  }

  /**
   * Handle resolve alert
   */
  async handleResolveAlert(req, res) {
    try {
      const alertId = req.params.alertId;
      const resolution = req.body.resolution || 'manual';

      const success = await this.alertSystem.resolveAlert(alertId, resolution);

      if (success) {
        res.json({
          success: true,
          message: 'Alert resolved successfully',
          alertId: alertId
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Alert not found or already resolved'
        });
      }

    } catch (error) {
      logger.error('Error handling resolve alert request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to resolve alert'
      });
    }
  }

  /**
   * Handle performance data
   */
  async handlePerformance(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      
      // Get performance data from statistics service
      const statisticsService = require('../services/statisticsService');
      const performance = await statisticsService.getOverallStatistics(days);

      res.json({
        success: true,
        data: {
          performance: performance,
          period: `${days} days`
        }
      });

    } catch (error) {
      logger.error('Error handling performance request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get performance data'
      });
    }
  }

  /**
   * Handle symbol performance
   */
  async handleSymbolPerformance(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      const limit = parseInt(req.query.limit) || 10;

      const statisticsService = require('../services/statisticsService');
      const symbolPerformance = await statisticsService.getSymbolStatistics(days, limit);

      res.json({
        success: true,
        data: {
          symbols: symbolPerformance,
          period: `${days} days`,
          limit: limit
        }
      });

    } catch (error) {
      logger.error('Error handling symbol performance request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get symbol performance'
      });
    }
  }

  /**
   * Handle timeframe performance
   */
  async handleTimeframePerformance(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;

      const statisticsService = require('../services/statisticsService');
      const timeframePerformance = await statisticsService.getTimeframeStatistics(days);

      res.json({
        success: true,
        data: {
          timeframes: timeframePerformance,
          period: `${days} days`
        }
      });

    } catch (error) {
      logger.error('Error handling timeframe performance request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get timeframe performance'
      });
    }
  }

  /**
   * Handle active signals
   */
  async handleActiveSignals(req, res) {
    try {
      const orderManager = require('../trading/orderManager');
      const monitoringStatus = await orderManager.getMonitoringStatus();

      res.json({
        success: true,
        data: {
          signals: monitoringStatus.activeSignals || [],
          count: monitoringStatus.activeSignalsCount || 0,
          monitoring: monitoringStatus.isActive || false
        }
      });

    } catch (error) {
      logger.error('Error handling active signals request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get active signals'
      });
    }
  }

  /**
   * Handle recent signals
   */
  async handleRecentSignals(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 20;
      const hours = parseInt(req.query.hours) || 24;

      const TradingSignal = require('../models/tradingSignal');
      const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      const signals = await TradingSignal.find({
        createdAt: { $gte: cutoffTime },
        isConflictNotification: false
      })
      .sort({ createdAt: -1 })
      .limit(limit);

      res.json({
        success: true,
        data: {
          signals: signals,
          count: signals.length,
          period: `${hours} hours`
        }
      });

    } catch (error) {
      logger.error('Error handling recent signals request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get recent signals'
      });
    }
  }

  /**
   * Handle get configuration
   */
  async handleGetConfig(req, res) {
    try {
      const config = {
        monitoring: {
          thresholds: this.realTimeMonitor.config.thresholds,
          intervals: {
            metricsUpdate: this.realTimeMonitor.config.metricsUpdateInterval,
            performanceCheck: this.realTimeMonitor.config.performanceCheckInterval,
            systemHealth: this.realTimeMonitor.config.systemHealthInterval
          }
        },
        alerts: {
          channels: this.alertSystem.config.channels,
          rules: this.alertSystem.config.rules,
          priorities: this.alertSystem.config.priorities
        }
      };

      res.json({
        success: true,
        data: config
      });

    } catch (error) {
      logger.error('Error handling get config request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get configuration'
      });
    }
  }

  /**
   * Handle update configuration
   */
  async handleUpdateConfig(req, res) {
    try {
      const { monitoring, alerts } = req.body;

      if (monitoring) {
        this.realTimeMonitor.updateConfig(monitoring);
      }

      if (alerts) {
        this.alertSystem.updateConfig(alerts);
      }

      res.json({
        success: true,
        message: 'Configuration updated successfully'
      });

    } catch (error) {
      logger.error('Error handling update config request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to update configuration'
      });
    }
  }

  /**
   * Handle status
   */
  async handleStatus(req, res) {
    try {
      const status = {
        timestamp: new Date().toISOString(),
        monitoring: this.realTimeMonitor.getMonitoringStatus(),
        alerts: {
          enabled: this.alertSystem.isEnabled,
          active: this.alertSystem.getActiveAlerts().length,
          statistics: this.alertSystem.getAlertStatistics(1) // Last hour
        },
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.version
        }
      };

      res.json({
        success: true,
        data: status
      });

    } catch (error) {
      logger.error('Error handling status request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to get status'
      });
    }
  }

  /**
   * Handle start monitoring
   */
  async handleStartMonitoring(req, res) {
    try {
      await this.realTimeMonitor.startMonitoring();

      res.json({
        success: true,
        message: 'Monitoring started successfully'
      });

    } catch (error) {
      logger.error('Error handling start monitoring request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to start monitoring'
      });
    }
  }

  /**
   * Handle stop monitoring
   */
  async handleStopMonitoring(req, res) {
    try {
      this.realTimeMonitor.stopMonitoring();

      res.json({
        success: true,
        message: 'Monitoring stopped successfully'
      });

    } catch (error) {
      logger.error('Error handling stop monitoring request', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'Failed to stop monitoring'
      });
    }
  }

  /**
   * Get Express router
   */
  getRouter() {
    return this.router;
  }
}

module.exports = DashboardAPI;
