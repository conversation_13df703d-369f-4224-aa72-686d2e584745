const EventEmitter = require('events');
const logger = require('../logger');

/**
 * Alert System
 * Manages alerts, notifications, and escalation procedures
 */
class AlertSystem extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      // Alert channels
      channels: {
        telegram: config.channels?.telegram || true,
        email: config.channels?.email || false,
        webhook: config.channels?.webhook || false,
        console: config.channels?.console || true,
        ...config.channels
      },

      // Alert rules
      rules: {
        // Escalation rules
        escalation: {
          enabled: config.rules?.escalation?.enabled || true,
          warningToleranceMinutes: config.rules?.escalation?.warningToleranceMinutes || 30,
          criticalToleranceMinutes: config.rules?.escalation?.criticalToleranceMinutes || 10,
          maxEscalationLevel: config.rules?.escalation?.maxEscalationLevel || 3
        },

        // Rate limiting
        rateLimit: {
          enabled: config.rules?.rateLimit?.enabled || true,
          maxAlertsPerHour: config.rules?.rateLimit?.maxAlertsPerHour || 20,
          maxSameAlertsPer5Min: config.rules?.rateLimit?.maxSameAlertsPer5Min || 2
        },

        // Quiet hours
        quietHours: {
          enabled: config.rules?.quietHours?.enabled || false,
          startHour: config.rules?.quietHours?.startHour || 23, // 11 PM
          endHour: config.rules?.quietHours?.endHour || 7,     // 7 AM
          timezone: config.rules?.quietHours?.timezone || 'UTC'
        },

        ...config.rules
      },

      // Alert priorities
      priorities: {
        critical: {
          color: '#FF0000',
          emoji: '🚨',
          urgency: 'immediate',
          escalate: true
        },
        warning: {
          color: '#FFA500',
          emoji: '⚠️',
          urgency: 'normal',
          escalate: false
        },
        info: {
          color: '#0000FF',
          emoji: 'ℹ️',
          urgency: 'low',
          escalate: false
        },
        ...config.priorities
      },

      ...config
    };

    // State management
    this.activeAlerts = new Map();
    this.alertHistory = [];
    this.escalatedAlerts = new Map();
    this.rateLimitCounters = new Map();
    this.isEnabled = true;

    // Notification handlers
    this.notificationHandlers = new Map();
    this.setupDefaultHandlers();

    // Cleanup timer
    this.cleanupTimer = setInterval(() => this.cleanup(), 60000); // Every minute
  }

  /**
   * Setup default notification handlers
   */
  setupDefaultHandlers() {
    // Console handler
    this.notificationHandlers.set('console', async (alert) => {
      const priority = this.config.priorities[alert.severity] || this.config.priorities.info;
      console.log(`${priority.emoji} [${alert.severity.toUpperCase()}] ${alert.message}`);
    });

    // Telegram handler (if available)
    try {
      const telegramBot = require('../trading/telegramBot');
      this.notificationHandlers.set('telegram', async (alert) => {
        const message = this.formatTelegramAlert(alert);
        await telegramBot.sendMessage(message);
      });
    } catch (error) {
      logger.warn('Telegram bot not available for alerts');
    }
  }

  /**
   * Process an alert
   */
  async processAlert(alert) {
    try {
      if (!this.isEnabled) {
        return;
      }

      // Validate alert
      if (!this.validateAlert(alert)) {
        logger.warn('Invalid alert received', { alert });
        return;
      }

      // Check rate limiting
      if (this.isRateLimited(alert)) {
        logger.debug('Alert rate limited', { 
          type: alert.type, 
          metric: alert.metric 
        });
        return;
      }

      // Check quiet hours
      if (this.isQuietHours() && alert.severity !== 'critical') {
        logger.debug('Alert suppressed due to quiet hours', { alert });
        return;
      }

      // Add alert metadata
      alert.id = alert.id || this.generateAlertId();
      alert.timestamp = alert.timestamp || new Date();
      alert.processed = new Date();

      // Store active alert
      this.activeAlerts.set(alert.id, alert);

      // Add to history
      this.alertHistory.push(alert);

      // Send notifications
      await this.sendNotifications(alert);

      // Handle escalation
      if (this.shouldEscalate(alert)) {
        await this.escalateAlert(alert);
      }

      // Emit alert event
      this.emit('alert_processed', alert);

      logger.info('Alert processed', {
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        metric: alert.metric
      });

    } catch (error) {
      logger.error('Error processing alert', { 
        error: error.message, 
        alert: alert 
      });
    }
  }

  /**
   * Send notifications through configured channels
   */
  async sendNotifications(alert) {
    const enabledChannels = Object.entries(this.config.channels)
      .filter(([channel, enabled]) => enabled)
      .map(([channel]) => channel);

    const notificationPromises = enabledChannels.map(async (channel) => {
      try {
        const handler = this.notificationHandlers.get(channel);
        if (handler) {
          await handler(alert);
          logger.debug(`Alert sent via ${channel}`, { alertId: alert.id });
        } else {
          logger.warn(`No handler found for channel: ${channel}`);
        }
      } catch (error) {
        logger.error(`Error sending alert via ${channel}`, { 
          error: error.message, 
          alertId: alert.id 
        });
      }
    });

    await Promise.allSettled(notificationPromises);
  }

  /**
   * Escalate an alert
   */
  async escalateAlert(alert) {
    try {
      const escalationKey = `${alert.type}_${alert.metric}`;
      let escalationLevel = 1;

      if (this.escalatedAlerts.has(escalationKey)) {
        const existing = this.escalatedAlerts.get(escalationKey);
        escalationLevel = existing.level + 1;
      }

      if (escalationLevel > this.config.rules.escalation.maxEscalationLevel) {
        logger.warn('Maximum escalation level reached', { 
          alert: alert.id, 
          level: escalationLevel 
        });
        return;
      }

      const escalatedAlert = {
        ...alert,
        id: this.generateAlertId(),
        escalationLevel: escalationLevel,
        originalAlertId: alert.id,
        escalated: true,
        severity: 'critical', // Escalated alerts are always critical
        message: `ESCALATED (Level ${escalationLevel}): ${alert.message}`
      };

      // Store escalation info
      this.escalatedAlerts.set(escalationKey, {
        level: escalationLevel,
        timestamp: new Date(),
        originalAlert: alert.id
      });

      // Process escalated alert
      await this.sendNotifications(escalatedAlert);

      this.emit('alert_escalated', escalatedAlert);

      logger.warn('Alert escalated', {
        originalId: alert.id,
        escalatedId: escalatedAlert.id,
        level: escalationLevel
      });

    } catch (error) {
      logger.error('Error escalating alert', { 
        error: error.message, 
        alertId: alert.id 
      });
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId, resolution = 'manual') {
    try {
      const alert = this.activeAlerts.get(alertId);
      if (!alert) {
        logger.warn('Alert not found for resolution', { alertId });
        return false;
      }

      // Mark as resolved
      alert.resolved = true;
      alert.resolvedAt = new Date();
      alert.resolution = resolution;

      // Remove from active alerts
      this.activeAlerts.delete(alertId);

      // Clear escalation if exists
      const escalationKey = `${alert.type}_${alert.metric}`;
      this.escalatedAlerts.delete(escalationKey);

      // Send resolution notification for critical alerts
      if (alert.severity === 'critical') {
        const resolutionAlert = {
          id: this.generateAlertId(),
          type: 'resolution',
          severity: 'info',
          message: `✅ RESOLVED: ${alert.message}`,
          originalAlertId: alertId,
          resolution: resolution,
          timestamp: new Date()
        };

        await this.sendNotifications(resolutionAlert);
      }

      this.emit('alert_resolved', alert);

      logger.info('Alert resolved', {
        alertId: alertId,
        resolution: resolution
      });

      return true;

    } catch (error) {
      logger.error('Error resolving alert', { 
        error: error.message, 
        alertId 
      });
      return false;
    }
  }

  /**
   * Validate alert structure
   */
  validateAlert(alert) {
    const requiredFields = ['type', 'severity', 'message'];
    return requiredFields.every(field => alert.hasOwnProperty(field));
  }

  /**
   * Check if alert is rate limited
   */
  isRateLimited(alert) {
    if (!this.config.rules.rateLimit.enabled) {
      return false;
    }

    const now = Date.now();
    const hourKey = `hour_${Math.floor(now / (60 * 60 * 1000))}`;
    const fiveMinKey = `5min_${Math.floor(now / (5 * 60 * 1000))}_${alert.type}_${alert.metric}`;

    // Check hourly limit
    const hourlyCount = this.rateLimitCounters.get(hourKey) || 0;
    if (hourlyCount >= this.config.rules.rateLimit.maxAlertsPerHour) {
      return true;
    }

    // Check 5-minute same alert limit
    const fiveMinCount = this.rateLimitCounters.get(fiveMinKey) || 0;
    if (fiveMinCount >= this.config.rules.rateLimit.maxSameAlertsPer5Min) {
      return true;
    }

    // Update counters
    this.rateLimitCounters.set(hourKey, hourlyCount + 1);
    this.rateLimitCounters.set(fiveMinKey, fiveMinCount + 1);

    return false;
  }

  /**
   * Check if currently in quiet hours
   */
  isQuietHours() {
    if (!this.config.rules.quietHours.enabled) {
      return false;
    }

    const now = new Date();
    const currentHour = now.getUTCHours(); // Using UTC for simplicity
    const startHour = this.config.rules.quietHours.startHour;
    const endHour = this.config.rules.quietHours.endHour;

    if (startHour < endHour) {
      // Same day quiet hours (e.g., 14:00 - 18:00)
      return currentHour >= startHour && currentHour < endHour;
    } else {
      // Overnight quiet hours (e.g., 23:00 - 07:00)
      return currentHour >= startHour || currentHour < endHour;
    }
  }

  /**
   * Check if alert should be escalated
   */
  shouldEscalate(alert) {
    if (!this.config.rules.escalation.enabled) {
      return false;
    }

    const priority = this.config.priorities[alert.severity];
    if (!priority || !priority.escalate) {
      return false;
    }

    const escalationKey = `${alert.type}_${alert.metric}`;
    const existing = this.escalatedAlerts.get(escalationKey);

    if (!existing) {
      // First occurrence - check if we should escalate based on time
      const toleranceMinutes = alert.severity === 'critical' 
        ? this.config.rules.escalation.criticalToleranceMinutes
        : this.config.rules.escalation.warningToleranceMinutes;

      // For now, escalate immediately for critical alerts
      return alert.severity === 'critical';
    }

    // Check if enough time has passed for re-escalation
    const timeSinceLastEscalation = Date.now() - existing.timestamp.getTime();
    const escalationInterval = 15 * 60 * 1000; // 15 minutes

    return timeSinceLastEscalation > escalationInterval;
  }

  /**
   * Format alert for Telegram
   */
  formatTelegramAlert(alert) {
    const priority = this.config.priorities[alert.severity] || this.config.priorities.info;
    
    let message = `${priority.emoji} *${alert.severity.toUpperCase()} ALERT*\n\n`;
    message += `📊 *Type:* ${alert.type}\n`;
    
    if (alert.metric) {
      message += `📈 *Metric:* ${alert.metric}\n`;
    }
    
    if (alert.value !== undefined) {
      message += `🔢 *Value:* ${alert.value}\n`;
    }
    
    if (alert.threshold !== undefined) {
      message += `⚡ *Threshold:* ${alert.threshold}\n`;
    }
    
    message += `\n💬 *Message:* ${alert.message}\n`;
    message += `⏰ *Time:* ${alert.timestamp.toISOString()}`;

    if (alert.escalationLevel) {
      message += `\n🚨 *Escalation Level:* ${alert.escalationLevel}`;
    }

    return message;
  }

  /**
   * Generate unique alert ID
   */
  generateAlertId() {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup old data
   */
  cleanup() {
    try {
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;
      const oneDay = 24 * oneHour;

      // Clean rate limit counters (older than 1 hour)
      for (const [key, value] of this.rateLimitCounters.entries()) {
        if (key.startsWith('hour_') || key.startsWith('5min_')) {
          // Extract timestamp from key and check if old
          const keyTime = key.includes('hour_') 
            ? parseInt(key.split('_')[1]) * oneHour
            : parseInt(key.split('_')[1]) * 5 * 60 * 1000;
          
          if (now - keyTime > oneHour) {
            this.rateLimitCounters.delete(key);
          }
        }
      }

      // Clean old escalations (older than 1 day)
      for (const [key, escalation] of this.escalatedAlerts.entries()) {
        if (now - escalation.timestamp.getTime() > oneDay) {
          this.escalatedAlerts.delete(key);
        }
      }

      // Clean old alert history (keep last 1000 alerts)
      if (this.alertHistory.length > 1000) {
        this.alertHistory = this.alertHistory.slice(-1000);
      }

    } catch (error) {
      logger.error('Error during alert system cleanup', { error: error.message });
    }
  }

  /**
   * Get alert statistics
   */
  getAlertStatistics(hours = 24) {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentAlerts = this.alertHistory.filter(alert => alert.timestamp >= cutoffTime);

    const stats = {
      total: recentAlerts.length,
      critical: recentAlerts.filter(a => a.severity === 'critical').length,
      warning: recentAlerts.filter(a => a.severity === 'warning').length,
      info: recentAlerts.filter(a => a.severity === 'info').length,
      escalated: recentAlerts.filter(a => a.escalated).length,
      resolved: recentAlerts.filter(a => a.resolved).length,
      active: this.activeAlerts.size,
      byType: {},
      byMetric: {}
    };

    // Group by type
    recentAlerts.forEach(alert => {
      stats.byType[alert.type] = (stats.byType[alert.type] || 0) + 1;
      if (alert.metric) {
        stats.byMetric[alert.metric] = (stats.byMetric[alert.metric] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts() {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * Get alert history
   */
  getAlertHistory(hours = 24) {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alertHistory.filter(alert => alert.timestamp >= cutoffTime);
  }

  /**
   * Enable/disable alert system
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    logger.info(`Alert system ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Add custom notification handler
   */
  addNotificationHandler(channel, handler) {
    this.notificationHandlers.set(channel, handler);
    logger.info(`Notification handler added for channel: ${channel}`);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    logger.info('Alert system configuration updated');
  }

  /**
   * Destroy alert system
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.removeAllListeners();
    logger.info('Alert system destroyed');
  }
}

module.exports = AlertSystem;
