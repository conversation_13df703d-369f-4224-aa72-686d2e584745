/**
 * Test script để kiểm tra việc không spam notification
 * Đảm bảo mỗi loại thông báo chỉ gửi 1 lần
 */

// Mock config và logger
global.config = {
  trading: {
    riskManagement: {
      stopLossPercent: 0.5,
      minRiskReward: 1.2,
      dynamicTP: { enabled: true }
    }
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

// Mock TradingSignal model
const mockTradingSignal = {
  findByIdAndUpdate: async (id, update) => {
    // Silent mock - không log để test notification
    return true;
  }
};

// Mock Telegram Bot
const mockTelegramBot = {
  config: { chatId: 'test-chat' },
  bot: {
    sendMessage: async (chatId, message, options) => {
      console.log(`📱 TELEGRAM NOTIFICATION SENT:`);
      console.log(`   Chat: ${chatId}`);
      console.log(`   Message: ${message.split('\n')[0]}`); // Chỉ log dòng đầu
      return { message_id: Date.now() };
    }
  }
};

// Mock require
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === '../models/tradingSignal') {
    return mockTradingSignal;
  }
  if (id === './telegramBot') {
    return mockTelegramBot;
  }
  return originalRequire.apply(this, arguments);
};

const TrailingManager = require('./lib/trading/trailingManager');

async function testNotificationSpam() {
  console.log('🧪 Testing Notification Spam Prevention\n');

  const sellSignal = {
    _id: 'test-spam-prevention',
    symbol: 'SAPIENUSDT',
    type: 'SELL',
    entry: 0.156000,
    stopLoss: 0.160000,
    takeProfit: 0.152000
  };

  console.log('📊 SELL Signal Setup:');
  console.log(`  Entry: ${sellSignal.entry}`);
  console.log(`  Original SL: ${sellSignal.stopLoss}`);
  console.log(`  Original TP: ${sellSignal.takeProfit}`);

  // Add signal to trailing
  TrailingManager.addSignalToTrailing(sellSignal);

  console.log('\n🎯 Test 1: Multiple Breakeven Triggers (Should Only Send 1 Notification)');
  console.log('=======================================================================');

  // Trigger breakeven multiple times with different prices
  const breakevenPrices = [0.152000, 0.151140, 0.150840, 0.150680];

  for (let i = 0; i < breakevenPrices.length; i++) {
    const price = breakevenPrices[i];
    console.log(`\n💰 Update ${i + 1}: Price ${price} (should trigger breakeven)`);

    await TrailingManager.updateTrailing('test-spam-prevention', price);

    const trailingInfo = TrailingManager.getTrailingInfo('test-spam-prevention');
    console.log(`   Breakeven: ${trailingInfo.breakeven}`);
    console.log(`   Breakeven Notified: ${trailingInfo.breakevenNotified}`);
    console.log(`   Current SL: ${trailingInfo.currentSL.toFixed(6)}`);
  }

  console.log('\n🎯 Test 2: Trailing Active Trigger (Should Only Send 1 Notification)');
  console.log('====================================================================');

  // Trigger trailing active multiple times
  const trailingPrices = [0.148000, 0.147500, 0.147000];

  for (let i = 0; i < trailingPrices.length; i++) {
    const price = trailingPrices[i];
    console.log(`\n📈 Update ${i + 1}: Price ${price} (should trigger trailing)`);

    await TrailingManager.updateTrailing('test-spam-prevention', price);

    const trailingInfo = TrailingManager.getTrailingInfo('test-spam-prevention');
    console.log(`   Trailing Active: ${trailingInfo.trailingActive}`);
    console.log(`   Trailing Notified: ${trailingInfo.trailingNotified}`);
    console.log(`   Current SL: ${trailingInfo.currentSL.toFixed(6)}`);
  }

  console.log('\n📊 Summary of Notifications Sent:');
  console.log('==================================');
  console.log('Expected: 2 notifications total');
  console.log('  1. Breakeven notification (first trigger only)');
  console.log('  2. Trailing active notification (first trigger only)');
  console.log('');
  console.log('✅ No spam notifications should be sent for repeated conditions!');

  // Test with BUY signal as well
  console.log('\n🎯 Test 3: BUY Signal (Quick Test)');
  console.log('==================================');

  const buySignal = {
    _id: 'test-buy-spam',
    symbol: 'BTCUSDT',
    type: 'BUY',
    entry: 43250.00,
    stopLoss: 43000.00,
    takeProfit: 43500.00
  };

  TrailingManager.addSignalToTrailing(buySignal);

  // Trigger breakeven multiple times
  console.log('\n💰 BUY Breakeven Test (multiple triggers):');
  const buyBreakevenPrices = [43500.00, 43520.00, 43540.00];

  for (const price of buyBreakevenPrices) {
    console.log(`   Price: ${price}`);
    await TrailingManager.updateTrailing('test-buy-spam', price);
  }

  const buyTrailingInfo = TrailingManager.getTrailingInfo('test-buy-spam');
  console.log(`   Final Breakeven Notified: ${buyTrailingInfo.breakevenNotified}`);

  console.log('\n🎉 Test Complete!');
  console.log('=================');
  console.log('✅ Notification spam prevention implemented');
  console.log('✅ Each notification type sent only once per signal');
  console.log('✅ Multiple price updates do not trigger duplicate notifications');

  // Cleanup
  TrailingManager.removeSignalFromTrailing('test-spam-prevention');
  TrailingManager.removeSignalFromTrailing('test-buy-spam');
}

// Run the test
if (require.main === module) {
  testNotificationSpam().catch(console.error);
}

module.exports = { testNotificationSpam };