/**
 * Test script để kiểm tra zombie cleanup service
 */

// Mock config và logger
global.config = {
  mongo: {
    connections: {
      master: {
        host: 'localhost',
        port: 27017,
        database: 'test_trading',
        options: {}
      }
    }
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

global.moment = require('moment');

const mongoose = require('mongoose');

// Mock TradingSignal model
const tradingSignalSchema = new mongoose.Schema({
  symbol: String,
  timeframe: String,
  type: String,
  entry: Number,
  stopLoss: Number,
  takeProfit: Number,
  status: {
    type: String,
    enum: ['active', 'hit_tp', 'hit_sl', 'cancelled', 'early_exit'],
    default: 'active'
  },
  exitPrice: Number,
  exitTime: Date,
  pnlPercent: Number,
  telegramMessageId: String,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const TradingSignal = mongoose.model('TradingSignal', tradingSignalSchema);

// Mock require
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === '../models/tradingSignal') {
    return TradingSignal;
  }
  return originalRequire.apply(this, arguments);
};

async function testZombieCleanup() {
  console.log('🧪 Testing Zombie Cleanup Service\\n');
  console.log('===================================\\n');

  try {
    // Connect to test database
    await mongoose.connect('mongodb://localhost:27017/test_trading');
    console.log('✅ Connected to test database');

    // Clear existing test data
    await TradingSignal.deleteMany({});
    console.log('🗑️ Cleared existing test data');

    // Create test zombie signals
    console.log('\\n📊 Creating Test Zombie Signals:');
    console.log('=================================');

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));
    const twoHoursAgo = new Date(now.getTime() - (2 * 60 * 60 * 1000));
    const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));

    // 1. Old active signals (> 24h)
    const oldSignals = await TradingSignal.insertMany([
      {
        symbol: 'BTCUSDT',
        timeframe: '5m',
        type: 'BUY',
        entry: 43000,
        stopLoss: 42500,
        takeProfit: 43500,
        status: 'active',
        telegramMessageId: 'msg_001',
        createdAt: oneDayAgo
      },
      {
        symbol: 'ETHUSDT',
        timeframe: '5m',
        type: 'SELL',
        entry: 2500,
        stopLoss: 2550,
        takeProfit: 2450,
        status: 'active',
        telegramMessageId: 'msg_002',
        createdAt: new Date(oneDayAgo.getTime() - (60 * 60 * 1000)) // 25h ago
      }
    ]);
    console.log(`✅ Created ${oldSignals.length} old active signals (> 24h)`);

    // 2. Orphaned signals (no telegram ID)
    const orphanedSignals = await TradingSignal.insertMany([
      {
        symbol: 'ADAUSDT',
        timeframe: '5m',
        type: 'BUY',
        entry: 0.35,
        stopLoss: 0.33,
        takeProfit: 0.37,
        status: 'active',
        telegramMessageId: null, // No telegram ID
        createdAt: twoHoursAgo
      },
      {
        symbol: 'SOLUSDT',
        timeframe: '5m',
        type: 'SELL',
        entry: 150,
        stopLoss: 155,
        takeProfit: 145,
        status: 'active',
        // telegramMessageId missing
        createdAt: oneHourAgo
      }
    ]);
    console.log(`✅ Created ${orphanedSignals.length} orphaned signals (no Telegram ID)`);

    // 3. Corrupted signals (missing required fields)
    const corruptedSignals = await TradingSignal.insertMany([
      {
        symbol: 'DOGEUSDT',
        timeframe: '5m',
        type: 'BUY',
        // entry: missing
        stopLoss: 0.08,
        takeProfit: 0.12,
        status: 'active',
        telegramMessageId: 'msg_005',
        createdAt: now
      },
      {
        symbol: '', // Empty symbol
        timeframe: '5m',
        type: 'SELL',
        entry: 100,
        stopLoss: 105,
        takeProfit: 95,
        status: 'active',
        telegramMessageId: 'msg_006',
        createdAt: now
      }
    ]);
    console.log(`✅ Created ${corruptedSignals.length} corrupted signals (missing fields)`);

    // 4. Valid active signals (should not be cleaned)
    const validSignals = await TradingSignal.insertMany([
      {
        symbol: 'FILUSDT',
        timeframe: '5m',
        type: 'BUY',
        entry: 4.4,
        stopLoss: 4.2,
        takeProfit: 4.6,
        status: 'active',
        telegramMessageId: 'msg_007',
        createdAt: new Date(now.getTime() - (30 * 60 * 1000)) // 30 min ago
      },
      {
        symbol: 'MATICUSDT',
        timeframe: '5m',
        type: 'SELL',
        entry: 0.8,
        stopLoss: 0.85,
        takeProfit: 0.75,
        status: 'active',
        telegramMessageId: 'msg_008',
        createdAt: new Date(now.getTime() - (10 * 60 * 1000)) // 10 min ago
      }
    ]);
    console.log(`✅ Created ${validSignals.length} valid active signals (should remain)`);

    // 5. Completed signals (should not be affected)
    const completedSignals = await TradingSignal.insertMany([
      {
        symbol: 'LINKUSDT',
        timeframe: '5m',
        type: 'BUY',
        entry: 15,
        stopLoss: 14.5,
        takeProfit: 15.5,
        status: 'hit_tp',
        exitPrice: 15.5,
        exitTime: new Date(),
        pnlPercent: 3.33,
        telegramMessageId: 'msg_009',
        createdAt: oneDayAgo
      }
    ]);
    console.log(`✅ Created ${completedSignals.length} completed signals (should not be affected)`);

    // Get initial stats
    console.log('\\n📊 Initial Database State:');
    console.log('==========================');
    const initialStats = {
      total: await TradingSignal.countDocuments({}),
      active: await TradingSignal.countDocuments({ status: 'active' }),
      completed: await TradingSignal.countDocuments({ status: { $ne: 'active' } })
    };
    console.log(`Total signals: ${initialStats.total}`);
    console.log(`Active signals: ${initialStats.active}`);
    console.log(`Completed signals: ${initialStats.completed}`);

    // Load and test zombie cleanup service
    console.log('\\n🧹 Testing Zombie Cleanup Service:');
    console.log('===================================');

    const ZombieCleanupService = require('./lib/services/zombieCleanupService');

    // Get zombie stats before cleanup
    console.log('\\n📊 Zombie Stats Before Cleanup:');
    const statsBefore = await ZombieCleanupService.getZombieStats();
    console.log('Stats before:', JSON.stringify(statsBefore, null, 2));

    // Perform cleanup
    console.log('\\n🧹 Performing Cleanup...');
    const cleanupResult = await ZombieCleanupService.cleanupZombieSignals();
    console.log('Cleanup result:', JSON.stringify(cleanupResult, null, 2));

    // Get stats after cleanup
    console.log('\\n📊 Database State After Cleanup:');
    console.log('=================================');
    const finalStats = {
      total: await TradingSignal.countDocuments({}),
      active: await TradingSignal.countDocuments({ status: 'active' }),
      cancelled: await TradingSignal.countDocuments({ status: 'cancelled' }),
      completed: await TradingSignal.countDocuments({ status: { $in: ['hit_tp', 'hit_sl', 'early_exit'] } })
    };
    console.log(`Total signals: ${finalStats.total}`);
    console.log(`Active signals: ${finalStats.active} (should be ${validSignals.length})`);
    console.log(`Cancelled signals: ${finalStats.cancelled} (should be ${oldSignals.length + orphanedSignals.length + corruptedSignals.length})`);
    console.log(`Completed signals: ${finalStats.completed}`);

    // Verify results
    console.log('\\n✅ Verification:');
    console.log('================');

    const expectedCleaned = oldSignals.length + orphanedSignals.length + corruptedSignals.length;
    const actualCleaned = cleanupResult.totalCleaned;

    console.log(`Expected cleaned: ${expectedCleaned}`);
    console.log(`Actually cleaned: ${actualCleaned}`);
    console.log(`Match: ${expectedCleaned === actualCleaned ? '✅ YES' : '❌ NO'}`);

    console.log(`Expected remaining active: ${validSignals.length}`);
    console.log(`Actually remaining active: ${finalStats.active}`);
    console.log(`Match: ${validSignals.length === finalStats.active ? '✅ YES' : '❌ NO'}`);

    // Test zombie stats after cleanup
    console.log('\\n📊 Zombie Stats After Cleanup:');
    const statsAfter = await ZombieCleanupService.getZombieStats();
    console.log('Stats after:', JSON.stringify(statsAfter, null, 2));
    console.log(`Zombies remaining: ${statsAfter.zombies.total} (should be 0)`);

    console.log('\\n🎉 Test Summary:');
    console.log('================');
    console.log('✅ Zombie cleanup service working correctly:');
    console.log(`   - Cleaned ${actualCleaned} zombie signals`);
    console.log(`   - Preserved ${finalStats.active} valid active signals`);
    console.log(`   - Did not affect ${finalStats.completed} completed signals`);
    console.log('✅ Categories cleaned:');
    console.log(`   - Old active (>24h): ${cleanupResult.categories.oldActive}`);
    console.log(`   - Orphaned (no Telegram ID): ${cleanupResult.categories.orphaned}`);
    console.log(`   - Corrupted (missing fields): ${cleanupResult.categories.corrupted}`);

    console.log('\\n📋 Expected Impact on Real System:');
    console.log('===================================');
    console.log('- ✅ Clean startup: No zombie signals interfering');
    console.log('- ✅ Accurate monitoring: Only valid signals tracked');
    console.log('- ✅ Better performance: Reduced database load');
    console.log('- ✅ Reliable statistics: No corrupted data');
    console.log('- ✅ System health: Clean state for new trading session');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Cleanup test data
    await TradingSignal.deleteMany({});
    await mongoose.connection.close();
    console.log('\\n🗑️ Test cleanup completed');
  }
}

// Run the test
if (require.main === module) {
  testZombieCleanup().catch(console.error);
}

module.exports = { testZombieCleanup };