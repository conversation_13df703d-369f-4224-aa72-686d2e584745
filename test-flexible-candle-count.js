#!/usr/bin/env node

/**
 * Test logic linh hoạt với số nến khác nhau
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testFlexibleCandleCount() {
  console.log('🔄 Testing Flexible Candle Count Logic...\n');

  try {
    // <PERSON>ết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');

    // Test với các symbols có số nến khác nhau
    const testSymbols = [
      'BNXUSDT',  // Có thể có ít nến
      '42USDT',   // Có ít nến
      'DOGEUSDT', // Có nhiều nến
      'BTCUSDT',  // Có nhiều nến
      'ETHUSDT'   // Có nhiều nến
    ];

    console.log('📊 Testing symbols with different candle counts:');
    console.log('='.repeat(70));

    for (const symbol of testSymbols) {
      console.log(`\n🔍 Testing ${symbol} 5m:`);
      console.log('-'.repeat(40));

      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        console.log(`📈 Available candles: ${klines.length}`);

        // Test với số nến thực tế có
        if (klines.length >= 220) {
          const allIndicators = indicators.calculateAllIndicators(klines);
          
          if (allIndicators) {
            console.log(`✅ Indicators calculated successfully:`);
            console.log(`   💰 Entry: ${allIndicators.currentPrice}`);
            console.log(`   📈 EMA50: ${allIndicators.ema50.toFixed(6)}`);
            console.log(`   📈 EMA200: ${allIndicators.ema200.toFixed(6)}`);
            console.log(`   📊 RSI: ${allIndicators.rsi?.toFixed(2) || 'N/A'}`);
            
            // Đánh giá chất lượng dựa trên số nến
            let quality = 'UNKNOWN';
            if (klines.length >= 500) {
              quality = '🟢 EXCELLENT (500+ candles)';
            } else if (klines.length >= 300) {
              quality = '🟡 GOOD (300+ candles)';
            } else if (klines.length >= 250) {
              quality = '🟠 FAIR (250+ candles)';
            } else {
              quality = '🔴 REDUCED ACCURACY (<250 candles)';
            }
            console.log(`   📊 Quality: ${quality}`);
          } else {
            console.log(`❌ Failed to calculate indicators`);
          }
        } else if (klines.length >= 50) {
          console.log(`⚠️ Not enough candles: ${klines.length} (minimum: 220)`);
          console.log(`   Can calculate basic indicators but not EMA200`);
        } else {
          console.log(`❌ Too few candles: ${klines.length} (minimum: 50 for any analysis)`);
        }

        // Test với các độ dài khác nhau nếu có đủ dữ liệu
        if (klines.length >= 300) {
          console.log(`\n   🧪 Testing accuracy with different lengths:`);
          const testLengths = [220, 250, 300, Math.min(500, klines.length)];
          
          for (const length of testLengths) {
            if (klines.length >= length) {
              const testData = klines.slice(-length);
              const testIndicators = indicators.calculateAllIndicators(testData);
              
              if (testIndicators) {
                console.log(`   📊 ${length} candles: EMA200 = ${testIndicators.ema200.toFixed(6)}`);
              }
            }
          }
        }

      } catch (error) {
        console.log(`❌ Error testing ${symbol}: ${error.message}`);
      }
    }

    // Test edge cases
    console.log('\n\n🧪 Testing Edge Cases:');
    console.log('='.repeat(50));

    // Test với DOGEUSDT để có đủ dữ liệu
    const dogeKlines = await binanceClient.getKlines('DOGEUSDT', '1m', 1000);
    console.log(`\n📊 DOGEUSDT 1m: ${dogeKlines.length} candles available`);

    if (dogeKlines.length >= 300) {
      // Test với các edge cases
      const edgeCases = [
        { length: 220, description: 'Minimum required' },
        { length: 250, description: 'Basic accuracy' },
        { length: 300, description: 'Good accuracy' },
        { length: 400, description: 'High accuracy' },
        { length: 500, description: 'Excellent accuracy' }
      ];

      console.log('\nEdge Case Testing:');
      console.log('Length\tDescription\t\tEMA200\t\tStatus');
      console.log('-'.repeat(65));

      for (const testCase of edgeCases) {
        if (dogeKlines.length >= testCase.length) {
          const testData = dogeKlines.slice(-testCase.length);
          const testIndicators = indicators.calculateAllIndicators(testData);
          
          if (testIndicators) {
            let status = '✅ OK';
            if (testCase.length < 250) {
              status = '⚠️ REDUCED';
            } else if (testCase.length >= 400) {
              status = '🎯 OPTIMAL';
            }
            
            console.log(`${testCase.length}\t${testCase.description.padEnd(15)}\t${testIndicators.ema200.toFixed(6)}\t${status}`);
          } else {
            console.log(`${testCase.length}\t${testCase.description.padEnd(15)}\tFAILED\t\t❌ ERROR`);
          }
        }
      }
    }

    console.log('\n✅ Flexible candle count test completed!');
    console.log('\n📋 Summary:');
    console.log('- Minimum requirement: 220 candles');
    console.log('- Optimal requirement: 300+ candles');
    console.log('- System now works with available data');
    console.log('- Quality warnings for sub-optimal data');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testFlexibleCandleCount().catch(console.error);
}

module.exports = testFlexibleCandleCount;
