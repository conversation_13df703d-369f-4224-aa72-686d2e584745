#!/usr/bin/env node

/**
 * Test logic mới: Body mạnh 60% hoặc Engulfing là BẮT BUỘC
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testMandatoryPattern() {
  console.log('🔧 Testing Mandatory Pattern Logic...\n');

  try {
    // <PERSON><PERSON>t nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    console.log('📊 NEW LOGIC: Body mạnh ≥60% HOẶC Engulfing là BẮT BUỘC');
    console.log('='.repeat(70));
    console.log('✅ Core: Price vs EMA200 + EMA alignment');
    console.log('✅ Signal: MACD crossover HOẶC RSI zone');
    console.log('✅ Pattern: Body mạnh ≥60% HOẶC Engulfing (BẮT BUỘC)');
    console.log('');

    // Test với top volume coins
    const topCoins = await binanceClient.getTop24hVolumeCoins(15);
    console.log(`📊 Testing ${topCoins.length} top volume coins...`);

    let totalSignals = 0;
    const foundSignals = [];
    const rejectedSignals = [];

    for (const symbol of topCoins) {
      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        
        if (klines.length >= 220) {
          const indicatorData = indicators.calculateAllIndicators(klines);
          const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);

          // Test BUY conditions
          const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);
          
          // Test SELL conditions
          const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);

          // Analyze detailed conditions
          const coreConditionsBuy = buyConditions.details.coreConditions;
          const signalConditionBuy = buyConditions.details.signalCondition;
          const patternConditionBuy = buyConditions.details.patternCondition;

          const coreConditionsSell = sellConditions.details.coreConditions;
          const signalConditionSell = sellConditions.details.signalCondition;
          const patternConditionSell = sellConditions.details.patternCondition;

          // Check if would have been valid with old logic (without mandatory pattern)
          const wouldBeValidBuyOld = coreConditionsBuy && signalConditionBuy;
          const wouldBeValidSellOld = coreConditionsSell && signalConditionSell;

          // Check if valid with new logic (with mandatory pattern)
          const isValidBuyNew = buyConditions.isValid;
          const isValidSellNew = sellConditions.isValid;

          // Log detailed analysis
          if (wouldBeValidBuyOld || wouldBeValidSellOld || isValidBuyNew || isValidSellNew) {
            console.log(`\n🔍 ${symbol}:`);
            console.log(`   📊 Price: ${indicatorData.currentPrice}`);
            console.log(`   💪 Strong Body: ${indicatorData.strongBody?.isStrong ? 'Yes' : 'No'} (${indicatorData.strongBody?.bodyPercent?.toFixed(1) || 0}%)`);
            console.log(`   🕯️ Engulfing: ${indicatorData.engulfing}`);
            console.log(`   🔄 MACD: ${macdCrossover}`);
            console.log(`   📊 RSI: ${indicatorData.rsi?.toFixed(1)}`);

            if (wouldBeValidBuyOld || isValidBuyNew) {
              console.log(`   📋 BUY: Core(${coreConditionsBuy}) + Signal(${signalConditionBuy}) + Pattern(${patternConditionBuy})`);
              console.log(`   📊 Old Logic: ${wouldBeValidBuyOld ? 'VALID' : 'INVALID'} | New Logic: ${isValidBuyNew ? 'VALID' : 'INVALID'}`);
            }

            if (wouldBeValidSellOld || isValidSellNew) {
              console.log(`   📋 SELL: Core(${coreConditionsSell}) + Signal(${signalConditionSell}) + Pattern(${patternConditionSell})`);
              console.log(`   📊 Old Logic: ${wouldBeValidSellOld ? 'VALID' : 'INVALID'} | New Logic: ${isValidSellNew ? 'VALID' : 'INVALID'}`);
            }

            // Track rejected signals (would be valid with old logic but not new)
            if ((wouldBeValidBuyOld && !isValidBuyNew) || (wouldBeValidSellOld && !isValidSellNew)) {
              rejectedSignals.push({
                symbol,
                type: wouldBeValidBuyOld ? 'BUY' : 'SELL',
                reason: 'Missing pattern condition',
                strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0,
                engulfing: indicatorData.engulfing
              });
            }
          }

          // Test actual signal generation
          const signal = await signalAnalyzer.analyzeSignal(symbol, '5m', klines);
          
          if (signal) {
            totalSignals++;
            foundSignals.push({
              symbol,
              type: signal.type,
              entry: signal.entry,
              riskReward: signal.riskReward,
              strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0,
              engulfing: indicatorData.engulfing,
              conditions: signal.conditions
            });
            
            console.log(`🚀 SIGNAL: ${symbol} ${signal.type} at ${signal.entry} (RR: 1:${signal.riskReward})`);
          }
        }
      } catch (error) {
        // Skip individual errors
      }
    }

    console.log(`\n\n📊 RESULTS SUMMARY:`);
    console.log('='.repeat(70));
    console.log(`📊 Total signals found: ${totalSignals} out of ${topCoins.length} symbols`);
    console.log(`📊 Success rate: ${((totalSignals/topCoins.length)*100).toFixed(1)}%`);
    console.log(`📊 Rejected signals (missing pattern): ${rejectedSignals.length}`);

    if (foundSignals.length > 0) {
      console.log(`\n🎉 Valid Signals with Mandatory Pattern:`);
      foundSignals.forEach((signal, i) => {
        console.log(`${i+1}. ${signal.symbol}: ${signal.type} at ${signal.entry}`);
        console.log(`   💪 Body: ${signal.strongBodyPercent.toFixed(1)}%, Engulfing: ${signal.engulfing}`);
        console.log(`   📋 Conditions: ${signal.conditions.split('\n')[0]}...`);
      });

      // Pattern analysis
      const strongBodySignals = foundSignals.filter(s => s.strongBodyPercent >= 60);
      const engulfingSignals = foundSignals.filter(s => s.engulfing !== 'none');
      
      console.log(`\n📊 Pattern Analysis:`);
      console.log(`   💪 Strong Body (≥60%): ${strongBodySignals.length}/${foundSignals.length}`);
      console.log(`   🕯️ Engulfing Pattern: ${engulfingSignals.length}/${foundSignals.length}`);
    }

    if (rejectedSignals.length > 0) {
      console.log(`\n❌ Rejected Signals (would be valid with old logic):`);
      rejectedSignals.forEach((signal, i) => {
        console.log(`${i+1}. ${signal.symbol}: ${signal.type}`);
        console.log(`   💪 Body: ${signal.strongBodyPercent.toFixed(1)}%, Engulfing: ${signal.engulfing}`);
        console.log(`   ❌ Reason: ${signal.reason}`);
      });

      console.log(`\n📊 Rejection Analysis:`);
      const weakBodyRejected = rejectedSignals.filter(s => s.strongBodyPercent < 60 && s.engulfing === 'none');
      console.log(`   📊 Rejected due to weak body + no engulfing: ${weakBodyRejected.length}/${rejectedSignals.length}`);
    }

    console.log(`\n🎯 MANDATORY PATTERN LOGIC SUMMARY:`);
    console.log('✅ Pattern condition is now MANDATORY');
    console.log('✅ Requires Body ≥60% OR Engulfing pattern');
    console.log('✅ More selective but higher quality signals');
    console.log(`✅ Current success rate: ${((totalSignals/topCoins.length)*100).toFixed(1)}%`);

  } catch (error) {
    console.error('❌ Error during test:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testMandatoryPattern().catch(console.error);
}

module.exports = testMandatoryPattern;
