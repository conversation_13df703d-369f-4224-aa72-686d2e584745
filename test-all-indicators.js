#!/usr/bin/env node

/**
 * Test tất cả indicators để so s<PERSON>h với Binance
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testAllIndicators() {
  console.log('🔍 Testing All Indicators vs Binance Exchange...\n');

  try {
    // <PERSON>ết nối <PERSON>go<PERSON>
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');

    // Test với 42USDT 5m
    console.log('🔍 Testing 42USDT 5m:');
    console.log('='.repeat(60));

    const klines42 = await binanceClient.getKlines('42USDT', '5m', 500);
    console.log(`✅ Lấy được ${klines42.length} nến 42USDT 5m`);

    if (klines42.length >= 220) {
      const indicators42 = indicators.calculateAllIndicators(klines42);
      
      if (indicators42) {
        console.log('\n📊 42USDT 5m Indicators:');
        console.log(`💰 Entry: ${indicators42.currentPrice}`);
        console.log(`📈 EMA50: ${indicators42.ema50.toFixed(6)}`);
        console.log(`📈 EMA200: ${indicators42.ema200.toFixed(6)}`);
        console.log(`📊 MACD: ${indicators42.macd?.macd?.toFixed(6) || 'N/A'}`);
        console.log(`📊 Signal: ${indicators42.macd?.signal?.toFixed(6) || 'N/A'}`);
        console.log(`📊 RSI: ${indicators42.rsi?.toFixed(2) || 'N/A'}`);
        console.log(`🕯️ Pattern: ${indicators42.engulfing}`);

        console.log('\n🔍 Comparison with Binance (from screenshot):');
        console.log('Binance Exchange:');
        console.log('- Current Price: ~0.16801');
        console.log('- EMA(9): 0.16756');
        console.log('- EMA(21): 0.16709');
        console.log('- EMA(200): 0.17348');
        console.log('- RSI: 52.95051');
        console.log('');
        console.log('Our calculation:');
        console.log(`- Current Price: ${indicators42.currentPrice}`);
        console.log(`- EMA50: ${indicators42.ema50.toFixed(6)}`);
        console.log(`- EMA200: ${indicators42.ema200.toFixed(6)}`);
        console.log(`- RSI: ${indicators42.rsi?.toFixed(2) || 'N/A'}`);

        // Tính chênh lệch
        const binanceData = {
          price: 0.16801,
          ema200: 0.17348,
          rsi: 52.95051
        };

        const priceDiff = Math.abs(binanceData.price - indicators42.currentPrice);
        const ema200Diff = Math.abs(binanceData.ema200 - indicators42.ema200);
        const rsiDiff = Math.abs(binanceData.rsi - indicators42.rsi);

        console.log('\n⚖️ Accuracy Analysis:');
        console.log(`Price difference: ${priceDiff.toFixed(6)} (${(priceDiff/binanceData.price*100).toFixed(2)}%)`);
        console.log(`EMA200 difference: ${ema200Diff.toFixed(6)} (${(ema200Diff/binanceData.ema200*100).toFixed(2)}%)`);
        console.log(`RSI difference: ${rsiDiff.toFixed(2)} (${(rsiDiff/binanceData.rsi*100).toFixed(2)}%)`);

        // Đánh giá
        const ema200Accuracy = (ema200Diff/binanceData.ema200*100) < 1.0;
        const rsiAccuracy = (rsiDiff/binanceData.rsi*100) < 5.0;

        console.log('\n📋 Accuracy Summary:');
        console.log(`✅ EMA200: ${ema200Accuracy ? 'ACCURATE' : 'NEEDS IMPROVEMENT'} (${(ema200Diff/binanceData.ema200*100).toFixed(2)}%)`);
        console.log(`✅ RSI: ${rsiAccuracy ? 'ACCURATE' : 'NEEDS IMPROVEMENT'} (${(rsiDiff/binanceData.rsi*100).toFixed(2)}%)`);
      }
    }

    // Test với DOGEUSDT 1m
    console.log('\n\n🐕 Testing DOGEUSDT 1m:');
    console.log('='.repeat(60));

    const klinesDoge = await binanceClient.getKlines('DOGEUSDT', '1m', 500);
    console.log(`✅ Lấy được ${klinesDoge.length} nến DOGEUSDT 1m`);

    if (klinesDoge.length >= 220) {
      const indicatorsDoge = indicators.calculateAllIndicators(klinesDoge);
      
      if (indicatorsDoge) {
        console.log('\n📊 DOGEUSDT 1m Indicators:');
        console.log(`💰 Entry: ${indicatorsDoge.currentPrice}`);
        console.log(`📈 EMA50: ${indicatorsDoge.ema50.toFixed(6)}`);
        console.log(`📈 EMA200: ${indicatorsDoge.ema200.toFixed(6)}`);
        console.log(`📊 MACD: ${indicatorsDoge.macd?.macd?.toFixed(6) || 'N/A'}`);
        console.log(`📊 Signal: ${indicatorsDoge.macd?.signal?.toFixed(6) || 'N/A'}`);
        console.log(`📊 RSI: ${indicatorsDoge.rsi?.toFixed(2) || 'N/A'}`);
        console.log(`🕯️ Pattern: ${indicatorsDoge.engulfing}`);

        // So sánh với dữ liệu từ test trước
        console.log('\n🔍 Comparison with previous test:');
        console.log('Previous test (from screenshot):');
        console.log('- Entry: 0.199550');
        console.log('- EMA50: 0.199371');
        console.log('- EMA200: 0.199188');
        console.log('- MACD: -0.000036');
        console.log('- Signal: -0.000053');
        console.log('- RSI: 56.62');
        console.log('');
        console.log('Current calculation:');
        console.log(`- Entry: ${indicatorsDoge.currentPrice}`);
        console.log(`- EMA50: ${indicatorsDoge.ema50.toFixed(6)}`);
        console.log(`- EMA200: ${indicatorsDoge.ema200.toFixed(6)}`);
        console.log(`- MACD: ${indicatorsDoge.macd?.macd?.toFixed(6) || 'N/A'}`);
        console.log(`- Signal: ${indicatorsDoge.macd?.signal?.toFixed(6) || 'N/A'}`);
        console.log(`- RSI: ${indicatorsDoge.rsi?.toFixed(2) || 'N/A'}`);
      }
    }

    console.log('\n✅ All indicators test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testAllIndicators().catch(console.error);
}

module.exports = testAllIndicators;
