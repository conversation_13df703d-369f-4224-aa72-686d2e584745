#!/usr/bin/env node

/**
 * Test EMA với SMA seed method vs thư viện technicalindicators
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function testSMASeedEMA() {
  console.log('🧪 Testing SMA Seed EMA vs Library EMA...\n');

  try {
    // <PERSON>ết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');

    // Test với DOGEUSDT vì có nhiều dữ liệu hơn
    console.log('🐕 Testing DOGEUSDT 1m with SMA Seed EMA:');
    console.log('='.repeat(60));

    const klines = await binanceClient.getKlines('DOGEUSDT', '1m', 1000);
    console.log(`✅ Fetched ${klines.length} candles for DOGEUSDT 1m`);

    if (klines.length >= 300) {
      const closes = klines.map(c => c.close);
      
      // Test với các độ dài khác nhau
      const testLengths = [300, 400, 500, 600, 800, 1000];
      
      console.log('\n📊 EMA200 Comparison - SMA Seed vs Library:');
      console.log('Length\tSMA Seed\tLibrary\t\tDifference\t%Diff');
      console.log('-'.repeat(70));
      
      for (const length of testLengths) {
        if (closes.length >= length) {
          const testData = closes.slice(-length);
          
          // Method 1: SMA Seed (our new method)
          const smaSeedEMA = indicators.calculateEMA(testData, 200);
          
          // Method 2: Library method (first value seed)
          const { EMA } = require('technicalindicators');
          const libEMA = EMA.calculate({
            period: 200,
            values: testData
          });
          const libEMAValue = libEMA[libEMA.length - 1];
          
          if (smaSeedEMA && libEMAValue) {
            const diff = Math.abs(smaSeedEMA - libEMAValue);
            const percentDiff = (diff / smaSeedEMA) * 100;
            
            console.log(`${length}\t${smaSeedEMA.toFixed(6)}\t${libEMAValue.toFixed(6)}\t${diff.toFixed(6)}\t${percentDiff.toFixed(2)}%`);
          }
        }
      }

      // Test với 42USDT nếu có đủ dữ liệu
      console.log('\n\n💰 Testing 42USDT 5m with SMA Seed EMA:');
      console.log('='.repeat(60));

      const klines42 = await binanceClient.getKlines('42USDT', '5m', 1000);
      console.log(`✅ Fetched ${klines42.length} candles for 42USDT 5m`);

      if (klines42.length >= 300) {
        const closes42 = klines42.map(c => c.close);
        
        // Test với SMA seed method
        const smaSeedEMA42 = indicators.calculateEMA(closes42, 200);
        
        // So sánh với Binance
        const binanceEMA200 = 0.17348;
        const diff = Math.abs(binanceEMA200 - smaSeedEMA42);
        const percentDiff = (diff / binanceEMA200) * 100;
        
        console.log(`\n📊 42USDT EMA200 Results:`);
        console.log(`SMA Seed Method: ${smaSeedEMA42.toFixed(6)}`);
        console.log(`Binance Exchange: ${binanceEMA200.toFixed(6)}`);
        console.log(`Difference: ${diff.toFixed(6)} (${percentDiff.toFixed(2)}%)`);
        
        if (percentDiff < 0.5) {
          console.log('🎯 EXCELLENT: SMA Seed method is very accurate!');
        } else if (percentDiff < 1.0) {
          console.log('✅ GOOD: SMA Seed method is accurate!');
        } else {
          console.log('⚠️ NEEDS IMPROVEMENT: Still some difference');
        }

        // Test với tất cả indicators
        console.log('\n📊 All Indicators with SMA Seed EMA:');
        const allIndicators = indicators.calculateAllIndicators(klines42);
        
        if (allIndicators) {
          console.log(`💰 Entry: ${allIndicators.currentPrice}`);
          console.log(`📈 EMA50: ${allIndicators.ema50.toFixed(6)}`);
          console.log(`📈 EMA200: ${allIndicators.ema200.toFixed(6)}`);
          console.log(`📊 MACD: ${allIndicators.macd?.macd?.toFixed(6) || 'N/A'}`);
          console.log(`📊 Signal: ${allIndicators.macd?.signal?.toFixed(6) || 'N/A'}`);
          console.log(`📊 RSI: ${allIndicators.rsi?.toFixed(2) || 'N/A'}`);
          console.log(`🕯️ Pattern: ${allIndicators.engulfing}`);
        } else {
          console.log('❌ Not enough data for all indicators (need 300+ candles)');
        }
      } else {
        console.log(`❌ Not enough 42USDT data: ${klines42.length} candles (need 300+)`);
      }

      // Kiểm tra warm-up effect với SMA seed
      console.log('\n\n🔥 Testing Warm-up Effect with SMA Seed:');
      console.log('='.repeat(60));

      if (klines.length >= 600) {
        const closes = klines.map(c => c.close);
        const warmupTests = [250, 300, 400, 500, 600];
        
        console.log('Length\tEMA200\t\tStability');
        console.log('-'.repeat(40));
        
        let previousEMA = null;
        for (const length of warmupTests) {
          if (closes.length >= length) {
            const testData = closes.slice(-length);
            const emaValue = indicators.calculateEMA(testData, 200);
            
            if (emaValue) {
              let stability = 'N/A';
              if (previousEMA) {
                const change = Math.abs(emaValue - previousEMA);
                const changePercent = (change / previousEMA) * 100;
                stability = `${changePercent.toFixed(3)}%`;
              }
              
              console.log(`${length}\t${emaValue.toFixed(6)}\t${stability}`);
              previousEMA = emaValue;
            }
          }
        }
      }
    } else {
      console.log(`❌ Not enough DOGEUSDT data: ${klines.length} candles (need 300+)`);
    }

    console.log('\n✅ SMA Seed EMA test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testSMASeedEMA().catch(console.error);
}

module.exports = testSMASeedEMA;
