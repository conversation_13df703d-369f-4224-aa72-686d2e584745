#!/usr/bin/env node

/**
 * Test chuyên sâu EMA theo gợi ý của user
 * Ki<PERSON>m tra: seed method, warm-up, timestamp, data integrity
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Import thư viện EMA để test trự<PERSON> tiế<PERSON>
const { EMA } = require('technicalindicators');

async function testEMADeepAnalysis() {
  console.log('🔬 Deep EMA Analysis - Following User Suggestions...\n');

  try {
    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');

    // 1. KIỂM TRA WARM-UP EFFECT
    console.log('🔥 1. TESTING WARM-UP EFFECT:');
    console.log('='.repeat(60));

    const symbol = '42USDT';
    const timeframe = '5m';
    
    // Lấy nhiều dữ liệu để test warm-up
    const klines = await binanceClient.getKlines(symbol, timeframe, 1000);
    console.log(`✅ Fetched ${klines.length} candles for ${symbol} ${timeframe}`);

    if (klines.length > 0) {
      console.log(`📅 From: ${klines[0].openTime.toISOString()}`);
      console.log(`📅 To: ${klines[klines.length - 1].openTime.toISOString()}`);
      
      // Kiểm tra tính liên tục của dữ liệu
      let missingCandles = 0;
      for (let i = 1; i < klines.length; i++) {
        const timeDiff = klines[i].openTime - klines[i-1].openTime;
        const expectedDiff = 5 * 60 * 1000; // 5 minutes in ms
        if (timeDiff !== expectedDiff) {
          missingCandles++;
        }
      }
      console.log(`⚠️ Missing/irregular candles: ${missingCandles}`);

      // Test EMA với các độ dài khác nhau
      const testLengths = [200, 250, 300, 400, 500, 600, 800, 1000];
      const closes = klines.map(c => c.close);
      
      console.log('\n📊 EMA200 with different data lengths:');
      console.log('Length\tEMA200\t\tDiff from Binance\t%Diff');
      console.log('-'.repeat(60));
      
      const binanceEMA200 = 0.17348; // From screenshot
      
      for (const length of testLengths) {
        if (closes.length >= length) {
          const testData = closes.slice(-length);
          const emaResult = EMA.calculate({
            period: 200,
            values: testData
          });
          
          if (emaResult.length > 0) {
            const ema200 = emaResult[emaResult.length - 1];
            const diff = Math.abs(binanceEMA200 - ema200);
            const percentDiff = (diff / binanceEMA200) * 100;
            
            console.log(`${length}\t${ema200.toFixed(6)}\t${diff.toFixed(6)}\t\t${percentDiff.toFixed(2)}%`);
          }
        }
      }
    }

    // 2. KIỂM TRA SEED METHOD
    console.log('\n\n🌱 2. TESTING SEED METHOD:');
    console.log('='.repeat(60));

    if (klines.length >= 400) {
      const closes = klines.slice(-400).map(c => c.close);
      
      // Method 1: Thư viện technicalindicators (seed = first value)
      const emaLib = EMA.calculate({
        period: 200,
        values: closes
      });
      
      // Method 2: Manual EMA với SMA seed (như Binance/TradingView)
      function calculateEMAWithSMASeed(prices, period) {
        if (prices.length < period) return [];
        
        // Tính SMA cho 200 giá trị đầu làm seed
        const smaSum = prices.slice(0, period).reduce((sum, price) => sum + price, 0);
        const sma = smaSum / period;
        
        const multiplier = 2 / (period + 1);
        const emaValues = [sma];
        
        // Tính EMA từ giá trị thứ 201 trở đi
        for (let i = period; i < prices.length; i++) {
          const ema = (prices[i] * multiplier) + (emaValues[emaValues.length - 1] * (1 - multiplier));
          emaValues.push(ema);
        }
        
        return emaValues;
      }
      
      const emaManual = calculateEMAWithSMASeed(closes, 200);
      
      console.log('Method comparison:');
      console.log(`Library (seed=first): ${emaLib[emaLib.length - 1]?.toFixed(6) || 'N/A'}`);
      console.log(`Manual (seed=SMA):   ${emaManual[emaManual.length - 1]?.toFixed(6) || 'N/A'}`);
      console.log(`Binance Exchange:    ${binanceEMA200.toFixed(6)}`);
      
      if (emaLib.length > 0 && emaManual.length > 0) {
        const libDiff = Math.abs(binanceEMA200 - emaLib[emaLib.length - 1]);
        const manualDiff = Math.abs(binanceEMA200 - emaManual[emaManual.length - 1]);
        
        console.log(`\nAccuracy comparison:`);
        console.log(`Library method: ${(libDiff/binanceEMA200*100).toFixed(2)}% difference`);
        console.log(`Manual method:  ${(manualDiff/binanceEMA200*100).toFixed(2)}% difference`);
        
        if (manualDiff < libDiff) {
          console.log('🎯 Manual method (SMA seed) is more accurate!');
        } else {
          console.log('🎯 Library method is more accurate!');
        }
      }
    }

    // 3. KIỂM TRA TIMESTAMP VÀ DATA INTEGRITY
    console.log('\n\n⏰ 3. TESTING TIMESTAMP & DATA INTEGRITY:');
    console.log('='.repeat(60));

    // Lấy dữ liệu mới nhất để so sánh timestamp
    const latestKlines = await binanceClient.getKlines(symbol, timeframe, 10);
    
    console.log('Latest candles timestamps:');
    latestKlines.forEach((candle, i) => {
      console.log(`${i+1}. ${candle.openTime.toISOString()} | Close: ${candle.close}`);
    });

    // 4. KIỂM TRA PRECISION VÀ ROUNDING
    console.log('\n\n🔢 4. TESTING PRECISION & ROUNDING:');
    console.log('='.repeat(60));

    if (klines.length >= 300) {
      const closes = klines.slice(-300).map(c => c.close);
      
      // Test với precision khác nhau
      const precisionTests = [6, 8, 10, 12];
      
      console.log('Precision\tEMA200\t\tDifference');
      console.log('-'.repeat(40));
      
      for (const precision of precisionTests) {
        // Round input data to different precisions
        const roundedCloses = closes.map(price => parseFloat(price.toFixed(precision)));
        
        const emaResult = EMA.calculate({
          period: 200,
          values: roundedCloses
        });
        
        if (emaResult.length > 0) {
          const ema200 = emaResult[emaResult.length - 1];
          const diff = Math.abs(binanceEMA200 - ema200);
          
          console.log(`${precision}\t\t${ema200.toFixed(6)}\t${diff.toFixed(6)}`);
        }
      }
    }

    console.log('\n✅ Deep EMA analysis completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  testEMADeepAnalysis().catch(console.error);
}

module.exports = testEMADeepAnalysis;
