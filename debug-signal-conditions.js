#!/usr/bin/env node

/**
 * Debug chi tiết điều kiện signals
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function debugSignalConditions() {
  console.log('🔍 Debug Signal Conditions in Detail...\n');

  try {
    // <PERSON>ết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');
    const signalAnalyzer = require('./lib/trading/signalAnalyzer');

    // Test với một số symbols cụ thể
    const testSymbols = ['BTCUSDT', 'ETHUSDT', 'TRUMPUSDT', 'ZECUSDT'];

    for (const symbol of testSymbols) {
      console.log(`\n🔍 DETAILED ANALYSIS: ${symbol} 5m`);
      console.log('='.repeat(70));

      try {
        const klines = await binanceClient.getKlines(symbol, '5m', 1000);
        
        if (klines.length >= 220) {
          const indicatorData = indicators.calculateAllIndicators(klines);
          
          if (indicatorData) {
            console.log(`📊 Raw Indicators:`);
            console.log(`   💰 Current Price: ${indicatorData.currentPrice}`);
            console.log(`   📈 EMA50: ${indicatorData.ema50.toFixed(6)}`);
            console.log(`   📈 EMA200: ${indicatorData.ema200.toFixed(6)}`);
            console.log(`   📊 RSI: ${indicatorData.rsi?.toFixed(2) || 'N/A'}`);
            console.log(`   🕯️ Engulfing: ${indicatorData.engulfing}`);
            console.log(`   💪 Strong Body: ${indicatorData.strongBody?.isStrong ? 'Yes' : 'No'} (${indicatorData.strongBody?.bodyPercent?.toFixed(1) || 0}%)`);
            console.log(`   📊 MACD: ${indicatorData.macd?.macd?.toFixed(6) || 'N/A'}`);
            console.log(`   📊 Signal: ${indicatorData.macd?.signal?.toFixed(6) || 'N/A'}`);

            const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, klines);
            console.log(`   🔄 MACD Crossover: ${macdCrossover}`);

            // Detailed BUY analysis
            console.log(`\n📋 BUY CONDITIONS BREAKDOWN:`);
            
            const buyConditions = signalAnalyzer.checkBuyConditions(indicatorData, macdCrossover);
            
            console.log(`   1️⃣ Price > EMA200: ${buyConditions.conditions.priceAboveEMA200} (${indicatorData.currentPrice} > ${indicatorData.ema200.toFixed(6)})`);
            console.log(`   2️⃣ EMA50 > EMA200: ${buyConditions.conditions.emaAlignment} (${indicatorData.ema50.toFixed(6)} > ${indicatorData.ema200.toFixed(6)})`);
            console.log(`   3️⃣ MACD Crossover: ${buyConditions.conditions.macdCrossover} (${macdCrossover})`);
            console.log(`   4️⃣ RSI Zone (50-70): ${buyConditions.conditions.rsiZone} (RSI: ${indicatorData.rsi?.toFixed(2)})`);
            console.log(`   5️⃣ Strong Body/Engulfing: ${buyConditions.conditions.strongBodyOrEngulfing}`);
            
            console.log(`\n   🎯 Core Conditions: ${buyConditions.details.coreConditions}`);
            console.log(`   🎯 Signal Condition: ${buyConditions.details.signalCondition}`);
            console.log(`   🎯 Pattern Condition: ${buyConditions.details.patternCondition}`);
            console.log(`   🎯 FINAL BUY VALID: ${buyConditions.isValid}`);

            // Detailed SELL analysis
            console.log(`\n📋 SELL CONDITIONS BREAKDOWN:`);
            
            const sellConditions = signalAnalyzer.checkSellConditions(indicatorData, macdCrossover);
            
            console.log(`   1️⃣ Price < EMA200: ${sellConditions.conditions.priceBelowEMA200} (${indicatorData.currentPrice} < ${indicatorData.ema200.toFixed(6)})`);
            console.log(`   2️⃣ EMA50 < EMA200: ${sellConditions.conditions.emaAlignment} (${indicatorData.ema50.toFixed(6)} < ${indicatorData.ema200.toFixed(6)})`);
            console.log(`   3️⃣ MACD Crossover: ${sellConditions.conditions.macdCrossover} (${macdCrossover})`);
            console.log(`   4️⃣ RSI Zone (30-50): ${sellConditions.conditions.rsiZone} (RSI: ${indicatorData.rsi?.toFixed(2)})`);
            console.log(`   5️⃣ Strong Body/Engulfing: ${sellConditions.conditions.strongBodyOrEngulfing}`);
            
            console.log(`\n   🎯 Core Conditions: ${sellConditions.details.coreConditions}`);
            console.log(`   🎯 Signal Condition: ${sellConditions.details.signalCondition}`);
            console.log(`   🎯 Pattern Condition: ${sellConditions.details.patternCondition}`);
            console.log(`   🎯 FINAL SELL VALID: ${sellConditions.isValid}`);

            // Suggestions
            console.log(`\n💡 SUGGESTIONS:`);
            if (!buyConditions.isValid && !sellConditions.isValid) {
              console.log(`   ⚠️ No valid signals. Reasons:`);
              
              if (!buyConditions.details.coreConditions && !sellConditions.details.coreConditions) {
                console.log(`   📊 Price is between EMA50 and EMA200 (sideways market)`);
              }
              
              if (!buyConditions.details.signalCondition && !sellConditions.details.signalCondition) {
                console.log(`   📊 No MACD crossover and RSI not in zones`);
              }
              
              if (!buyConditions.details.patternCondition && !sellConditions.details.patternCondition) {
                console.log(`   📊 No strong body pattern or engulfing`);
              }
            }

            // Test signal generation
            const signal = await signalAnalyzer.analyzeSignal(symbol, '5m', klines);
            
            if (signal) {
              console.log(`\n🚀 SIGNAL GENERATED!`);
              console.log(`   📊 Type: ${signal.type}`);
              console.log(`   💰 Entry: ${signal.entry}`);
              console.log(`   🛑 Stop Loss: ${signal.stopLoss}`);
              console.log(`   🎯 Take Profit: ${signal.takeProfit}`);
              console.log(`   ⚖️ Risk/Reward: 1:${signal.riskReward}`);
            } else {
              console.log(`\n❌ No signal generated despite analysis`);
            }
          }
        }
      } catch (error) {
        console.log(`❌ Error analyzing ${symbol}: ${error.message}`);
      }
    }

    console.log('\n\n📊 MARKET ANALYSIS SUMMARY:');
    console.log('='.repeat(60));
    console.log('Based on the analysis above, the market appears to be in:');
    console.log('1. Sideways/consolidation phase');
    console.log('2. Lack of strong MACD crossovers');
    console.log('3. RSI values mostly outside signal zones');
    console.log('4. Weak candle body patterns');
    console.log('');
    console.log('💡 Recommendations:');
    console.log('- Consider relaxing RSI zones further');
    console.log('- Allow signals without MACD crossover in strong trends');
    console.log('- Reduce strong body requirement to 50%');
    console.log('- Add volume confirmation');

    console.log('\n✅ Debug completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy debug
if (require.main === module) {
  debugSignalConditions().catch(console.error);
}

module.exports = debugSignalConditions;
