/**
 * Test script để kiểm tra rate limiting và error handling cho Binance API
 * Đ<PERSON><PERSON> bảo không bị lỗi 418 (rate limit exceeded)
 */

// Mock config và logger
global.config = {
  trading: {
    binance: {
      baseURL: 'https://fapi.binance.com',
      wsBaseURL: 'wss://fstream.binance.com/ws/'
    }
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

// Mock axios để simulate rate limiting
const axios = require('axios');
let requestCount = 0;
let shouldSimulateRateLimit = false;

const originalAxiosGet = axios.get;
axios.get = async (url, config) => {
  requestCount++;

  // Simulate rate limiting after 15 requests
  if (shouldSimulateRateLimit && requestCount > 15) {
    const error = new Error('Request failed with status code 418');
    error.response = { status: 418, statusText: "I'm a teapot" };
    throw error;
  }

  // Simulate successful response
  if (url.includes('/ticker/price')) {
    return {
      data: { price: '43250.50' }
    };
  } else if (url.includes('/ping')) {
    return { data: {} };
  } else if (url.includes('/ticker/24hr')) {
    return {
      data: [
        { symbol: 'BTCUSDT', quoteVolume: '1000000000' },
        { symbol: 'ETHUSDT', quoteVolume: '800000000' }
      ]
    };
  }

  return { data: {} };
};

// Mock require
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === 'axios') {
    return axios;
  }
  return originalRequire.apply(this, arguments);
};

async function testRateLimiting() {
  console.log('🧪 Testing Binance API Rate Limiting\n');
  console.log('====================================\n');

  const BinanceClient = require('./lib/trading/binanceClient');

  console.log('📊 Test 1: Normal API Calls (No Rate Limiting)');
  console.log('===============================================');

  requestCount = 0;
  shouldSimulateRateLimit = false;

  const startTime = Date.now();

  // Test multiple price requests
  const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOGEUSDT'];

  console.log('Making 5 price requests...');
  for (const symbol of symbols) {
    try {
      const price = await BinanceClient.getCurrentPrice(symbol);
      console.log(`  ${symbol}: ${price}`);
    } catch (error) {
      console.log(`  ${symbol}: ERROR - ${error.message}`);
    }
  }

  const duration1 = Date.now() - startTime;
  console.log(`\nCompleted in: ${duration1}ms`);
  console.log(`Requests made: ${requestCount}`);
  console.log(`Rate: ${(requestCount / (duration1 / 1000)).toFixed(2)} req/sec`);

  console.log('\n📊 Test 2: Rate Limited API Calls');
  console.log('=================================');

  requestCount = 0;
  shouldSimulateRateLimit = true;

  const startTime2 = Date.now();

  console.log('Making 20 price requests (should trigger rate limiting after 15)...');

  const testSymbols = [];
  for (let i = 1; i <= 20; i++) {
    testSymbols.push(`TEST${i}USDT`);
  }

  let successCount = 0;
  let errorCount = 0;

  for (const symbol of testSymbols) {
    try {
      const price = await BinanceClient.getCurrentPrice(symbol);
      successCount++;
      if (successCount <= 5 || successCount % 5 === 0) {
        console.log(`  ${symbol}: ${price} ✅`);
      }
    } catch (error) {
      errorCount++;
      if (errorCount <= 3) {
        console.log(`  ${symbol}: ERROR - ${error.message} ❌`);
      } else if (errorCount === 4) {
        console.log(`  ... (${testSymbols.length - successCount - 3} more errors) ❌`);
      }
    }
  }

  const duration2 = Date.now() - startTime2;
  console.log(`\nCompleted in: ${duration2}ms`);
  console.log(`Success: ${successCount}, Errors: ${errorCount}`);
  console.log(`Total requests made: ${requestCount}`);

  console.log('\n📊 Test 3: Cache Effectiveness');
  console.log('==============================');

  requestCount = 0;
  shouldSimulateRateLimit = false;

  console.log('Testing price cache (same symbol multiple times)...');

  const startTime3 = Date.now();

  // Request same symbol multiple times quickly
  for (let i = 0; i < 5; i++) {
    try {
      const price = await BinanceClient.getCurrentPrice('BTCUSDT');
      console.log(`  Request ${i + 1}: ${price}`);
    } catch (error) {
      console.log(`  Request ${i + 1}: ERROR - ${error.message}`);
    }
  }

  const duration3 = Date.now() - startTime3;
  console.log(`\nCache test completed in: ${duration3}ms`);
  console.log(`Actual API requests made: ${requestCount} (should be 1 due to cache)`);
  console.log(`Cache effectiveness: ${requestCount === 1 ? '✅ EXCELLENT' : '⚠️ NEEDS IMPROVEMENT'}`);

  console.log('\n📊 Test 4: Error Recovery');
  console.log('=========================');

  console.log('Testing connectivity...');
  const connectivity = await BinanceClient.testConnectivity();
  console.log(`Connectivity test: ${connectivity ? '✅ PASS' : '❌ FAIL'}`);

  const cacheStats = BinanceClient.getCacheStats();
  console.log('\nCache statistics:');
  console.log(`  Price cache size: ${cacheStats.priceCache.size}`);
  console.log(`  Cache timeout: ${cacheStats.priceCache.timeout}ms`);
  console.log(`  Request queue: ${cacheStats.requestQueue.pending} pending`);
  console.log(`  Rate limit: ${cacheStats.requestQueue.rateLimit} req/sec`);

  console.log('\n🎉 Test Summary');
  console.log('===============');
  console.log('✅ Rate Limiting Implemented:');
  console.log('   - Max 10 requests/second (safe limit)');
  console.log('   - Request queue with proper spacing');
  console.log('   - Exponential backoff for retries');
  console.log('✅ Error Handling Enhanced:');
  console.log('   - Specific handling for 418/429 errors');
  console.log('   - Automatic retry with backoff');
  console.log('   - Fallback to cached prices during errors');
  console.log('✅ Price Caching:');
  console.log('   - 2-second cache to reduce API calls');
  console.log('   - Fallback to cache during API errors');
  console.log('   - Significant reduction in API requests');

  console.log('\n📋 Expected Impact:');
  console.log('==================');
  console.log('- ✅ No more 418 "I\'m a teapot" errors');
  console.log('- ✅ Reduced API call frequency');
  console.log('- ✅ Better error recovery');
  console.log('- ✅ More stable price monitoring');
  console.log('- ✅ Compliance with Binance rate limits');

  // Cleanup
  BinanceClient.clearPriceCache();
}

// Run the test
if (require.main === module) {
  testRateLimiting().catch(console.error);
}

module.exports = { testRateLimiting };