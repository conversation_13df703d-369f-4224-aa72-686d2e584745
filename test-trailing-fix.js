/**
 * Test script để kiểm tra logic trailing stop đã được sửa
 * Đặc biệt kiểm tra lỗi breakeven cho lệnh SELL
 */

// Mock config và logger
global.config = {
  trading: {
    riskManagement: {
      stopLossPercent: 0.5,
      minRiskReward: 1.2
    }
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

// Mock TradingSignal model
const mockTradingSignal = {
  findByIdAndUpdate: async (id, update) => {
    console.log(`📝 Mock DB Update - Signal ${id}:`, update);
    return true;
  }
};

// Mock require để test
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === '../models/tradingSignal') {
    return mockTradingSignal;
  }
  return originalRequire.apply(this, arguments);
};

const TrailingManager = require('./lib/trading/trailingManager');

async function testTrailingLogic() {
  console.log('🧪 Testing Trailing Stop Logic Fix\n');

  // Test Case 1: SELL Signal Breakeven Logic
  console.log('📉 Test Case 1: SELL Signal Breakeven');
  console.log('=====================================');

  const sellSignal = {
    _id: 'test-sell-123',
    symbol: 'GIGGLEUSDT',
    type: 'SELL',
    entry: 110.020000,
    stopLoss: 110.570000,  // Original SL (higher than entry for SELL)
    takeProfit: 109.020000  // Original TP (lower than entry for SELL)
  };

  console.log('📊 Original SELL Signal:');
  console.log(`  Entry: ${sellSignal.entry}`);
  console.log(`  Original SL: ${sellSignal.stopLoss}`);
  console.log(`  Original TP: ${sellSignal.takeProfit}`);

  const riskAmount = sellSignal.stopLoss - sellSignal.entry; // 0.55
  const profitAmount = sellSignal.entry - sellSignal.takeProfit; // 1.0

  console.log(`  Risk Amount: ${riskAmount.toFixed(6)}`);
  console.log(`  Profit Amount: ${profitAmount.toFixed(6)}`);

  // Add signal to trailing
  TrailingManager.addSignalToTrailing(sellSignal);

  // Test scenarios
  const testPrices = [
    { price: 110.020000, desc: 'At Entry' },
    { price: 109.520000, desc: 'Small profit (0.5)' },
    { price: 109.020000, desc: 'At TP (1R profit)' },
    { price: 108.520000, desc: '1.5R profit' },
    { price: 108.020000, desc: '2R profit' }
  ];

  for (const test of testPrices) {
    console.log(`\n💰 Testing price: ${test.price} (${test.desc})`);

    await TrailingManager.updateTrailing('test-sell-123', test.price);

    const trailingInfo = TrailingManager.getTrailingInfo('test-sell-123');
    if (trailingInfo) {
      console.log(`  Current SL: ${trailingInfo.currentSL.toFixed(6)}`);
      console.log(`  Breakeven: ${trailingInfo.breakeven}`);
      console.log(`  Trailing Active: ${trailingInfo.trailingActive}`);
      console.log(`  Lowest Price: ${trailingInfo.lowestPrice?.toFixed(6) || 'N/A'}`);

      // Validate breakeven logic for SELL
      if (trailingInfo.breakeven) {
        const isValidBreakeven =
          trailingInfo.currentSL > sellSignal.entry &&           // Cao hơn entry (bảo vệ)
          trailingInfo.currentSL < sellSignal.stopLoss;          // Thấp hơn SL gốc (cải thiện)

        if (isValidBreakeven) {
          console.log('  ✅ CORRECT: SELL Breakeven SL is between entry and original SL');
          console.log(`     Entry: ${sellSignal.entry} < Breakeven SL: ${trailingInfo.currentSL} < Original SL: ${sellSignal.stopLoss}`);
        } else {
          console.log('  ❌ ERROR: SELL Breakeven SL is not in valid range!');
          console.log(`     Entry: ${sellSignal.entry}, Breakeven SL: ${trailingInfo.currentSL}, Original SL: ${sellSignal.stopLoss}`);
        }
      }
    }
  }

  console.log('\n📈 Test Case 2: BUY Signal Breakeven (for comparison)');
  console.log('====================================================');

  const buySignal = {
    _id: 'test-buy-123',
    symbol: 'BTCUSDT',
    type: 'BUY',
    entry: 43250.00,
    stopLoss: 43000.00,   // Original SL (lower than entry for BUY)
    takeProfit: 43500.00  // Original TP (higher than entry for BUY)
  };

  console.log('📊 Original BUY Signal:');
  console.log(`  Entry: ${buySignal.entry}`);
  console.log(`  Original SL: ${buySignal.stopLoss}`);
  console.log(`  Original TP: ${buySignal.takeProfit}`);

  TrailingManager.addSignalToTrailing(buySignal);

  const buyTestPrices = [
    { price: 43250.00, desc: 'At Entry' },
    { price: 43375.00, desc: 'Small profit (0.5R)' },
    { price: 43500.00, desc: 'At TP (1R profit)' },
    { price: 43625.00, desc: '1.5R profit' }
  ];

  for (const test of buyTestPrices) {
    console.log(`\n💰 Testing BUY price: ${test.price} (${test.desc})`);

    await TrailingManager.updateTrailing('test-buy-123', test.price);

    const trailingInfo = TrailingManager.getTrailingInfo('test-buy-123');
    if (trailingInfo) {
      console.log(`  Current SL: ${trailingInfo.currentSL.toFixed(2)}`);
      console.log(`  Breakeven: ${trailingInfo.breakeven}`);
      console.log(`  Trailing Active: ${trailingInfo.trailingActive}`);

      // Validate breakeven logic
      if (trailingInfo.breakeven && trailingInfo.currentSL < buySignal.entry) {
        console.log('  ❌ ERROR: Breakeven SL is LOWER than entry for BUY!');
      } else if (trailingInfo.breakeven) {
        console.log('  ✅ CORRECT: Breakeven SL is higher than entry for BUY');
      }
    }
  }

  console.log('\n🎯 Test Summary');
  console.log('================');
  console.log('✅ Fixed SELL breakeven logic:');
  console.log('   - SELL breakeven SL should be LOWER than entry');
  console.log('   - Uses profitAmount instead of riskAmount for calculations');
  console.log('   - Proper validation of price movements');
  console.log('\n✅ BUY logic remains correct:');
  console.log('   - BUY breakeven SL should be HIGHER than entry');
  console.log('   - Uses riskAmount for calculations');

  console.log('\n🔧 Key Changes Made:');
  console.log('1. Separated riskAmount and profitAmount calculations');
  console.log('2. Fixed breakeven SL calculation for SELL signals');
  console.log('3. Added proper logging with signal type identification');
  console.log('4. Corrected trailing distance calculations');

  // Cleanup
  TrailingManager.removeSignalFromTrailing('test-sell-123');
  TrailingManager.removeSignalFromTrailing('test-buy-123');
}

// Run the test
if (require.main === module) {
  testTrailingLogic().catch(console.error);
}

module.exports = { testTrailingLogic };