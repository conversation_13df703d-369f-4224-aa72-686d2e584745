# ScalpWizard Trading Bot Environment Variables

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=-1003287098255

# Binance API (Optional - chỉ cần nếu muốn trade thật)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here

# Database Configuration
MONGO_HOST=************
MONGO_PORT=27227
MONGO_DATABASE=wizard-management
MONGO_USER=
MONGO_PASS=

# Redis Configuration
REDIS_HOST=************
REDIS_PORT=6379
REDIS_DATABASE=15
REDIS_PASSWORD=mmjjnnhh

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
PORT=6886

# Trading Configuration
MAX_COINS_TO_TRACK=50
UPDATE_INTERVAL_HOURS=1
STOP_LOSS_PERCENT=0.5
TAKE_PROFIT_PERCENT_1=1.0
TAKE_PROFIT_PERCENT_2=2.0

# Notification Settings
ENABLE_TELEGRAM=true
ENABLE_EMAIL_ALERTS=false
