# ScalpWizard - Hướng Dẫn Chi Tiết

## 📋 <PERSON>
1. [<PERSON><PERSON><PERSON><PERSON> Thiệu](#giới-thiệu)
2. [<PERSON><PERSON><PERSON> Đặt Chi Tiết](#cài-đặt-chi-tiết)
3. [<PERSON><PERSON><PERSON>](#c<PERSON><PERSON>-hình)
4. [S<PERSON> Dụng](#sử-dụng)
5. [API Reference](#api-reference)
6. [Troubleshooting](#troubleshooting)
7. [FAQ](#faq)

## 🎯 Giới Thiệu

ScalpWizard là bot trading tự động được thiết kế để:
- Phân tích tín hiệu kỹ thuật real-time
- G<PERSON>i thông báo qua Telegram
- Theo dõi và quản lý kết quả lệnh
- Thống kê performance tự động

### Luồng Hoạt Động
```
Binance API → Market Data → Technical Analysis → Signal → Telegram → Order Tracking → Results
```

## 🛠️ Cài Đặt Chi Tiết

### Bước 1: <PERSON><PERSON><PERSON> Bị Môi Trường

#### MongoDB
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mongodb

# macOS
brew install mongodb-community

# Windows
# Download từ https://www.mongodb.com/try/download/community
```

#### Redis (Optional)
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis

# Windows
# Download từ https://redis.io/download
```

#### Node.js
```bash
# Sử dụng nvm (khuyến nghị)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

### Bước 2: Tạo Telegram Bot

1. Mở Telegram, tìm @BotFather
2. Gửi `/newbot`
3. Đặt tên bot: `ScalpWizard Bot`
4. Đặt username: `scalpwizard_yourname_bot`
5. Lưu Bot Token

### Bước 3: Lấy Chat ID

```bash
# Gửi tin nhắn cho bot, sau đó chạy:
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates

# Tìm "chat":{"id": trong response
```

### Bước 4: Clone và Cài Đặt

```bash
git clone <repository-url>
cd ScalpWizard
npm install
cp .env.example .env
```

## ⚙️ Cấu Hình

### File .env
```env
# Telegram
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=-1003287098255

# Database
MONGO_HOST=************
MONGO_PORT=27227
MONGO_DATABASE=wizard-management

# Application
NODE_ENV=production
PORT=6886
LOG_LEVEL=info
```

### File config/default.json

#### Trading Configuration
```json
{
  "trading": {
    "binance": {
      "baseURL": "https://fapi.binance.com",
      "wsBaseURL": "wss://fstream.binance.com/ws/",
      "topCoinsUpdateInterval": "0 0 */1 * * *",
      "maxCoinsToTrack": 50
    },
    "timeframes": ["1m", "5m", "15m"],
    "indicators": {
      "ema": {
        "fast": 50,
        "slow": 200
      },
      "macd": {
        "fast": 12,
        "slow": 26,
        "signal": 9
      },
      "rsi": {
        "period": 14,
        "buyZone": [55, 65],
        "sellZone": [35, 45]
      }
    },
    "riskManagement": {
      "stopLossPercent": 0.5,
      "takeProfitPercent": [1, 2],
      "riskRewardRatio": [1.5, 2]
    }
  }
}
```

#### Telegram Configuration
```json
{
  "telegram": {
    "botToken": "**********************************************",
    "chatId": "-1003287098255",
    "enabled": true,
    "messageFormat": {
      "signal": "🚀 **TÍNH HIỆU TRADING** 🚀\n\n📊 **Cặp:** {symbol}\n⏰ **Thời gian:** {time}\n📈 **Loại lệnh:** {type}\n💰 **Entry:** {entry}\n🛑 **Stop Loss:** {sl}\n🎯 **Take Profit:** {tp}\n\n📋 **Chỉ báo:**\n{indicators}\n\n#ScalpWizard #{symbol}",
      "result": "📊 **KẾT QUẢ LỆNH** 📊\n\n📊 **Cặp:** {symbol}\n⏰ **Thời gian đóng:** {closeTime}\n📈 **Loại lệnh:** {type}\n💰 **Entry:** {entry}\n🏁 **Exit:** {exit}\n📊 **Kết quả:** {result}\n💵 **P&L:** {pnl}%\n\n📈 **Thống kê tổng:**\n✅ **Win:** {totalWin}\n❌ **Loss:** {totalLoss}\n📊 **Win Rate:** {winRate}%\n\n#ScalpWizard #{symbol}"
    }
  }
}
```

## 🚀 Sử Dụng

### Khởi Động Bot

#### Development Mode
```bash
npm run dev
```

#### Production Mode
```bash
npm start

# Hoặc sử dụng PM2
npm install -g pm2
pm2 start index.js --name "scalpwizard"
pm2 save
pm2 startup
```

### Kiểm Tra Trạng Thái

```bash
# Health check
curl http://localhost:6886/health

# Trading status
curl http://localhost:6886/api/v1/trading/status

# Statistics
curl http://localhost:6886/api/v1/trading/statistics?days=30
```

### Logs

```bash
# Xem logs real-time
tail -f logs/system-*.log

# Xem logs lỗi
grep "ERROR" logs/system-*.log

# Xem logs trading
grep "Signal" logs/system-*.log
```

## 📊 API Reference

### GET /api/v1/trading/status
Lấy trạng thái tổng quan hệ thống

**Response:**
```json
{
  "marketData": {
    "isInitialized": true,
    "trackedSymbolsCount": 50,
    "trackedSymbols": ["BTCUSDT", "ETHUSDT", ...],
    "timeframes": ["5m", "15m"],
    "connectionStatus": {
      "BTCUSDT_5m": "connected",
      "BTCUSDT_15m": "connected"
    }
  },
  "signal": {
    "isRunning": true,
    "processingQueue": true,
    "queueSize": 5,
    "maxQueueSize": 100
  },
  "orderManager": {
    "isActive": true,
    "activeSignalsCount": 3,
    "activeSignals": [...]
  },
  "scheduler": {
    "isRunning": true,
    "jobCount": 5,
    "jobs": {...}
  },
  "timestamp": "2024-10-28T10:30:00.000Z"
}
```

### GET /api/v1/trading/statistics?days=30
Lấy thống kê trading

**Parameters:**
- `days` (optional): Số ngày thống kê (default: 30)

**Response:**
```json
{
  "totalTrades": 150,
  "winTrades": 95,
  "lossTrades": 55,
  "winRate": 63.33,
  "totalPnL": 12.45,
  "avgPnL": 0.083
}
```

### GET /api/v1/trading/signals/recent?limit=10
Lấy signals gần đây

**Parameters:**
- `limit` (optional): Số lượng signals (default: 10)

**Response:**
```json
[
  {
    "_id": "...",
    "symbol": "BTCUSDT",
    "timeframe": "5m",
    "type": "BUY",
    "entry": 43250.50,
    "stopLoss": 43035.00,
    "takeProfit": 43682.00,
    "status": "hit_tp",
    "createdAt": "2024-10-28T10:15:00.000Z",
    "exitTime": "2024-10-28T10:45:00.000Z",
    "pnlPercent": 1.02
  }
]
```

## 🔧 Troubleshooting

### Lỗi Thường Gặp

#### 1. MongoDB Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:27017
```

**Giải pháp:**
```bash
# Kiểm tra MongoDB đang chạy
sudo systemctl status mongod

# Khởi động MongoDB
sudo systemctl start mongod

# Kiểm tra kết nối
mongosh mongodb://localhost:27017/test
```

#### 2. Telegram Bot Error
```
Error: 401 Unauthorized
```

**Giải pháp:**
- Kiểm tra Bot Token trong .env
- Đảm bảo bot đã được start bằng cách gửi `/start`
- Kiểm tra Chat ID đúng format

#### 3. WebSocket Connection Error
```
WebSocket connection failed
```

**Giải pháp:**
- Kiểm tra kết nối internet
- Restart bot
- Kiểm tra Binance API status

#### 4. High Memory Usage
```
Process killed due to memory limit
```

**Giải pháp:**
- Giảm `maxCoinsToTrack` trong config
- Tăng RAM server
- Optimize database queries

### Debug Mode

```bash
# Chạy với debug logs
DEBUG=* npm run dev

# Chỉ debug trading modules
DEBUG=trading:* npm run dev
```

### Performance Monitoring

```bash
# CPU và Memory usage
top -p $(pgrep -f "node.*index.js")

# Database connections
mongosh --eval "db.serverStatus().connections"

# WebSocket connections
netstat -an | grep :443 | wc -l
```

## ❓ FAQ

### Q: Bot có tự động trade không?
A: Không, bot chỉ phân tích và gửi tín hiệu. Bạn cần tự vào lệnh.

### Q: Làm sao để thay đổi coins theo dõi?
A: Bot tự động lấy top 30 coins volume cao nhất. Có thể thay đổi số lượng trong config.

### Q: Tại sao không nhận được thông báo Telegram?
A: Kiểm tra Bot Token, Chat ID, và đảm bảo bot đã được start.

### Q: Làm sao để backup dữ liệu?
A: Sử dụng mongodump để backup MongoDB và copy folder logs.

### Q: Bot có hoạt động 24/7 không?
A: Có, bot được thiết kế để chạy liên tục với auto-reconnect.

### Q: Làm sao để tùy chỉnh điều kiện tín hiệu?
A: Chỉnh sửa file `lib/trading/signalAnalyzer.js` và config indicators.

### Q: Bot có hỗ trợ multiple timeframes?
A: Có, hiện tại hỗ trợ 5m và 15m, có thể thêm trong config.

### Q: Làm sao để xem performance của từng coin?
A: Sử dụng API endpoint hoặc kiểm tra logs/statistics backup.

---

**Cần hỗ trợ thêm? Liên hệ qua Telegram hoặc tạo issue trên GitHub! 🚀**
