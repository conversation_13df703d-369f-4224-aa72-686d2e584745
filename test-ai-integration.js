const AIClient = require('./lib/ai/aiClient');
const AIPerformanceAnalyzer = require('./lib/ai/aiPerformanceAnalyzer');
const AIResponseParser = require('./lib/ai/aiResponseParser');
const AIFallbackManager = require('./lib/ai/aiFallbackManager');

/**
 * Simple integration test for AI components
 */
async function testAIIntegration() {
  console.log('🤖 Testing AI Integration Infrastructure...\n');

  try {
    // Test 1: AI Client initialization and configuration
    console.log('1. Testing AI Client initialization...');
    const aiClient = new AIClient();
    const config = aiClient.getConfig();
    console.log('✅ AI Client initialized successfully');
    console.log(`   Model: ${config.model}`);
    console.log(`   URL: ${config.url}`);
    console.log(`   Timeout: ${config.timeout}ms\n`);

    // Test 2: AI Response Parser
    console.log('2. Testing AI Response Parser...');
    const parser = new AIResponseParser();

    const testResponse = JSON.stringify({
      insights: [{
        category: 'risk_management',
        finding: 'Test finding for integration',
        impact: 'medium',
        confidence: 75
      }],
      recommendations: [{
        type: 'parameter_change',
        description: 'Test recommendation',
        expectedImprovement: 'Test improvement',
        riskLevel: 'low',
        priority: 6
      }],
      confidenceScore: 80
    });

    const parseResult = parser.parsePerformanceAnalysis(testResponse);
    console.log('✅ Response parser working correctly');
    console.log(`   Parsed insights: ${parseResult.data.insights.length}`);
    console.log(`   Parsed recommendations: ${parseResult.data.recommendations.length}`);
    console.log(`   Confidence score: ${parseResult.data.confidenceScore}\n`);

    // Test 3: Fallback Manager
    console.log('3. Testing AI Fallback Manager...');
    const fallbackManager = new AIFallbackManager();

    const mockPerformanceData = {
      totalTrades: 50,
      winRate: 45,
      profitFactor: 1.1,
      maxDrawdown: 15,
      consecutiveLosses: 4,
      symbolStats: [
        { symbol: 'BTCUSDT', profitFactor: 0.8, winRate: 40 },
        { symbol: 'ETHUSDT', profitFactor: 1.5, winRate: 60 }
      ]
    };

    const fallbackResult = fallbackManager.generatePerformanceFallback(mockPerformanceData);
    console.log('✅ Fallback manager working correctly');
    console.log(`   Generated insights: ${fallbackResult.insights.length}`);
    console.log(`   Generated recommendations: ${fallbackResult.recommendations.length}`);
    console.log(`   Risk level: ${fallbackResult.riskAssessment.currentRiskLevel}`);
    console.log(`   Confidence: ${fallbackResult.confidenceScore}\n`);

    // Test 4: Performance Analyzer (without actual AI call)
    console.log('4. Testing Performance Analyzer initialization...');
    const performanceAnalyzer = new AIPerformanceAnalyzer({
      autoImplement: false
    });
    console.log('✅ Performance analyzer initialized successfully\n');

    // Test 5: AI Client connectivity test (optional - only if service is available)
    console.log('5. Testing AI service connectivity...');
    try {
      const connectionTest = await aiClient.testConnection();
      if (connectionTest.success) {
        console.log('✅ AI service is reachable');
        console.log(`   Response: ${connectionTest.response.substring(0, 100)}...\n`);
      } else {
        console.log('⚠️  AI service not reachable (this is expected in test environment)');
        console.log(`   Error: ${connectionTest.error}\n`);
      }
    } catch (error) {
      console.log('⚠️  AI service connectivity test failed (expected in test environment)');
      console.log(`   Error: ${error.message}\n`);
    }

    // Test 6: Integration with fallback
    console.log('6. Testing complete integration with fallback...');

    const mockAIFunction = async () => {
      throw new Error('Simulated AI service failure');
    };

    const integrationResult = await fallbackManager.executeWithFallback(
      mockAIFunction,
      mockPerformanceData,
      { analysisType: 'performance' }
    );

    console.log('✅ Integration with fallback working correctly');
    console.log(`   Result source: ${integrationResult.source}`);
    console.log(`   Success: ${integrationResult.success}`);
    console.log(`   Has warning: ${!!integrationResult.warning}\n`);

    // Test 7: Response parser error handling
    console.log('7. Testing error handling...');

    const invalidResponse = 'This is not valid JSON';
    const errorResult = parser.parsePerformanceAnalysis(invalidResponse);

    console.log('✅ Error handling working correctly');
    console.log(`   Parse success: ${errorResult.success}`);
    console.log(`   Has safe defaults: ${!!errorResult.data}\n`);

    console.log('🎉 All AI integration tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ AI Client - Ready');
    console.log('   ✅ Response Parser - Ready');
    console.log('   ✅ Fallback Manager - Ready');
    console.log('   ✅ Performance Analyzer - Ready');
    console.log('   ✅ Error Handling - Ready');
    console.log('   ✅ Integration Flow - Ready');

    return true;

  } catch (error) {
    console.error('❌ AI integration test failed:', error.message);
    console.error(error.stack);
    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testAIIntegration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testAIIntegration;