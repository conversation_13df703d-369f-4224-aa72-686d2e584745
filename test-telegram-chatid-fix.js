/**
 * Test script để verify fix cho lỗi chat_id is empty
 */

// Mock config
global.config = {
  telegram: {
    enabled: true,
    botToken: "**********************************************",
    chatId: "-1003287098255"
  }
};

global.logger = {
  logInfo: console.log,
  logError: console.error,
  warn: console.warn
};

global.moment = require('moment');

async function testTelegramChatIdFix() {
  console.log('🧪 Testing Telegram ChatId Fix\\n');
  console.log('==============================\\n');

  try {
    console.log('📊 Config Check:');
    console.log('================');
    console.log('Telegram enabled:', config.telegram.enabled);
    console.log('Bot token:', config.telegram.botToken ? `${config.telegram.botToken.substring(0, 10)}...` : 'NOT SET');
    console.log('Chat ID:', config.telegram.chatId);

    // Load TelegramBot
    const TelegramBot = require('./lib/trading/telegramBot');

    console.log('\\n🤖 Bot Initialization Check:');
    console.log('=============================');
    console.log('Bot enabled:', TelegramBot.isEnabled);
    console.log('Bot exists:', !!TelegramBot.bot);
    console.log('Config loaded:', !!TelegramBot.config);
    console.log('Config chatId:', TelegramBot.config?.chatId);

    if (!TelegramBot.isEnabled) {
      console.log('❌ Telegram bot is not enabled');
      return;
    }

    if (!TelegramBot.bot) {
      console.log('❌ Telegram bot is not initialized');
      return;
    }

    // Test bot connection first
    console.log('\\n🔗 Testing Bot Connection:');
    console.log('===========================');

    try {
      const botInfo = await TelegramBot.bot.getMe();
      console.log('✅ Bot connection successful');
      console.log('Bot info:', {
        id: botInfo.id,
        username: botInfo.username,
        first_name: botInfo.first_name
      });
    } catch (error) {
      console.log('❌ Bot connection failed:', error.message);
      return;
    }

    // Test sendSystemNotification (the method that was broken)
    console.log('\\n📱 Testing sendSystemNotification (Fixed Method):');
    console.log('=================================================');

    const testSystemMessage = `🧪 <b>TEST SYSTEM NOTIFICATION</b> 🧪

🔧 <b>Testing ChatId Fix</b>

⏰ <b>Time:</b> ${moment().format('DD/MM/YYYY HH:mm:ss')}
🔧 <b>Fix:</b> Changed this.chatId → this.config.chatId
✅ <b>Status:</b> Should work now!

#Test #SystemNotification #ChatIdFix`;

    console.log('Sending system notification...');

    const systemResult = await TelegramBot.sendSystemNotification(testSystemMessage);

    if (systemResult) {
      console.log('✅ System notification sent successfully!');
    } else {
      console.log('❌ Failed to send system notification');
    }

    // Test other notification methods to ensure they still work
    console.log('\\n📱 Testing Other Notification Methods:');
    console.log('======================================');

    // Test sendSignalNotification
    const mockSignal = {
      symbol: 'TESTUSDT',
      timeframe: '5m',
      type: 'BUY',
      entry: 100,
      stopLoss: 95,
      takeProfit: 105,
      riskReward: 2.0,
      tpMethod: 'percentage',
      riskAmount: 10,
      rewardAmount: 20,
      riskPercent: 1,
      rewardPercent: 2,
      indicators: {
        ema50: 99,
        ema200: 98,
        macd: { macd: 0.5, signal: 0.3, histogram: 0.2 },
        rsi: 65,
        engulfing: { type: 'bullish' },
        strongBody: { isStrong: true, bodyPercent: 80, direction: 'bullish' }
      },
      marketData: {
        open: 99.5,
        high: 100.5,
        low: 99,
        close: 100,
        volume: 1000000
      },
      conditions: 'EMA Crossover + RSI Bullish + Strong Body',
      createdAt: new Date()
    };

    console.log('Testing sendSignalNotification...');
    const signalResult = await TelegramBot.sendSignalNotification(mockSignal);

    if (signalResult) {
      console.log(`✅ Signal notification sent successfully! Message ID: ${signalResult}`);
    } else {
      console.log('❌ Failed to send signal notification');
    }

    // Test testConflictNotification
    console.log('\\nTesting testConflictNotification...');
    const conflictResult = await TelegramBot.testConflictNotification();

    if (conflictResult) {
      console.log(`✅ Test conflict notification sent successfully! Message ID: ${conflictResult}`);
    } else {
      console.log('❌ Failed to send test conflict notification');
    }

    console.log('\\n🎉 Test Summary:');
    console.log('================');
    console.log('✅ ChatId fix verification completed');
    console.log('✅ All notification methods tested');
    console.log('✅ Check Telegram chat for actual messages');

    console.log('\\n📋 Fix Details:');
    console.log('===============');
    console.log('❌ Before: await this.bot.sendMessage(this.chatId, message, options)');
    console.log('✅ After:  await this.bot.sendMessage(this.config.chatId, message, options)');
    console.log('');
    console.log('🔧 Root Cause: sendSystemNotification was using undefined this.chatId');
    console.log('🔧 Solution: Changed to this.config.chatId like other methods');
    console.log('🔧 Impact: System notifications (startup, cleanup, etc.) now work');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testTelegramChatIdFix().catch(console.error);
}

module.exports = { testTelegramChatIdFix };