#!/usr/bin/env node

/**
 * ScalpWizard Test Script
 * Script để test các chức năng ch<PERSON>h của bot
 */

require('dotenv').config();
const axios = require('axios');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

class BotTester {
  constructor() {
    this.baseURL = 'http://localhost:6886';
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Bắt đầu test ScalpWizard Bot...\n');

    try {
      // Test 1: Kết nối MongoDB
      await this.testMongoConnection();

      // Test 2: Test Telegram Bot
      await this.testTelegramBot();

      // Test 3: Test Binance API
      await this.testBinanceAPI();

      // Test 4: Test Technical Indicators
      await this.testTechnicalIndicators();

      // Test 5: Test Signal Analysis
      await this.testSignalAnalysis();

      // Test 6: Test API Endpoints
      await this.testAPIEndpoints();

      // Hiển thị kết quả
      this.displayResults();

    } catch (error) {
      console.error('❌ Lỗi trong quá trình test:', error.message);
    }
  }

  async testMongoConnection() {
    console.log('📊 Test 1: Kết nối MongoDB...');

    try {
      const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
      await mongoose.connect(mongoUri, config.mongo.connections.master.options);

      // Test insert/read
      const testDoc = new TradingSignalModel({
        symbol: 'TESTUSDT',
        timeframe: '5m',
        type: 'BUY',
        entry: 100,
        stopLoss: 99,
        takeProfit: 101,
        indicators: {
          ema50: 100,
          ema200: 99,
          rsi: 60
        },
        marketData: {
          open: 100,
          high: 101,
          low: 99,
          close: 100,
          volume: 1000
        }
      });

      await testDoc.save();
      await testDoc.deleteOne();

      this.addResult('MongoDB Connection', true, 'Kết nối và CRUD thành công');
      console.log('✅ MongoDB: OK\n');

    } catch (error) {
      this.addResult('MongoDB Connection', false, error.message);
      console.log('❌ MongoDB: FAILED -', error.message, '\n');
    }
  }

  async testTelegramBot() {
    console.log('📱 Test 2: Telegram Bot...');

    try {
      const telegramBot = require('./lib/trading/telegramBot');

      // Test connection
      const isConnected = await telegramBot.testConnection();

      if (isConnected) {
        // Test send message
        const testSignal = {
          symbol: 'BTCUSDT',
          timeframe: '1m',
          type: 'BUY',
          entry: 43250.50,
          stopLoss: 43035.00,
          takeProfit: 43682.00,
          indicators: {
            ema50: 43200,
            ema200: 43000,
            macd: { macd: 0.5, signal: 0.3, histogram: 0.2 },
            rsi: 62,
            engulfing: 'bullish'
          }
        };

        await telegramBot.sendSignalNotification(testSignal);
        this.addResult('Telegram Bot', true, 'Kết nối và gửi tin nhắn thành công');
        console.log('✅ Telegram: OK\n');
      } else {
        throw new Error('Không thể kết nối Telegram Bot');
      }

    } catch (error) {
      this.addResult('Telegram Bot', false, error.message);
      console.log('❌ Telegram: FAILED -', error.message, '\n');
    }
  }

  async testBinanceAPI() {
    console.log('🔗 Test 3: Binance API...');

    try {
      const binanceClient = require('./lib/trading/binanceClient');

      // Test get top coins
      const topCoins = await binanceClient.getTop24hVolumeCoins(5);

      if (topCoins && topCoins.length > 0) {
        console.log('📊 Top 5 coins:', topCoins.slice(0, 5));

        // Test get klines
        const klines = await binanceClient.getKlines(topCoins[0], '1m', 10);

        if (klines && klines.length > 0) {
          console.log('📈 Sample kline:', {
            symbol: topCoins[0],
            close: klines[klines.length - 1].close,
            volume: klines[klines.length - 1].volume
          });

          this.addResult('Binance API', true, `Lấy được ${topCoins.length} coins và ${klines.length} klines`);
          console.log('✅ Binance API: OK\n');
        } else {
          throw new Error('Không lấy được klines data');
        }
      } else {
        throw new Error('Không lấy được top coins');
      }

    } catch (error) {
      this.addResult('Binance API', false, error.message);
      console.log('❌ Binance API: FAILED -', error.message, '\n');
    }
  }

  async testTechnicalIndicators() {
    console.log('📊 Test 4: Technical Indicators...');

    try {
      const indicators = require('./lib/trading/indicators');

      // Tạo sample data
      const sampleCandles = [];
      for (let i = 0; i < 200; i++) {
        const basePrice = 43000 + Math.random() * 1000;
        sampleCandles.push({
          open: basePrice,
          high: basePrice + Math.random() * 100,
          low: basePrice - Math.random() * 100,
          close: basePrice + (Math.random() - 0.5) * 50,
          volume: 1000 + Math.random() * 5000
        });
      }

      // Test calculate indicators
      const indicatorData = indicators.calculateAllIndicators(sampleCandles);

      if (indicatorData && indicatorData.ema50 && indicatorData.ema200 && indicatorData.rsi) {
        console.log('📊 Sample indicators:', {
          ema50: indicatorData.ema50.toFixed(2),
          ema200: indicatorData.ema200.toFixed(2),
          rsi: indicatorData.rsi.toFixed(2),
          macd: indicatorData.macd ? indicatorData.macd.macd.toFixed(4) : 'N/A',
          engulfing: indicatorData.engulfing
        });

        this.addResult('Technical Indicators', true, 'Tính toán indicators thành công');
        console.log('✅ Technical Indicators: OK\n');
      } else {
        throw new Error('Không tính được indicators');
      }

    } catch (error) {
      this.addResult('Technical Indicators', false, error.message);
      console.log('❌ Technical Indicators: FAILED -', error.message, '\n');
    }
  }

  async testSignalAnalysis() {
    console.log('🎯 Test 5: Signal Analysis...');

    try {
      const signalAnalyzer = require('./lib/trading/signalAnalyzer');

      // Tạo sample candles với trend tăng
      const sampleCandles = [];
      for (let i = 0; i < 200; i++) {
        const basePrice = 43000 + i * 2; // Trend tăng
        sampleCandles.push({
          openTime: new Date(Date.now() - (200 - i) * 5 * 60 * 1000),
          closeTime: new Date(Date.now() - (200 - i - 1) * 5 * 60 * 1000),
          open: basePrice,
          high: basePrice + 20,
          low: basePrice - 10,
          close: basePrice + 5,
          volume: 1000 + Math.random() * 5000
        });
      }

      // Test analyze signal
      const signal = await signalAnalyzer.analyzeSignal('TESTUSDT', '1m', sampleCandles);

      if (signal) {
        console.log('🎯 Generated signal:', {
          symbol: signal.symbol,
          type: signal.type,
          entry: signal.entry,
          stopLoss: signal.stopLoss,
          takeProfit: signal.takeProfit
        });
        this.addResult('Signal Analysis', true, `Tạo được signal ${signal.type} cho ${signal.symbol}`);
      } else {
        this.addResult('Signal Analysis', true, 'Không có signal (điều kiện chưa đủ)');
      }

      console.log('✅ Signal Analysis: OK\n');

    } catch (error) {
      this.addResult('Signal Analysis', false, error.message);
      console.log('❌ Signal Analysis: FAILED -', error.message, '\n');
    }
  }

  async testAPIEndpoints() {
    console.log('🌐 Test 6: API Endpoints...');

    try {
      // Kiểm tra xem server có đang chạy không
      try {
        const healthResponse = await axios.get(`${this.baseURL}/health`, { timeout: 5000 });
        console.log('💚 Health check:', healthResponse.data);

        // Đợi một chút để server khởi động đầy đủ
        await this.sleep(2000);

        // Test trading status (có thể fail nếu services chưa init)
        try {
          const statusResponse = await axios.get(`${this.baseURL}/api/v1/trading/status`);
          console.log('📊 Trading status:', {
            marketData: statusResponse.data.marketData?.isInitialized || false,
            signal: statusResponse.data.signal?.isRunning || false,
            orderManager: statusResponse.data.orderManager?.isActive || false
          });
        } catch (statusError) {
          console.log('⚠️ Trading status: Services đang khởi động...');
        }

        this.addResult('API Endpoints', true, 'Server đang chạy và API hoạt động');
        console.log('✅ API Endpoints: OK\n');

      } catch (connectionError) {
        // Server chưa chạy
        console.log('⚠️ Server chưa khởi động. Đây là điều bình thường khi test riêng lẻ.');
        this.addResult('API Endpoints', true, 'Server chưa chạy (test offline)');
        console.log('✅ API Endpoints: OK (offline test)\n');
      }

    } catch (error) {
      this.addResult('API Endpoints', false, error.message);
      console.log('❌ API Endpoints: FAILED -', error.message, '\n');
    }
  }

  addResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
      timestamp: new Date()
    });
  }

  displayResults() {
    console.log('📋 KẾT QUẢ TEST TỔNG QUAN');
    console.log('='.repeat(50));

    let passedTests = 0;
    let totalTests = this.testResults.length;

    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${result.test}: ${status}`);
      console.log(`   ${result.message}`);

      if (result.success) passedTests++;
    });

    console.log('='.repeat(50));
    console.log(`📊 Tổng kết: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log('🎉 Tất cả tests đều PASS! Bot sẵn sàng hoạt động.');
    } else {
      console.log('⚠️ Một số tests FAILED. Kiểm tra lại cấu hình.');
    }

    // Lưu kết quả vào file
    const reportFile = `logs/test-report-${moment().format('YYYY-MM-DD-HH-mm-ss')}.json`;
    fs.writeFileSync(reportFile, JSON.stringify({
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        timestamp: new Date()
      },
      results: this.testResults
    }, null, 2));

    console.log(`📄 Chi tiết test đã lưu vào: ${reportFile}`);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Chạy tests
if (require.main === module) {
  const tester = new BotTester();
  tester.runAllTests().catch(console.error);
}

module.exports = BotTester;
