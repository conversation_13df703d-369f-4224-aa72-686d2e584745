#!/usr/bin/env node

/**
 * Test cụ thể cho 42USDT để kiểm tra EMA200 và so sánh với sàn Binance
 */

require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

async function test42USDTAnalysis() {
  console.log('🔍 Testing 42USDT Analysis vs Binance Exchange...\n');

  try {
    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    console.log('✅ MongoDB connected\n');

    // Load services
    const binanceClient = require('./lib/trading/binanceClient');
    const indicators = require('./lib/trading/indicators');

    // Test với nhiều timeframes
    const timeframes = ['5m'];
    
    for (const timeframe of timeframes) {
      console.log(`\n🔍 Testing ${timeframe} timeframe for 42USDT:`);
      console.log('='.repeat(60));

      // Lấy dữ liệu 42USDT thực từ Binance
      console.log(`📊 Fetching 42USDT ${timeframe} data...`);
      const klines = await binanceClient.getKlines('42USDT', timeframe, 500); // Lấy nhiều hơn để đảm bảo đủ data
      
      if (!klines || klines.length < 200) {
        console.log(`❌ Không đủ dữ liệu 42USDT ${timeframe}: ${klines?.length || 0} nến`);
        continue;
      }

      console.log(`✅ Lấy được ${klines.length} nến 42USDT ${timeframe}`);
      console.log(`📅 Từ: ${klines[0].openTime}`);
      console.log(`📅 Đến: ${klines[klines.length - 1].openTime}`);
      console.log(`💰 Giá hiện tại: ${klines[klines.length - 1].close}`);

      // Kiểm tra thứ tự dữ liệu
      const isAscending = klines[0].openTime <= klines[klines.length - 1].openTime;
      console.log(`📊 Thứ tự dữ liệu: ${isAscending ? 'Tăng dần ✅' : 'Giảm dần ❌'}`);

      // Tính toán indicators với dữ liệu đầy đủ
      console.log('\n🔢 Calculating indicators...');
      const indicatorData = indicators.calculateAllIndicators(klines);

      if (!indicatorData) {
        console.log('❌ Không tính được indicators');
        continue;
      }

      console.log('\n📊 42USDT Indicators (Our Calculation):');
      console.log(`💰 Entry (Current Price): ${indicatorData.currentPrice}`);
      console.log(`📈 EMA50: ${indicatorData.ema50}`);
      console.log(`📈 EMA200: ${indicatorData.ema200}`);
      console.log(`📊 MACD: ${indicatorData.macd?.macd || 'N/A'}`);
      console.log(`📊 Signal: ${indicatorData.macd?.signal || 'N/A'}`);
      console.log(`📊 RSI: ${indicatorData.rsi}`);

      // So sánh với dữ liệu từ ảnh sàn Binance
      console.log('\n🔍 COMPARISON WITH BINANCE EXCHANGE:');
      console.log('From Binance Exchange (Screenshot):');
      console.log('- Current Price: ~0.16801');
      console.log('- EMA(9): 0.16756 (Yellow line)');
      console.log('- EMA(21): 0.16709 (Green line)'); 
      console.log('- EMA(200): 0.17348 (Red line) ⚠️');
      console.log('- RSI: 52.95051');
      console.log('');
      console.log('From our calculation:');
      console.log(`- Current Price: ${indicatorData.currentPrice}`);
      console.log(`- EMA50: ${indicatorData.ema50}`);
      console.log(`- EMA200: ${indicatorData.ema200} ⚠️`);
      console.log(`- RSI: ${indicatorData.rsi}`);

      // Tính chênh lệch EMA200
      const binanceEMA200 = 0.17348;
      const ourEMA200 = indicatorData.ema200;
      const difference = Math.abs(binanceEMA200 - ourEMA200);
      const percentDiff = (difference / binanceEMA200) * 100;

      console.log('\n⚠️ EMA200 ANALYSIS:');
      console.log(`Binance EMA200: ${binanceEMA200}`);
      console.log(`Our EMA200: ${ourEMA200}`);
      console.log(`Difference: ${difference.toFixed(6)} (${percentDiff.toFixed(2)}%)`);
      
      if (percentDiff > 1) {
        console.log('🚨 CRITICAL: EMA200 difference > 1%! Possible issues:');
        console.log('1. Different data source or timeframe');
        console.log('2. Different calculation method');
        console.log('3. Data synchronization issue');
        console.log('4. Binance might use different EMA settings');
      }

      // Debug: Kiểm tra dữ liệu gần đây
      console.log('\n🔍 RECENT DATA ANALYSIS:');
      const recentCandles = klines.slice(-10);
      console.log('Last 10 candles:');
      recentCandles.forEach((candle, i) => {
        console.log(`${i+1}. ${candle.openTime.toISOString()}: O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
      });

      // Test với dữ liệu ít hơn để xem có khác biệt không
      console.log('\n🧪 Testing with different data lengths:');
      for (const testLength of [200, 300, 400, 500]) {
        if (klines.length >= testLength) {
          const testData = klines.slice(-testLength);
          const testIndicators = indicators.calculateAllIndicators(testData);
          if (testIndicators) {
            console.log(`📊 EMA200 with ${testLength} candles: ${testIndicators.ema200}`);
          }
        }
      }
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Chạy test
if (require.main === module) {
  test42USDTAnalysis().catch(console.error);
}

module.exports = test42USDTAnalysis;
